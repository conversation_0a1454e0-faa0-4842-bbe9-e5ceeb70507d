{"build": "h1361d0a_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["cuda-nvrtc", "cuda-version >=12,<13.0a0", "libcublas", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cudnn-********-h1361d0a_1", "files": ["Library/bin/cudnn64_9.dll", "Library/bin/cudnn_adv64_9.dll", "Library/bin/cudnn_cnn64_9.dll", "Library/bin/cudnn_engines_precompiled64_9.dll", "Library/bin/cudnn_engines_runtime_compiled64_9.dll", "Library/bin/cudnn_graph64_9.dll", "Library/bin/cudnn_heuristic64_9.dll", "Library/bin/cudnn_ops64_9.dll", "Library/include/cudnn.h", "Library/include/cudnn_adv.h", "Library/include/cudnn_backend.h", "Library/include/cudnn_cnn.h", "Library/include/cudnn_graph.h", "Library/include/cudnn_ops.h", "Library/include/cudnn_version.h", "Library/lib/cudnn.lib", "Library/lib/cudnn64_9.lib", "Library/lib/cudnn_adv.lib", "Library/lib/cudnn_adv64_9.lib", "Library/lib/cudnn_cnn.lib", "Library/lib/cudnn_cnn64_9.lib", "Library/lib/cudnn_engines_precompiled.lib", "Library/lib/cudnn_engines_precompiled64_9.lib", "Library/lib/cudnn_engines_runtime_compiled.lib", "Library/lib/cudnn_engines_runtime_compiled64_9.lib", "Library/lib/cudnn_graph.lib", "Library/lib/cudnn_graph64_9.lib", "Library/lib/cudnn_heuristic.lib", "Library/lib/cudnn_heuristic64_9.lib", "Library/lib/cudnn_ops.lib", "Library/lib/cudnn_ops64_9.lib", "Scripts/.cudnn-post-link.bat"], "fn": "cudnn-********-h1361d0a_1.conda", "license": "LicenseRef-cuDNN-Software-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cudnn-********-h1361d0a_1", "type": 1}, "md5": "51d9ca9954df0c51ca47cdd6fb3471cf", "name": "cudnn", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cudnn-********-h1361d0a_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/cudnn64_9.dll", "path_type": "hardlink", "sha256": "0edb24306db36121137a3150ac55015aee174d4582d5b807bd3220180a7a57db", "sha256_in_prefix": "0edb24306db36121137a3150ac55015aee174d4582d5b807bd3220180a7a57db", "size_in_bytes": 265760}, {"_path": "Library/bin/cudnn_adv64_9.dll", "path_type": "hardlink", "sha256": "631af1130b27b4d14683c7da690dddec8e25ffe3097d498fbba7129faba1601c", "sha256_in_prefix": "631af1130b27b4d14683c7da690dddec8e25ffe3097d498fbba7129faba1601c", "size_in_bytes": 253583928}, {"_path": "Library/bin/cudnn_cnn64_9.dll", "path_type": "hardlink", "sha256": "68c9f6bf98a5620a0bb0519c3997800ef2c7cb8979f17a60afcf82d341629c73", "sha256_in_prefix": "68c9f6bf98a5620a0bb0519c3997800ef2c7cb8979f17a60afcf82d341629c73", "size_in_bytes": 4262456}, {"_path": "Library/bin/cudnn_engines_precompiled64_9.dll", "path_type": "hardlink", "sha256": "59e71b3adae62186d3336ccbda6d0c8b14a3a0ef21694757e2b32e84fa840152", "sha256_in_prefix": "59e71b3adae62186d3336ccbda6d0c8b14a3a0ef21694757e2b32e84fa840152", "size_in_bytes": 548927544}, {"_path": "Library/bin/cudnn_engines_runtime_compiled64_9.dll", "path_type": "hardlink", "sha256": "8671c24fc0debbd99c3dca57bfdf657f4c9490e2a009f641cfc2fcf7a1256180", "sha256_in_prefix": "8671c24fc0debbd99c3dca57bfdf657f4c9490e2a009f641cfc2fcf7a1256180", "size_in_bytes": 25217568}, {"_path": "Library/bin/cudnn_graph64_9.dll", "path_type": "hardlink", "sha256": "22c21907fb2108259cb25738156f4459c12c0a001715bbb6d5aafac150060e85", "sha256_in_prefix": "22c21907fb2108259cb25738156f4459c12c0a001715bbb6d5aafac150060e85", "size_in_bytes": 2239520}, {"_path": "Library/bin/cudnn_heuristic64_9.dll", "path_type": "hardlink", "sha256": "5378e50729665245fa5abe89c729b782de686675e9bd6319cda1a3165d9492e6", "sha256_in_prefix": "5378e50729665245fa5abe89c729b782de686675e9bd6319cda1a3165d9492e6", "size_in_bytes": 55919648}, {"_path": "Library/bin/cudnn_ops64_9.dll", "path_type": "hardlink", "sha256": "080c2bb37e9d1fdd585cdd53fdbb23c0478b99610a19f8bba512b596b1e302fe", "sha256_in_prefix": "080c2bb37e9d1fdd585cdd53fdbb23c0478b99610a19f8bba512b596b1e302fe", "size_in_bytes": 116958752}, {"_path": "Library/include/cudnn.h", "path_type": "hardlink", "sha256": "2ddcf445c5c6656886bb78641e18fdfdeef96cda7ff3b7f1ac4f5da72b4d39e7", "sha256_in_prefix": "2ddcf445c5c6656886bb78641e18fdfdeef96cda7ff3b7f1ac4f5da72b4d39e7", "size_in_bytes": 2909}, {"_path": "Library/include/cudnn_adv.h", "path_type": "hardlink", "sha256": "f50b1ec3e5da5dba5390bee0ed4152dc345f43b8fcdec23db490d341c8387264", "sha256_in_prefix": "f50b1ec3e5da5dba5390bee0ed4152dc345f43b8fcdec23db490d341c8387264", "size_in_bytes": 31523}, {"_path": "Library/include/cudnn_backend.h", "path_type": "hardlink", "sha256": "da1e85ac81029ab64a42ccafa30a699785e2361be28a83e02a5ca8c23d55fd9d", "sha256_in_prefix": "da1e85ac81029ab64a42ccafa30a699785e2361be28a83e02a5ca8c23d55fd9d", "size_in_bytes": 2811}, {"_path": "Library/include/cudnn_cnn.h", "path_type": "hardlink", "sha256": "81a28610eb265f801886f01db3aa5d29f279a77b9f087ac2b0ecb852100aa30d", "sha256_in_prefix": "81a28610eb265f801886f01db3aa5d29f279a77b9f087ac2b0ecb852100aa30d", "size_in_bytes": 37392}, {"_path": "Library/include/cudnn_graph.h", "path_type": "hardlink", "sha256": "b225a1756fd59b42e6553033ebc5cde9e6a61c4730052971b08fd0c68821cc94", "sha256_in_prefix": "b225a1756fd59b42e6553033ebc5cde9e6a61c4730052971b08fd0c68821cc94", "size_in_bytes": 42305}, {"_path": "Library/include/cudnn_ops.h", "path_type": "hardlink", "sha256": "44d33811e0578b7c56c0860b9870000c33f813bfc117174a4c6ce47ba574ed30", "sha256_in_prefix": "44d33811e0578b7c56c0860b9870000c33f813bfc117174a4c6ce47ba574ed30", "size_in_bytes": 64950}, {"_path": "Library/include/cudnn_version.h", "path_type": "hardlink", "sha256": "683b4a6c847edea351424676261cc016047c5f0a2468d401f0640dacab68118f", "sha256_in_prefix": "683b4a6c847edea351424676261cc016047c5f0a2468d401f0640dacab68118f", "size_in_bytes": 3183}, {"_path": "Library/lib/cudnn.lib", "path_type": "hardlink", "sha256": "6924171df255cc1519facd31fdc7ff8c285d20ad6b7f4be96295704b9c4ca8d2", "sha256_in_prefix": "6924171df255cc1519facd31fdc7ff8c285d20ad6b7f4be96295704b9c4ca8d2", "size_in_bytes": 61694}, {"_path": "Library/lib/cudnn64_9.lib", "path_type": "hardlink", "sha256": "6924171df255cc1519facd31fdc7ff8c285d20ad6b7f4be96295704b9c4ca8d2", "sha256_in_prefix": "6924171df255cc1519facd31fdc7ff8c285d20ad6b7f4be96295704b9c4ca8d2", "size_in_bytes": 61694}, {"_path": "Library/lib/cudnn_adv.lib", "path_type": "hardlink", "sha256": "9da688af86adb78a329fd65969f6dc203cd235c870a553fdbd0c025d463f7b3c", "sha256_in_prefix": "9da688af86adb78a329fd65969f6dc203cd235c870a553fdbd0c025d463f7b3c", "size_in_bytes": 65182}, {"_path": "Library/lib/cudnn_adv64_9.lib", "path_type": "hardlink", "sha256": "9da688af86adb78a329fd65969f6dc203cd235c870a553fdbd0c025d463f7b3c", "sha256_in_prefix": "9da688af86adb78a329fd65969f6dc203cd235c870a553fdbd0c025d463f7b3c", "size_in_bytes": 65182}, {"_path": "Library/lib/cudnn_cnn.lib", "path_type": "hardlink", "sha256": "0d1febb5bc22be7b5f691bbc83e34137e25b92450d14b1a841f96eba630a8f20", "sha256_in_prefix": "0d1febb5bc22be7b5f691bbc83e34137e25b92450d14b1a841f96eba630a8f20", "size_in_bytes": 20946}, {"_path": "Library/lib/cudnn_cnn64_9.lib", "path_type": "hardlink", "sha256": "0d1febb5bc22be7b5f691bbc83e34137e25b92450d14b1a841f96eba630a8f20", "sha256_in_prefix": "0d1febb5bc22be7b5f691bbc83e34137e25b92450d14b1a841f96eba630a8f20", "size_in_bytes": 20946}, {"_path": "Library/lib/cudnn_engines_precompiled.lib", "path_type": "hardlink", "sha256": "fd4d956092589df20476a56b861447cd849801d5a918ad8249319954f1039634", "sha256_in_prefix": "fd4d956092589df20476a56b861447cd849801d5a918ad8249319954f1039634", "size_in_bytes": 488198}, {"_path": "Library/lib/cudnn_engines_precompiled64_9.lib", "path_type": "hardlink", "sha256": "fd4d956092589df20476a56b861447cd849801d5a918ad8249319954f1039634", "sha256_in_prefix": "fd4d956092589df20476a56b861447cd849801d5a918ad8249319954f1039634", "size_in_bytes": 488198}, {"_path": "Library/lib/cudnn_engines_runtime_compiled.lib", "path_type": "hardlink", "sha256": "8e65b8d14ab72e93abd74aaa960c0180c6d041f982d4087f2a4d9d423007f1aa", "sha256_in_prefix": "8e65b8d14ab72e93abd74aaa960c0180c6d041f982d4087f2a4d9d423007f1aa", "size_in_bytes": 6240}, {"_path": "Library/lib/cudnn_engines_runtime_compiled64_9.lib", "path_type": "hardlink", "sha256": "8e65b8d14ab72e93abd74aaa960c0180c6d041f982d4087f2a4d9d423007f1aa", "sha256_in_prefix": "8e65b8d14ab72e93abd74aaa960c0180c6d041f982d4087f2a4d9d423007f1aa", "size_in_bytes": 6240}, {"_path": "Library/lib/cudnn_graph.lib", "path_type": "hardlink", "sha256": "7a3c7e59ebed822297e721d8fb3404783eb5b319f07ad73cdae6c61e4a4ae35e", "sha256_in_prefix": "7a3c7e59ebed822297e721d8fb3404783eb5b319f07ad73cdae6c61e4a4ae35e", "size_in_bytes": 984412}, {"_path": "Library/lib/cudnn_graph64_9.lib", "path_type": "hardlink", "sha256": "7a3c7e59ebed822297e721d8fb3404783eb5b319f07ad73cdae6c61e4a4ae35e", "sha256_in_prefix": "7a3c7e59ebed822297e721d8fb3404783eb5b319f07ad73cdae6c61e4a4ae35e", "size_in_bytes": 984412}, {"_path": "Library/lib/cudnn_heuristic.lib", "path_type": "hardlink", "sha256": "df500a6018ddc349297236d2fb8a0e366a52219b5c451350c0664dc3cd5f90c0", "sha256_in_prefix": "df500a6018ddc349297236d2fb8a0e366a52219b5c451350c0664dc3cd5f90c0", "size_in_bytes": 4436}, {"_path": "Library/lib/cudnn_heuristic64_9.lib", "path_type": "hardlink", "sha256": "df500a6018ddc349297236d2fb8a0e366a52219b5c451350c0664dc3cd5f90c0", "sha256_in_prefix": "df500a6018ddc349297236d2fb8a0e366a52219b5c451350c0664dc3cd5f90c0", "size_in_bytes": 4436}, {"_path": "Library/lib/cudnn_ops.lib", "path_type": "hardlink", "sha256": "af241bd94cd1c021390970894c1a8f9456cbef9e9559649795a6d762d9dcca60", "sha256_in_prefix": "af241bd94cd1c021390970894c1a8f9456cbef9e9559649795a6d762d9dcca60", "size_in_bytes": 45720}, {"_path": "Library/lib/cudnn_ops64_9.lib", "path_type": "hardlink", "sha256": "af241bd94cd1c021390970894c1a8f9456cbef9e9559649795a6d762d9dcca60", "sha256_in_prefix": "af241bd94cd1c021390970894c1a8f9456cbef9e9559649795a6d762d9dcca60", "size_in_bytes": 45720}, {"_path": "Scripts/.cudnn-post-link.bat", "path_type": "hardlink", "sha256": "51d209aa7a353b7090eff367658956cb3b634ab9e470f161294f72c8231f8862", "sha256_in_prefix": "51d209aa7a353b7090eff367658956cb3b634ab9e470f161294f72c8231f8862", "size_in_bytes": 208}], "paths_version": 1}, "requested_spec": "cudnn", "sha256": "e3920c063eefba471a25c5b15d5512d376b08d3b466c400bd026f7ca6b2624de", "size": 472001149, "subdir": "win-64", "timestamp": 1743628434000, "url": "https://conda.anaconda.org/conda-forge/win-64/cudnn-********-h1361d0a_1.conda", "version": "********"}