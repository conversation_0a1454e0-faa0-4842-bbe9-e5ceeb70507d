{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-cudart-dev", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-profiler-api-12.8.90-0", "files": ["Library/include/cudaProfiler.h", "Library/include/cuda_profiler_api.h"], "fn": "cuda-profiler-api-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-profiler-api-12.8.90-0", "type": 1}, "md5": "305fafffd0c34e9520eff869dc553c7a", "name": "cuda-profiler-api", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-profiler-api-12.8.90-0.conda", "paths_data": {"paths": [{"_path": "Library/include/cudaProfiler.h", "path_type": "hardlink", "sha256": "cb1eb61ea355aac7bd0ba4a4baef987cfd233b1309227fe3b88bb37b58eedadc", "sha256_in_prefix": "cb1eb61ea355aac7bd0ba4a4baef987cfd233b1309227fe3b88bb37b58eedadc", "size_in_bytes": 7235}, {"_path": "Library/include/cuda_profiler_api.h", "path_type": "hardlink", "sha256": "c8973678f0c821e7a3938e0ce37fd6af49c3b4995b215b204c1959002f558322", "sha256_in_prefix": "c8973678f0c821e7a3938e0ce37fd6af49c3b4995b215b204c1959002f558322", "size_in_bytes": 4700}], "paths_version": 1}, "requested_spec": "None", "sha256": "50a6fa4c795181466b24e13ba26375cea84c22722b663279f285f7ab57dad877", "size": 19468, "subdir": "win-64", "timestamp": 1739448360000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-profiler-api-12.8.90-0.conda", "version": "12.8.90"}