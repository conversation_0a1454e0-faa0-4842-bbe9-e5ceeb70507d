==> 2025-04-25 14:15:02 <==
# cmd: D:\anaconda3\Scripts\conda-script.py create -n py310 python=3.10
# conda version: 24.9.2
+defaults/noarch::tzdata-2025a-h04d1e81_0
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::ca-certificates-2025.2.25-haa95532_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::openssl-3.0.16-h3f729d1_0
+defaults/win-64::pip-25.0-py310haa95532_0
+defaults/win-64::python-3.10.16-h4607a30_1
+defaults/win-64::setuptools-75.8.0-py310haa95532_0
+defaults/win-64::sqlite-3.45.3-h2bbff1b_0
+defaults/win-64::tk-8.6.14-h0416ee5_0
+defaults/win-64::vc-14.42-haa95532_5
+defaults/win-64::vs2015_runtime-14.42.34433-hbfb602d_5
+defaults/win-64::wheel-0.45.1-py310haa95532_0
+defaults/win-64::xz-5.6.4-h4754444_1
+defaults/win-64::zlib-1.2.13-h8cc25b3_1
# update specs: ['python=3.10']
==> 2025-04-25 14:16:09 <==
# cmd: D:\anaconda3\Scripts\conda-script.py install nvidia/label/cuda-12.8.1::cuda-toolkit
# conda version: 24.9.2
+defaults/win-64::expat-2.7.1-h8ddb27b_0
+defaults/win-64::fontconfig-2.14.1-hb33846d_3
+defaults/win-64::freetype-2.13.3-h0620614_0
+defaults/win-64::khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0
+defaults/win-64::libglib-2.78.4-ha17d25a_0
+defaults/win-64::libiconv-1.16-h2bbff1b_3
+defaults/win-64::libpng-1.6.39-h8cc25b3_0
+defaults/win-64::libxml2-2.13.7-h866ff63_0
+defaults/win-64::pcre2-10.42-h0ff8eda_1
+defaults/win-64::vs2017_win-64-19.16.27032.1-hb4161e2_3
+defaults/win-64::vswhere-2.8.4-haa95532_0
+nvidia/label/cuda-12.8.1/noarch::cuda-compiler-12.8.1-0
+nvidia/label/cuda-12.8.1/noarch::cuda-crt-dev_win-64-12.8.93-0
+nvidia/label/cuda-12.8.1/noarch::cuda-cudart-dev_win-64-12.8.90-0
+nvidia/label/cuda-12.8.1/noarch::cuda-cudart-static_win-64-12.8.90-0
+nvidia/label/cuda-12.8.1/noarch::cuda-cudart_win-64-12.8.90-0
+nvidia/label/cuda-12.8.1/noarch::cuda-nvcc-dev_win-64-12.8.93-0
+nvidia/label/cuda-12.8.1/noarch::cuda-nvvm-dev_win-64-12.8.93-0
+nvidia/label/cuda-12.8.1/noarch::cuda-toolkit-12.8.1-0
+nvidia/label/cuda-12.8.1/noarch::cuda-version-12.8-3
+nvidia/label/cuda-12.8.1/win-64::cuda-cccl_win-64-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-command-line-tools-12.8.1-0
+nvidia/label/cuda-12.8.1/win-64::cuda-crt-tools-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-cudart-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-cudart-dev-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-cudart-static-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-cuobjdump-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-cupti-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-cupti-dev-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-cuxxfilt-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-libraries-12.8.1-0
+nvidia/label/cuda-12.8.1/win-64::cuda-libraries-dev-12.8.1-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvcc-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvcc-impl-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvcc-tools-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvcc_win-64-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvdisasm-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvml-dev-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvprof-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvprune-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvrtc-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvrtc-dev-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvvm-impl-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvvm-tools-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-nvvp-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-opencl-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-opencl-dev-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-profiler-api-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::cuda-sanitizer-api-12.8.93-0
+nvidia/label/cuda-12.8.1/win-64::cuda-tools-12.8.1-0
+nvidia/label/cuda-12.8.1/win-64::cuda-visual-tools-12.8.1-0
+nvidia/label/cuda-12.8.1/win-64::libcublas-12.8.4.1-0
+nvidia/label/cuda-12.8.1/win-64::libcublas-dev-12.8.4.1-0
+nvidia/label/cuda-12.8.1/win-64::libcufft-11.3.3.83-0
+nvidia/label/cuda-12.8.1/win-64::libcufft-dev-11.3.3.83-0
+nvidia/label/cuda-12.8.1/win-64::libcurand-10.3.9.90-0
+nvidia/label/cuda-12.8.1/win-64::libcurand-dev-10.3.9.90-0
+nvidia/label/cuda-12.8.1/win-64::libcusolver-11.7.3.90-0
+nvidia/label/cuda-12.8.1/win-64::libcusolver-dev-11.7.3.90-0
+nvidia/label/cuda-12.8.1/win-64::libcusparse-12.5.8.93-0
+nvidia/label/cuda-12.8.1/win-64::libcusparse-dev-12.5.8.93-0
+nvidia/label/cuda-12.8.1/win-64::libnpp-12.3.3.100-0
+nvidia/label/cuda-12.8.1/win-64::libnpp-dev-12.3.3.100-0
+nvidia/label/cuda-12.8.1/win-64::libnvfatbin-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::libnvfatbin-dev-12.8.90-0
+nvidia/label/cuda-12.8.1/win-64::libnvjitlink-12.8.93-1
+nvidia/label/cuda-12.8.1/win-64::libnvjitlink-dev-12.8.93-1
+nvidia/label/cuda-12.8.1/win-64::libnvjpeg-*********-0
+nvidia/label/cuda-12.8.1/win-64::libnvjpeg-dev-*********-0
+nvidia/label/cuda-12.8.1/win-64::nsight-compute-2025.1.1.2-0
# update specs: ['nvidia/label/cuda-12.8.1::cuda-toolkit']
==> 2025-04-25 14:17:24 <==
# cmd: D:\anaconda3\Scripts\conda-script.py install -c conda-forge cudnn
# conda version: 24.9.2
-defaults/win-64::openssl-3.0.16-h3f729d1_0
-defaults/win-64::vs2015_runtime-14.42.34433-hbfb602d_5
+conda-forge/win-64::cudnn-********-h1361d0a_1
+conda-forge/win-64::openssl-3.5.0-ha4e3fda_0
+conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1
+conda-forge/win-64::vc14_runtime-14.42.34438-hfd919c2_26
+conda-forge/win-64::vs2015_runtime-14.42.34438-h7142326_26
# update specs: ['cudnn']
