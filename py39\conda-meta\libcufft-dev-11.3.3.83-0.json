{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "libcufft 11.3.3.83 0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcufft-dev-11.3.3.83-0", "files": ["Library/include/cudalibxt.h", "Library/include/cufft.h", "Library/include/cufftXt.h", "Library/include/cufftw.h", "Library/lib/cufft.lib", "Library/lib/cufftw.lib"], "fn": "libcufft-dev-11.3.3.83-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcufft-dev-11.3.3.83-0", "type": 1}, "md5": "8237084072e5bc33d3a5ef7ff7aa9e23", "name": "libcufft-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcufft-dev-11.3.3.83-0.conda", "paths_data": {"paths": [{"_path": "Library/include/cudalibxt.h", "path_type": "hardlink", "sha256": "a1680320630e760cb28223be86f720178b1b9777f1d53638665ab1eebb274591", "sha256_in_prefix": "a1680320630e760cb28223be86f720178b1b9777f1d53638665ab1eebb274591", "size_in_bytes": 4202}, {"_path": "Library/include/cufft.h", "path_type": "hardlink", "sha256": "52ddefdc46dedc05b678528315e64a0763d841caf0baff9e4d2f029049eda30e", "sha256_in_prefix": "52ddefdc46dedc05b678528315e64a0763d841caf0baff9e4d2f029049eda30e", "size_in_bytes": 13504}, {"_path": "Library/include/cufftXt.h", "path_type": "hardlink", "sha256": "420eb4ceca7e603c4a483b681d70859518e7e2a869faf09c696312bdc31cdaa7", "sha256_in_prefix": "420eb4ceca7e603c4a483b681d70859518e7e2a869faf09c696312bdc31cdaa7", "size_in_bytes": 13223}, {"_path": "Library/include/cufftw.h", "path_type": "hardlink", "sha256": "64afcc68790a4e46a5b2556ead6502f66ca23ad45ea88ee5b36865b4cf4d335a", "sha256_in_prefix": "64afcc68790a4e46a5b2556ead6502f66ca23ad45ea88ee5b36865b4cf4d335a", "size_in_bytes": 20516}, {"_path": "Library/lib/cufft.lib", "path_type": "hardlink", "sha256": "5162e64f33318df3e6d3ee4a98262745613ba884ecacb6a9bc2e34ca17a1782b", "sha256_in_prefix": "5162e64f33318df3e6d3ee4a98262745613ba884ecacb6a9bc2e34ca17a1782b", "size_in_bytes": 14122}, {"_path": "Library/lib/cufftw.lib", "path_type": "hardlink", "sha256": "9df7d590730748bda4a87d8bc9475851bf8028f50fbe7a6be1b2866aa41e5d49", "sha256_in_prefix": "9df7d590730748bda4a87d8bc9475851bf8028f50fbe7a6be1b2866aa41e5d49", "size_in_bytes": 17120}], "paths_version": 1}, "requested_spec": "None", "sha256": "91f2e87c4e9e3fa79e0df4f34642f7e4615bf8e59e2544ad9bc7ca9548031cd6", "size": 27708, "subdir": "win-64", "timestamp": 1741046578000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/libcufft-dev-11.3.3.83-0.conda", "version": "11.3.3.83"}