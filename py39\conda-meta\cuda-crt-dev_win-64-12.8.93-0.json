{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/noarch", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-crt-dev_win-64-12.8.93-0", "files": ["Library/include/crt/common_functions.h", "Library/include/crt/cudacc_ext.h", "Library/include/crt/device_double_functions.h", "Library/include/crt/device_double_functions.hpp", "Library/include/crt/device_fp128_functions.h", "Library/include/crt/device_functions.h", "Library/include/crt/device_functions.hpp", "Library/include/crt/func_macro.h", "Library/include/crt/host_config.h", "Library/include/crt/host_defines.h", "Library/include/crt/host_runtime.h", "Library/include/crt/math_functions.h", "Library/include/crt/math_functions.hpp", "Library/include/crt/mma.h", "Library/include/crt/mma.hpp", "Library/include/crt/nvfunctional", "Library/include/crt/sm_100_rt.h", "Library/include/crt/sm_100_rt.hpp", "Library/include/crt/sm_70_rt.h", "Library/include/crt/sm_70_rt.hpp", "Library/include/crt/sm_80_rt.h", "Library/include/crt/sm_80_rt.hpp", "Library/include/crt/sm_90_rt.h", "Library/include/crt/sm_90_rt.hpp", "Library/include/crt/storage_class.h"], "fn": "cuda-crt-dev_win-64-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-crt-dev_win-64-12.8.93-0", "type": 1}, "md5": "af087c9bff57e1157857a93f8566005c", "name": "cuda-crt-dev_win-64", "noarch": "generic", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-crt-dev_win-64-12.8.93-0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/include/crt/common_functions.h", "path_type": "hardlink", "sha256": "8123e7afb0ddc439d531a25a1bc59bfb4117ffc8648199f8c32931910b81e3db", "sha256_in_prefix": "8123e7afb0ddc439d531a25a1bc59bfb4117ffc8648199f8c32931910b81e3db", "size_in_bytes": 13869}, {"_path": "Library/include/crt/cudacc_ext.h", "path_type": "hardlink", "sha256": "10b085d6b1fcbe650a75de7455e0f5d38d1be221bacddb3318144b88bc72c5a5", "sha256_in_prefix": "10b085d6b1fcbe650a75de7455e0f5d38d1be221bacddb3318144b88bc72c5a5", "size_in_bytes": 3288}, {"_path": "Library/include/crt/device_double_functions.h", "path_type": "hardlink", "sha256": "72329723f14175d3fcc6e4949b5b32db05b5de804097c2bbbbc94a7264a7dcde", "sha256_in_prefix": "72329723f14175d3fcc6e4949b5b32db05b5de804097c2bbbbc94a7264a7dcde", "size_in_bytes": 41130}, {"_path": "Library/include/crt/device_double_functions.hpp", "path_type": "hardlink", "sha256": "80d741d9fa10a3c8a212f2e9e8bc23306dc30f88d4b2a6016491f872386dc760", "sha256_in_prefix": "80d741d9fa10a3c8a212f2e9e8bc23306dc30f88d4b2a6016491f872386dc760", "size_in_bytes": 8765}, {"_path": "Library/include/crt/device_fp128_functions.h", "path_type": "hardlink", "sha256": "a0e64a7040f151c8bca4ddc4df7c707eb481922d4e25874e9c330fbe3106054e", "sha256_in_prefix": "a0e64a7040f151c8bca4ddc4df7c707eb481922d4e25874e9c330fbe3106054e", "size_in_bytes": 52264}, {"_path": "Library/include/crt/device_functions.h", "path_type": "hardlink", "sha256": "466feed76fc2700fe7d134c31dc85097a8de688a49c6803590cceda772e50615", "sha256_in_prefix": "466feed76fc2700fe7d134c31dc85097a8de688a49c6803590cceda772e50615", "size_in_bytes": 140912}, {"_path": "Library/include/crt/device_functions.hpp", "path_type": "hardlink", "sha256": "ae5e39349999c0d4b912d9ff3cb5ddbe329af3fb4a58dcf5ff1eeaf0d332f466", "sha256_in_prefix": "ae5e39349999c0d4b912d9ff3cb5ddbe329af3fb4a58dcf5ff1eeaf0d332f466", "size_in_bytes": 39154}, {"_path": "Library/include/crt/func_macro.h", "path_type": "hardlink", "sha256": "1bb685c45385895321df70d19248a734e13225f4fb5efbfc28fcd21a93b5930d", "sha256_in_prefix": "1bb685c45385895321df70d19248a734e13225f4fb5efbfc28fcd21a93b5930d", "size_in_bytes": 1812}, {"_path": "Library/include/crt/host_config.h", "path_type": "hardlink", "sha256": "423209b1f5fda31fc3105ecba0db889a7371a5fdc6fbb825a1df7008ec3fd5d7", "sha256_in_prefix": "423209b1f5fda31fc3105ecba0db889a7371a5fdc6fbb825a1df7008ec3fd5d7", "size_in_bytes": 12479}, {"_path": "Library/include/crt/host_defines.h", "path_type": "hardlink", "sha256": "086310aafabe0270fc1230cd903d025501617b7fcc810b7589c53f3d82fff8f5", "sha256_in_prefix": "086310aafabe0270fc1230cd903d025501617b7fcc810b7589c53f3d82fff8f5", "size_in_bytes": 10385}, {"_path": "Library/include/crt/host_runtime.h", "path_type": "hardlink", "sha256": "f6dcef2febf6ab6f47ce29067fd2ab4e257e7ec9c54bcc3b0dadfb7e588b0bce", "sha256_in_prefix": "f6dcef2febf6ab6f47ce29067fd2ab4e257e7ec9c54bcc3b0dadfb7e588b0bce", "size_in_bytes": 10590}, {"_path": "Library/include/crt/math_functions.h", "path_type": "hardlink", "sha256": "0e15c08685cf62fc03764146db4b6693aa456d8a2e71b124a7937b8d9833cf4e", "sha256_in_prefix": "0e15c08685cf62fc03764146db4b6693aa456d8a2e71b124a7937b8d9833cf4e", "size_in_bytes": 244541}, {"_path": "Library/include/crt/math_functions.hpp", "path_type": "hardlink", "sha256": "850897350fcf6abbd00d58b312774aa12098a1674b5327fbbca9018e76cb1b88", "sha256_in_prefix": "850897350fcf6abbd00d58b312774aa12098a1674b5327fbbca9018e76cb1b88", "size_in_bytes": 103605}, {"_path": "Library/include/crt/mma.h", "path_type": "hardlink", "sha256": "adbb71ca5b4c88cf4acfff6d78dd60e29a2e684cfd4b278f57b4a536184303da", "sha256_in_prefix": "adbb71ca5b4c88cf4acfff6d78dd60e29a2e684cfd4b278f57b4a536184303da", "size_in_bytes": 63456}, {"_path": "Library/include/crt/mma.hpp", "path_type": "hardlink", "sha256": "6f0b9c7425503672edc809b9d1584fe7d80219889375c6da8c15eca69812ac5b", "sha256_in_prefix": "6f0b9c7425503672edc809b9d1584fe7d80219889375c6da8c15eca69812ac5b", "size_in_bytes": 67727}, {"_path": "Library/include/crt/nvfunctional", "path_type": "hardlink", "sha256": "f6bd186addac0bcbe6dfeab969ab3cbd744c2553d3f6287739e72b04f207bfd8", "sha256_in_prefix": "f6bd186addac0bcbe6dfeab969ab3cbd744c2553d3f6287739e72b04f207bfd8", "size_in_bytes": 17521}, {"_path": "Library/include/crt/sm_100_rt.h", "path_type": "hardlink", "sha256": "22b83a565d4d780f0af426c498f25031c3bab0ae15205e36de6c91bbbb0e12f6", "sha256_in_prefix": "22b83a565d4d780f0af426c498f25031c3bab0ae15205e36de6c91bbbb0e12f6", "size_in_bytes": 9239}, {"_path": "Library/include/crt/sm_100_rt.hpp", "path_type": "hardlink", "sha256": "bf539574a1bad68e96fe7f4d15c59f3ef0917779c22a15966c025cbec6bcc0b9", "sha256_in_prefix": "bf539574a1bad68e96fe7f4d15c59f3ef0917779c22a15966c025cbec6bcc0b9", "size_in_bytes": 7012}, {"_path": "Library/include/crt/sm_70_rt.h", "path_type": "hardlink", "sha256": "11cfaa9a6987958ba1d573f9fe29b13041bf7fe471a8a245517d210f7d389084", "sha256_in_prefix": "11cfaa9a6987958ba1d573f9fe29b13041bf7fe471a8a245517d210f7d389084", "size_in_bytes": 6975}, {"_path": "Library/include/crt/sm_70_rt.hpp", "path_type": "hardlink", "sha256": "d0d13085e4671530e4f41f81ecf1d4e2101d2b3b3206aae7229b8e013ff55273", "sha256_in_prefix": "d0d13085e4671530e4f41f81ecf1d4e2101d2b3b3206aae7229b8e013ff55273", "size_in_bytes": 8029}, {"_path": "Library/include/crt/sm_80_rt.h", "path_type": "hardlink", "sha256": "f0d3ed12f5419b910a11e6cf6ce79af7d47603be2698d87eee16af67c2a2862f", "sha256_in_prefix": "f0d3ed12f5419b910a11e6cf6ce79af7d47603be2698d87eee16af67c2a2862f", "size_in_bytes": 7907}, {"_path": "Library/include/crt/sm_80_rt.hpp", "path_type": "hardlink", "sha256": "9224440b554c8ecf1357af8f6559a834c26137c4277abaa5948d968a5b233acf", "sha256_in_prefix": "9224440b554c8ecf1357af8f6559a834c26137c4277abaa5948d968a5b233acf", "size_in_bytes": 6853}, {"_path": "Library/include/crt/sm_90_rt.h", "path_type": "hardlink", "sha256": "2443718459320cd1993ec6cf66c7d822fe8866bc9eade5a197ae084884beb3a3", "sha256_in_prefix": "2443718459320cd1993ec6cf66c7d822fe8866bc9eade5a197ae084884beb3a3", "size_in_bytes": 11727}, {"_path": "Library/include/crt/sm_90_rt.hpp", "path_type": "hardlink", "sha256": "461b2c314cf5f1d2bd1782f22fa30882f135c68c8615935eb5360eec39ddc6fc", "sha256_in_prefix": "461b2c314cf5f1d2bd1782f22fa30882f135c68c8615935eb5360eec39ddc6fc", "size_in_bytes": 9476}, {"_path": "Library/include/crt/storage_class.h", "path_type": "hardlink", "sha256": "14b8ddc03a85d579cbc0cb3f1f888c3affa82fad960a1ad01ae689ce6ca008db", "sha256_in_prefix": "14b8ddc03a85d579cbc0cb3f1f888c3affa82fad960a1ad01ae689ce6ca008db", "size_in_bytes": 4933}], "paths_version": 1}, "requested_spec": "None", "sha256": "6f833f5e9514753586de889399d95dc6dd7aae6078cf43d3d6bbfd9302f004a3", "size": 84405, "subdir": "noarch", "timestamp": 1740205151000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/noarch/cuda-crt-dev_win-64-12.8.93-0.conda", "version": "12.8.93"}