{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-cudart 12.8.90 0", "cuda-cudart-dev_win-64 12.8.90 0", "cuda-cudart-static 12.8.90 0", "cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart-dev-12.8.90-0", "files": [], "fn": "cuda-cudart-dev-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart-dev-12.8.90-0", "type": 1}, "md5": "fe37db2da7a783d71772ada44a8e1f63", "name": "cuda-cudart-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart-dev-12.8.90-0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "166ef83ba8191d4fc6702b413688ef92032779c9ac04cc905181eb4edce15c05", "size": 16923, "subdir": "win-64", "timestamp": 1739448599000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-cudart-dev-12.8.90-0.conda", "version": "12.8.90"}