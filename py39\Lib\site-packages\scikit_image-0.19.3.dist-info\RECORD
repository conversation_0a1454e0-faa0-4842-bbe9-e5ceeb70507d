../../Scripts/skivi.exe,sha256=MaaWd8sXlY-zIVPbEZOaCtS0bghvuzol1BuSpo2ihsY,108388
doc/ext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
doc/ext/__pycache__/__init__.cpython-310.pyc,,
doc/ext/__pycache__/doi_role.cpython-310.pyc,,
doc/ext/__pycache__/notebook_doc.cpython-310.pyc,,
doc/ext/__pycache__/plot2rst.cpython-310.pyc,,
doc/ext/doi_role.py,sha256=Tb05HBsi1XHWGGu8hhLt-2eb7KKW7pN6GY3FbzLAqBY,1837
doc/ext/notebook_doc.py,sha256=scNcfB8I1MEjV5Y_HKwE6JM5Whi6D4hDVh90VDmbz0c,2431
doc/ext/plot2rst.py,sha256=sJkR1W6hU5gR0vWRs8iEJ7Jmx1EuwFhYCpACrGWFqEY,21125
doc/ext/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
doc/ext/tests/__pycache__/__init__.cpython-310.pyc,,
doc/ext/tests/__pycache__/test_notebook_doc.cpython-310.pyc,,
doc/ext/tests/test_notebook_doc.py,sha256=qvVeDbli3MPHxjwuMknMo4d7tRONVi_9PaBfXVFLBdI,714
scikit_image-0.19.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scikit_image-0.19.3.dist-info/LICENSE.txt,sha256=D_CAUcPngkLQW1HEt9Z7SdRje08ZaJaQWyCuEI5Ax1c,4379
scikit_image-0.19.3.dist-info/METADATA,sha256=UkMd4R3molTHFzSMtM3v4KfnM_RQe8duP7q5PgTEmRM,7981
scikit_image-0.19.3.dist-info/RECORD,,
scikit_image-0.19.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scikit_image-0.19.3.dist-info/WHEEL,sha256=W26pYN7HLsBT1jrDSL9udgf_mdNKJmYmL23sIP-FcgM,102
scikit_image-0.19.3.dist-info/entry_points.txt,sha256=Ixz10arvw_h7dXDaMU_wCmC23cgQ0Sdx1AoOvQsKI6U,54
scikit_image-0.19.3.dist-info/top_level.txt,sha256=p_BPN6f7FOr-PYr2rEp__LYXwFY3FVwEdu6LCzQdLw8,12
skimage/__init__.py,sha256=gD4yotLp5gufnxEfZ1HNrMtgl1Mhmt37n6nrRbGKth4,6235
skimage/__pycache__/__init__.cpython-310.pyc,,
skimage/__pycache__/_build.cpython-310.pyc,,
skimage/__pycache__/conftest.cpython-310.pyc,,
skimage/__pycache__/setup.cpython-310.pyc,,
skimage/_build.py,sha256=KArQ_YmjPNIqUoO_3KsM9AUzdl9iOl0IWm_dkoRAclA,3248
skimage/_shared/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_shared/__pycache__/__init__.cpython-310.pyc,,
skimage/_shared/__pycache__/_dependency_checks.cpython-310.pyc,,
skimage/_shared/__pycache__/_geometry.cpython-310.pyc,,
skimage/_shared/__pycache__/_tempfile.cpython-310.pyc,,
skimage/_shared/__pycache__/_warnings.cpython-310.pyc,,
skimage/_shared/__pycache__/coord.cpython-310.pyc,,
skimage/_shared/__pycache__/filters.cpython-310.pyc,,
skimage/_shared/__pycache__/lazy.cpython-310.pyc,,
skimage/_shared/__pycache__/setup.cpython-310.pyc,,
skimage/_shared/__pycache__/tester.cpython-310.pyc,,
skimage/_shared/__pycache__/testing.cpython-310.pyc,,
skimage/_shared/__pycache__/utils.cpython-310.pyc,,
skimage/_shared/__pycache__/version_requirements.cpython-310.pyc,,
skimage/_shared/_dependency_checks.py,sha256=njMTbECtd-kg0DdYYqLBWEPXbl7_KQWGKt3hjZza55E,99
skimage/_shared/_geometry.py,sha256=WFCodFk5FLYxjPm0ZD0xJf8AaopAu2DfE_DG_rjVqX0,1544
skimage/_shared/_tempfile.py,sha256=GRDjbesul72rQTSZRvpRkC21arbNCxz9ThQNZ8HK07s,805
skimage/_shared/_warnings.py,sha256=q64LIkeMtSW0Gtzk_LZotFJ2CmsEfB7mqNW5mC07w7o,5340
skimage/_shared/coord.py,sha256=FHUO6niVjbBHedz2RHUXQIOm9Yg8el3MyvQ2h4FBUiU,4605
skimage/_shared/fast_exp.cp310-win_amd64.pyd,sha256=4JLcbGWng2X7MnDCXkZ-G-Zg3fSh2kxQw8zdvzvzWcc,47104
skimage/_shared/fast_exp.pxd,sha256=WveQvzzJDXm8Zh_VvK7X_wEhkBhyJmqNs45WS1ubVlw,431
skimage/_shared/fast_exp.pyx,sha256=-vtRR-RY_6Q1wPT4_phPbEDE2ksdxH_SWd4AbGcnIUs,205
skimage/_shared/filters.py,sha256=SQt4AOcZEEat7xPK59_rrbRDDcMtAsr0GmFFroSUqLg,5858
skimage/_shared/fused_numerics.pxd,sha256=Wuvfq9h9wDeGdQQuCYQZSmXvgh64UXauh8vt_0v8NJg,567
skimage/_shared/geometry.cp310-win_amd64.pyd,sha256=DLQxAnM5E3jSgTcT4VjoKcJj2Q2Jro-Kmk3rRwq_S4E,107008
skimage/_shared/geometry.pxd,sha256=pAux_8w38oQ51qekJ4pXVKGCJegF-rlQEziB6X6gZz8,468
skimage/_shared/geometry.pyx,sha256=ip0F9cNssRCrRfIBQWCgjQf6Y-szy7Qy3eiN7jlxA9Y,2838
skimage/_shared/interpolation.cp310-win_amd64.pyd,sha256=0s7_nT2gJCzyAOjQzCgzqQ6hP6AVVR7j-N2qcHYfvQg,26624
skimage/_shared/interpolation.pxd,sha256=8PaK_HyA91aflIsjdotmd7aoWhAagxD4oAN1xsmxmq8,12776
skimage/_shared/interpolation.pyx,sha256=99B8-pe7bMLxCntSsKznuZ-3M4wCxAVguJoKTJtqoB4,214
skimage/_shared/lazy.py,sha256=TWYbhujJbUI59MNdSSdcWAvIn-UcIbGDpLSVrwy-4eE,4043
skimage/_shared/setup.py,sha256=r53q-Ny2mvMhQclZxxNFUs1KLZ_vwNATkksU99CZkiY,1271
skimage/_shared/tester.py,sha256=UdIH_0uP09HkE0NfrPbSCpOpsVdWwNXUy0r-qotNelc,3657
skimage/_shared/testing.py,sha256=xMCEVmff7ueyN-dpFtr6g4gTbZdtfuN_Gn7Sa3DrE54,10883
skimage/_shared/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_shared/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_coord.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_fast_exp.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_geometry.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_interpolation.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_safe_as_int.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_testing.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_utils.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_version_requirements.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_warnings.cpython-310.pyc,,
skimage/_shared/tests/test_coord.py,sha256=wgyC4m4rg1qJLxHDuPnRInonofhsPrcBvBkuB8ArGwg,3348
skimage/_shared/tests/test_fast_exp.py,sha256=O2825WQfrQUDIpMiUMFYoU_LaIuoq81b5O4TvO0LxUA,521
skimage/_shared/tests/test_geometry.py,sha256=0wCXAF93K7wHUCTtNC-vD4ZR21JzhLA-Tq1q6urU_nI,2218
skimage/_shared/tests/test_interpolation.py,sha256=s9XSmB520IozNWXMixiQCYuHnACDe-BYyZnBzw_RUqQ,1163
skimage/_shared/tests/test_safe_as_int.py,sha256=ESOVVSU9RSXnnzWD_072gYrY-qQn3IVCNp9WKgV1qbU,1714
skimage/_shared/tests/test_testing.py,sha256=jhmlKVVukNkx7ic0AGEkTtw68VpoyF7C4brU-qwb7JQ,3091
skimage/_shared/tests/test_utils.py,sha256=PHSOhZd38R4oC4UU4dvFIxhGu-HFcBBQWxKQxOboods,10437
skimage/_shared/tests/test_version_requirements.py,sha256=UFOtlOjiE10TwKH1I6kfJPB1HHQHO4qNGBQdHCjjwIs,1117
skimage/_shared/tests/test_warnings.py,sha256=k1Vza58q2u6z5XMxouzgIa8Rb9P_HCRxbTR7qHAiDSM,1288
skimage/_shared/transform.cp310-win_amd64.pyd,sha256=tBqgRXZST81aBiDLvNWIUzu0WZV-cgoyE9-sAUKuQjo,103936
skimage/_shared/transform.pxd,sha256=x5sK8rgzLrG7Vrbrawoh6A9sCzjR6EyaGiuVgp6wAso,239
skimage/_shared/transform.pyx,sha256=gvOAb51VtDUMjVX2g6HAh1Hx2ZhtC2PzGntNN-hEDMk,1177
skimage/_shared/utils.py,sha256=fDuewcGkdhmFQEwT9eF1ZL17XRJ2IiY0F33z1ORhFMk,28358
skimage/_shared/version_requirements.py,sha256=zrVUmfRiICp_4rQBIMnRIAWtrF2bobcQjQin4vIPuEY,5790
skimage/color/__init__.py,sha256=XpyPpqNz5Sqf6j21R7pl6zvpmuZgMVN9-95j_Y_43h8,3888
skimage/color/__pycache__/__init__.cpython-310.pyc,,
skimage/color/__pycache__/adapt_rgb.cpython-310.pyc,,
skimage/color/__pycache__/colorconv.cpython-310.pyc,,
skimage/color/__pycache__/colorlabel.cpython-310.pyc,,
skimage/color/__pycache__/delta_e.cpython-310.pyc,,
skimage/color/__pycache__/rgb_colors.cpython-310.pyc,,
skimage/color/adapt_rgb.py,sha256=kFMXXcqXLxH_ONrX-LH3wzc2w0-857Sfnh2u0_tFCOM,2578
skimage/color/colorconv.py,sha256=EnFhhjtMP1egrJ8GUdsN2_vmrixPN5jZNOJSeCs0TAY,66260
skimage/color/colorlabel.py,sha256=wuc15qQbyJMRxZ0xPTrXuKnJLfuJFtlIT21YswdgkGw,10783
skimage/color/delta_e.py,sha256=VJDm8KuHb_rzYehluSCTL5BkMDIyjuyKXKKYNlElYZ0,13128
skimage/color/rgb_colors.py,sha256=Mb_hq6RzxvPxwDVG2v4-h-1eKfyPZ5lyyseyBNos-eU,4639
skimage/color/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/color/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/color/tests/__pycache__/test_adapt_rgb.cpython-310.pyc,,
skimage/color/tests/__pycache__/test_colorconv.cpython-310.pyc,,
skimage/color/tests/__pycache__/test_colorlabel.cpython-310.pyc,,
skimage/color/tests/__pycache__/test_delta_e.cpython-310.pyc,,
skimage/color/tests/test_adapt_rgb.py,sha256=Pv-6P7rFmvmdEZasKy9L5bYcUkemRXuU82PPJ6hXHyo,2843
skimage/color/tests/test_colorconv.py,sha256=BfrbW6a2wSxywSox-tjd3Yo0x0JhJEhPgzCqmjU9YQM,38298
skimage/color/tests/test_colorlabel.py,sha256=BhwqNJmjGdwgSKX-NTKh1MNYHx93KIvLykJczWUacHs,11589
skimage/color/tests/test_delta_e.py,sha256=tn13mCoI4tGrXVbSxmufAJ5DHR5sYEnoh96rqxSZeU0,7356
skimage/conftest.py,sha256=ARPdUrO3qBK1vv-DO97_7X0aJzvKPwfsnJj1kzkgHH8,366
skimage/data/README.txt,sha256=OgaFfwPYunbnMTytBBmgub0XaU6Zy_CljrLczYGzxro,289
skimage/data/__init__.py,sha256=exgxv7s_LghJRF1pxEOHxfGrNg1trQsx-ZYv48dV3h8,1041
skimage/data/__pycache__/__init__.cpython-310.pyc,,
skimage/data/__pycache__/_binary_blobs.cpython-310.pyc,,
skimage/data/__pycache__/_fetchers.cpython-310.pyc,,
skimage/data/__pycache__/_registry.cpython-310.pyc,,
skimage/data/__pycache__/setup.cpython-310.pyc,,
skimage/data/_binary_blobs.py,sha256=5znj7qzUglYAXnPxZB07ZJ_c7LpNCNflK2ktDUMHA2o,2458
skimage/data/_fetchers.py,sha256=p-XbY_FY2kCneM919VJE-RZtJxoTN4iOCszCDSzGPxc,40182
skimage/data/_registry.py,sha256=iCvUlrVvR_0Mda9l-tj3kc-c6MjYiCe59O3xgZmyhiA,15238
skimage/data/astronaut.png,sha256=iEMc2WU8zVOXQbVV-wpGthVYswHUEQQStbwotePqbLU,791555
skimage/data/brick.png,sha256=eWbK8yT2uoQxGNmPegd0bSL2o0NDCt0CM-yl9uqqj88,106634
skimage/data/camera.png,sha256=sHk9Kt2g-mromcA5iUgr_5pC09VpD8fjZI8nldcwwjo,139512
skimage/data/cell.png,sha256=jSOn-4H3zId80J8zA1f8f1lWUTBuhOFyUvbgobP2FRU,74183
skimage/data/chelsea.png,sha256=WWqh58uHXrefQ34xA4HSazOKgcLaI0OXBKc8RlHoxLs,240512
skimage/data/chessboard_GRAY.png,sha256=PlGHB3RRWvTQfYIL2IJzZMcIOb-bVzx0bkhQleiT35A,418
skimage/data/chessboard_RGB.png,sha256=GsAe_y1OUPTtpVot3s3CimV2YjpY16fvhFE8XMGaAzE,1127
skimage/data/clock_motion.png,sha256=8Ckiayi2QugBE9hmIumyFe4Geglm_q9eYGBKHgVzOVU,58784
skimage/data/coffee.png,sha256=zAL4yhiLFnx3WnEBtddn0ecXks92LDPW-hWkWZtajec,466706
skimage/data/coins.png,sha256=-Ndz_Jz6b02OWULcNNCgeI_K7SpP77vtCu9TmNfvTLo,75825
skimage/data/color.png,sha256=fS35k94rT6KnjgTl34BQ9JqcURqnXlmrO9VqycmK734,85584
skimage/data/grass.png,sha256=trYCJCaziTbEOkrAljXNeK8HTpD0L_qCJ6yLdFLTn4k,217893
skimage/data/gravel.png,sha256=xIYVtFG_HmBvvXLAqp-MwPBoq3ER732Tu5sPJYZEDBI,194247
skimage/data/horse.png,sha256=x_tgeJ_jlMSF-EIpHqOyHlDRQPOdbctfuZF8wXgiVFU,16633
skimage/data/hubble_deep_field.jpg,sha256=OhnF3YqSepM0uxIpptY3EbHAx2f7J-IobnyEo-LC9fQ,527940
skimage/data/ihc.png,sha256=-N0ao4fd0fSditE7UJIbI3346bJiYG0lh3Boew75PO8,477916
skimage/data/lbpcascade_frontalface_opencv.xml,sha256=Awl3iaPcuw5A0gue-CU328O2cLan8iaNc1Rw8i4AOpE,51858
skimage/data/lfw_subset.npy,sha256=lWDsL17frAGXP2OoqZ0ABT_s0R4hh34YA4--UA-Ohyw,1000080
skimage/data/logo.png,sha256=8sV_6K8Inwi1ulI9lVc8JuYpBKxZZ_TIhRsn0DNpAWg,179723
skimage/data/microaneurysms.png,sha256=oeG-WapEf4zggvf6gJmXqzaaKxN8tsQgKrxkfHzPZFY,4950
skimage/data/moon.png,sha256=eHOWGdEffrnBZbtdLv1Hcs7lV4EuyEdTLbsdku9x9Xc,50177
skimage/data/motorcycle_disp.npz,sha256=LknIzr_z-iA1mgzGiAyC4cA7uxBtqBoXchgoG8LxE9c,1146173
skimage/data/motorcycle_left.png,sha256=2xjpxBV2F0A8NTemujVd_q_pp-q7a5uUyzP2Ul3UkXk,644701
skimage/data/motorcycle_right.png,sha256=X8kTrocOQqS2YjFLyQTReGvK2OLwubZ9uloilAY1d5c,640373
skimage/data/page.png,sha256=NBpvCmFVdmKwJzSptuVuwzqRWyxBiGuXUJ3t8qQ7R6M,47679
skimage/data/phantom.png,sha256=VS_2mBZ6pALM6xeYETBgeiKKCgqnxRkpnqpNXzAbo2w,3386
skimage/data/retina.jpg,sha256=OKB_NvJ_CV6Biup7ltNCAsBRdtMCU8ZnM_LgA3np4OY,269564
skimage/data/rocket.jpg,sha256=wt0N58U4340RHkeWGbEpRk0CadCuX9GMqR0zp_3-qVw,112525
skimage/data/setup.py,sha256=OsBdkgiCec-msw3_F1nuOoQQk8ND6gg3bZJqPscHzaQ,1150
skimage/data/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/data/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/data/tests/__pycache__/test_data.cpython-310.pyc,,
skimage/data/tests/test_data.py,sha256=VzaXasHWSzfpm0rUKfYq6JzNNS_J-CcLK95jPod0HAs,5855
skimage/data/text.png,sha256=vYSqOm48mIeFDUXWBslrLllDP771AzhXC2PDGeZo5tE,42704
skimage/draw/__init__.py,sha256=qpo1wzmh_tNKp7mg4-ztQB-11MM81RP4clhvtkJEMh4,946
skimage/draw/__pycache__/__init__.cpython-310.pyc,,
skimage/draw/__pycache__/_polygon2mask.cpython-310.pyc,,
skimage/draw/__pycache__/_random_shapes.cpython-310.pyc,,
skimage/draw/__pycache__/draw.cpython-310.pyc,,
skimage/draw/__pycache__/draw3d.cpython-310.pyc,,
skimage/draw/__pycache__/draw_nd.cpython-310.pyc,,
skimage/draw/__pycache__/setup.cpython-310.pyc,,
skimage/draw/_draw.cp310-win_amd64.pyd,sha256=gOwdA2UdRqOjbkRhOTGqtp5raoOJHuYdiBzjFiXi-m8,223232
skimage/draw/_draw.pyx,sha256=Z8Jk9-XI5Gq4uIjaOdq1Z4XELjy9Y9n9N0ktWKvALkk,24167
skimage/draw/_polygon2mask.py,sha256=toS2Affa4xv_yg3iw2VZd_nekvfsmTEUe1IrJSS6TOY,1149
skimage/draw/_random_shapes.py,sha256=NYWR0eTl5LFLW9FW4rmSuYPT9rEQY3xdp5YULxddL5k,17143
skimage/draw/draw.py,sha256=_QaeoDxBlBI0x1bbAYJXmCYr1EAqtyF56zrUdH5BGYc,33322
skimage/draw/draw3d.py,sha256=xkjFcdIljd8udEUDk-thNFztb8fx-K71V--Fd-oJeyU,3561
skimage/draw/draw_nd.py,sha256=hT9p20FIiZEq2-nzHyK8pwHoEuT1fZv1D5M7yoULyes,3829
skimage/draw/setup.py,sha256=xuBcDz1UA8GQ1R95Pc5QAafjP_t1IdKCngfhlDas1zs,979
skimage/draw/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/draw/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_draw.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_draw3d.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_draw_nd.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_polygon2mask.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_random_shapes.cpython-310.pyc,,
skimage/draw/tests/test_draw.py,sha256=rgXVJxyVWiCi0BC7h0pIgkU545HuUT8SuDdMpoYoMZg,40353
skimage/draw/tests/test_draw3d.py,sha256=PE3bYs7PjOz10quOLU8UgdZuthAb4Yr6oNgnJl5T6-E,6876
skimage/draw/tests/test_draw_nd.py,sha256=TYTNh-V5sxiL_qbCxLA7A9f2faUZkplEcyPHyT2Pjcc,503
skimage/draw/tests/test_polygon2mask.py,sha256=EjmFpAlBKTxjn1wUMC2DhI9AvnalnyfZBH1juOJHkJI,357
skimage/draw/tests/test_random_shapes.py,sha256=xFHaQOidV-chvmj6Of8ZRyAASCeNvLPK8BElGzwWCmw,6389
skimage/exposure/__init__.py,sha256=Yqwcli5iJj8c7qO-uZyccUDSzm0Esuqvc_JidZXJ9sk,633
skimage/exposure/__pycache__/__init__.cpython-310.pyc,,
skimage/exposure/__pycache__/_adapthist.cpython-310.pyc,,
skimage/exposure/__pycache__/exposure.cpython-310.pyc,,
skimage/exposure/__pycache__/histogram_matching.cpython-310.pyc,,
skimage/exposure/__pycache__/setup.cpython-310.pyc,,
skimage/exposure/_adapthist.py,sha256=1iCpAa3Bgh8BG9v9MUMYDOHai3sgXAFTILGRSO4mqF4,12036
skimage/exposure/exposure.py,sha256=IDdE4JR7wABw5q2GxS-_ItlufZcoBdps9ivvJdRG0lY,28810
skimage/exposure/histogram_matching.py,sha256=HUxnPWSElm5talBLzNtX1SHlRlXGQQ5DR6NcVVTt3oY,3296
skimage/exposure/setup.py,sha256=FyHEFVh8rS3Gik6ZgEI5ktIT9E1R5U59_9c20Rr4axA,745
skimage/exposure/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/exposure/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/exposure/tests/__pycache__/test_exposure.cpython-310.pyc,,
skimage/exposure/tests/__pycache__/test_histogram_matching.cpython-310.pyc,,
skimage/exposure/tests/test_exposure.py,sha256=z8sAxOvCY4D6WflOw0d5TvgMVUjVGjxitCR_7OoMttg,30223
skimage/exposure/tests/test_histogram_matching.py,sha256=szH8v2HOmKaia_Mv6AhJVpmG2fTfeaq7EWQGDZcYJ8U,5035
skimage/feature/__init__.py,sha256=T4bI-uXqU9SBzyQWqyjyFoKxM6DElFlsDsjIgo3HoOo,2977
skimage/feature/__pycache__/__init__.cpython-310.pyc,,
skimage/feature/__pycache__/_basic_features.cpython-310.pyc,,
skimage/feature/__pycache__/_canny.cpython-310.pyc,,
skimage/feature/__pycache__/_daisy.cpython-310.pyc,,
skimage/feature/__pycache__/_hessian_det_appx_pythran.cpython-310.pyc,,
skimage/feature/__pycache__/_hog.cpython-310.pyc,,
skimage/feature/__pycache__/_orb_descriptor_positions.cpython-310.pyc,,
skimage/feature/__pycache__/blob.cpython-310.pyc,,
skimage/feature/__pycache__/brief.cpython-310.pyc,,
skimage/feature/__pycache__/brief_pythran.cpython-310.pyc,,
skimage/feature/__pycache__/censure.cpython-310.pyc,,
skimage/feature/__pycache__/corner.cpython-310.pyc,,
skimage/feature/__pycache__/haar.cpython-310.pyc,,
skimage/feature/__pycache__/match.cpython-310.pyc,,
skimage/feature/__pycache__/orb.cpython-310.pyc,,
skimage/feature/__pycache__/peak.cpython-310.pyc,,
skimage/feature/__pycache__/setup.cpython-310.pyc,,
skimage/feature/__pycache__/sift.cpython-310.pyc,,
skimage/feature/__pycache__/template.cpython-310.pyc,,
skimage/feature/__pycache__/texture.cpython-310.pyc,,
skimage/feature/__pycache__/util.cpython-310.pyc,,
skimage/feature/_basic_features.py,sha256=RwDD3Bunse5utQPSCh02SdbL50CG733kLJpc5UaiN2w,6788
skimage/feature/_canny.py,sha256=KAdecWCwm5NsXPe3bb10IEKgQXS7HAHU6ORR-eh6vYQ,13463
skimage/feature/_cascade.cp310-win_amd64.pyd,sha256=Fv9rJzWSb9fnYlWCdC1buQ6mctt8BsOCJ4wH2--gyZE,194560
skimage/feature/_cascade.pyx,sha256=x3PDugfkp0W2zDSYEwtCmaocGW7bkI1kePlWsvzBSPM,33988
skimage/feature/_daisy.py,sha256=usarct_WMrccBuqHJ2qn5U3FsTFqmjaDt2rKMEU7UYE,10384
skimage/feature/_haar.cp310-win_amd64.pyd,sha256=JYiByYgrm9Ku4weHH-4XMTYyBmz0AmOa-j_cAn4S2w8,329728
skimage/feature/_haar.pxd,sha256=iMdoOr5RUlLybiAB-rh8OzhoOkNt03CALqULHH6pMrU,619
skimage/feature/_haar.pyx,sha256=mS7X2zz8arTrrrRAA0z__m2McDGqE0HWFSNbRe04Xcg,13974
skimage/feature/_hessian_det_appx.cp310-win_amd64.pyd,sha256=ACrgZDZFv3VExC_k6hKi9APQYB9hYj1SOAnXT8grtQw,52736
skimage/feature/_hessian_det_appx_pythran.py,sha256=j2M2-vA1PP1e3NlMsG0IPxhGnn_M4UFg9E1NMwsLcvI,3948
skimage/feature/_hog.py,sha256=_A_fPYO3Y0mT4mvSDDTcOrbMOgSQpGSHd_9SEodLiMU,13391
skimage/feature/_hoghistogram.cp310-win_amd64.pyd,sha256=MhxW_2Gr3hjUgSef1IRCI63FJMeD4wyOcNTZ9s_paSI,161792
skimage/feature/_hoghistogram.pyx,sha256=GhGkm1nFhIFggCXg_luP2SyUgHE4NindRhY14zBCiEM,5738
skimage/feature/_orb_descriptor_positions.py,sha256=xMxaIbJOZp5A1CRJCJwWBWwq6iyQOenDVaeI2-CTHcw,478
skimage/feature/_sift.cp310-win_amd64.pyd,sha256=zCOFnhIQhz9DM389bR6hLXNd2AAECJON0GjArxcgR84,190976
skimage/feature/_sift.pyx,sha256=g1lm73Yd1eQNxjqo3DrXLzhqZY6cwbTVazz0mO9EHKk,6021
skimage/feature/_texture.cp310-win_amd64.pyd,sha256=21TnVrU4zMAyWM-fPNfJEY1vWbYqOwvi6zwUZgSZTDA,222720
skimage/feature/_texture.pxd,sha256=5Ysg5NEg-_OyBwvF3R8extWE4p1bvVCGXC3JNvWX0-o,286
skimage/feature/_texture.pyx,sha256=ZhPWt8xnMex8umxQtIBduYTO5S9SNMIHTE1e6VC2VJE,14531
skimage/feature/blob.py,sha256=-P4vfhO0Zie4NI6H5Ct3Gpz73NbJcUaXepYxWCcRfpk,28330
skimage/feature/brief.py,sha256=fvK5yQp2vdoSdH_w8XiEbLoUDTdLJkDKZg2BQn65uMo,7834
skimage/feature/brief_cy.cp310-win_amd64.pyd,sha256=T_dbJcAcmkmEFT-6U6M-7RHPr3ZwJqy4nZnBPXOcFrg,133632
skimage/feature/brief_pythran.py,sha256=gFNFHnO2TFNjTVmYGnAE6ptOrwDqXvAo9TqwyCItvzQ,475
skimage/feature/censure.py,sha256=rutjEOzvEdE2A4zHnrE9iHt4bok18NtvISVnalmGjek,12259
skimage/feature/censure_cy.cp310-win_amd64.pyd,sha256=EBTRC2Rz_3Rqc4D6yhQ9WSnEUC1ovgSvJpSt6GeuGWI,120832
skimage/feature/censure_cy.pyx,sha256=VEQHGUJBi8hhYEGPDXG56L1IxUuKj-a4Pb4UQpS5Urw,3046
skimage/feature/corner.py,sha256=lRY-NtFer_9zoK5s5P4I72HlPQaj-weqMH7BU1crXOM,43108
skimage/feature/corner_cy.cp310-win_amd64.pyd,sha256=3wdJOIL2ZHhTJ3TQMJRVhw5NkXAwtDDM7Jda_1mqsZs,209408
skimage/feature/corner_cy.pyx,sha256=7OG14zvnj3qiPx06KaMuSvpjXzmlGyiQiwJdKanYZJw,10562
skimage/feature/haar.py,sha256=Dqsmp14lNXRppy8lUcOVcUsuxe7wKyqITuXhIZnfHNI,13611
skimage/feature/match.py,sha256=P97hM29IjeSvvkf4sgOBI9O2SeAZZtCVU0GJiONRidg,4124
skimage/feature/orb.py,sha256=4VyJx17rcs975tnftZJctJy6PoqUqoV3eCeQAvZ5BOE,13401
skimage/feature/orb_cy.cp310-win_amd64.pyd,sha256=ygybYLsOWTK-qyVd6MeSINA2JIdeqc2nuISCu_X9PbM,156160
skimage/feature/orb_cy.pyx,sha256=otAydR0S99CcXLnEdlEQ4R-F1fGSQDDr-FdF_6Mozzg,1631
skimage/feature/orb_descriptor_positions.txt,sha256=S_LZW-3MmEpxczRlk40BD0UHCftjCyJ3u8rRmBbsYCQ,3096
skimage/feature/peak.py,sha256=WigPvex8rvoZxcGvn7h1f3zhPhwOOMARoixaMLeMWV4,16335
skimage/feature/safe_openmp.pxd,sha256=ZpHu07DeGl9Mv_NA0k-oJO-n2D3tMPAvtvYG_ExNiNs,367
skimage/feature/setup.py,sha256=KcpPy9RTtxmWbwKfGhTvP6dpxh8j3GoVckaILHDQQmo,2932
skimage/feature/sift.py,sha256=Un4P7NS3tQyDqhjjCLVZHqzz932bEcIxL2L-_FmzMY4,30459
skimage/feature/template.py,sha256=nqW4QA4tss8t02rExBr90T3MCFqlyV8xJaaSr2vq9OY,6792
skimage/feature/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/feature/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_basic_features.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_blob.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_brief.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_canny.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_cascade.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_censure.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_corner.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_daisy.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_haar.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_hog.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_match.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_orb.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_peak.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_sift.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_template.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_texture.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_util.cpython-310.pyc,,
skimage/feature/tests/test_basic_features.py,sha256=m7yO5uyC5FAjyMwG-Y599xcpms_-Tb5t2dJCYGKt1_M,3394
skimage/feature/tests/test_blob.py,sha256=ptPVRlFxjPBzmFz-QhFgnOg8fOax2jeXf98lvVPlv6s,15557
skimage/feature/tests/test_brief.py,sha256=0oKOQt9FVrXA6p7E9-hlUBkXgUdIYY7li6IZOuKnakg,2929
skimage/feature/tests/test_canny.py,sha256=QmouE6IZpVsDX1ThdYbPNmyIImKWLwUXNRiUHsb1aps,5530
skimage/feature/tests/test_cascade.py,sha256=68bE5NXGP7UTPb9kcgjATq414O_OiMST8b5E_ql9Ubo,694
skimage/feature/tests/test_censure.py,sha256=6xnai7iSHzZmTssoRnpxj1yh5pyPuYxrNqijPR5Tt6U,3880
skimage/feature/tests/test_corner.py,sha256=Gd8Jk_zMsR8yy745Mbhy2Xgw8DNoikSqe6ganNgbO3E,24605
skimage/feature/tests/test_daisy.py,sha256=lqYk_Hkjte7rb-Fa8DZJW6ogmAR_WyHwEW4fNiYIz08,3521
skimage/feature/tests/test_haar.py,sha256=ribJgrUpzgGmrIOWzt8m_4-Syb7xRHHkg7fsBi7ayNk,7782
skimage/feature/tests/test_hog.py,sha256=iqY61DmY_s_Xs2BNXU4ZMVFLdkxZcR5BqueKhUEpTJg,11975
skimage/feature/tests/test_match.py,sha256=nfihwT2vWvP4uEpOcKIr48HAsu8zciDwQxn5rn-r0zg,7458
skimage/feature/tests/test_orb.py,sha256=Bb1CqW1B4Lb2XQA8hxWcPyrJJdOo5q3U9ovUDXHztnw,6514
skimage/feature/tests/test_peak.py,sha256=74GnMuhCtyAAG3IE7cOGkv_a9_FnDTB0sISm1SAcSzg,24575
skimage/feature/tests/test_sift.py,sha256=XNRbCbcd-jn6DlmRNqwahJGB5p8fw32F-42d3kdFe28,6427
skimage/feature/tests/test_template.py,sha256=kS_IqFVjlrCKcSm2LWcCJA8oQ5s7BTSOtXoLHGiKEgI,6361
skimage/feature/tests/test_texture.py,sha256=2SFtzA_FrqrZrUFBJYQ3YrlAyN-WzRr2jYKAqJPBUQg,13706
skimage/feature/tests/test_util.py,sha256=xwY_fC245zSiF9T00yB2BUbdZ9o4CBncrfeykx64T8c,3065
skimage/feature/texture.py,sha256=_-KUxrhLaq0b5rFz8UOln2QvSXxZj1pixdcklNUh8m0,19467
skimage/feature/util.py,sha256=Gi3WyLGdxEz2GH3EOJIr9BF_U5Orr7JVO-6dChvMp6s,6055
skimage/filters/__init__.py,sha256=Vq1xzJq9bZIGOtsCwCCBjIFe7UOKqMVk1quhdOfSUD8,1344
skimage/filters/__pycache__/__init__.cpython-310.pyc,,
skimage/filters/__pycache__/_fft_based.cpython-310.pyc,,
skimage/filters/__pycache__/_gabor.cpython-310.pyc,,
skimage/filters/__pycache__/_gaussian.cpython-310.pyc,,
skimage/filters/__pycache__/_median.cpython-310.pyc,,
skimage/filters/__pycache__/_rank_order.cpython-310.pyc,,
skimage/filters/__pycache__/_sparse.cpython-310.pyc,,
skimage/filters/__pycache__/_unsharp_mask.cpython-310.pyc,,
skimage/filters/__pycache__/_window.cpython-310.pyc,,
skimage/filters/__pycache__/edges.cpython-310.pyc,,
skimage/filters/__pycache__/lpi_filter.cpython-310.pyc,,
skimage/filters/__pycache__/ridges.cpython-310.pyc,,
skimage/filters/__pycache__/setup.cpython-310.pyc,,
skimage/filters/__pycache__/thresholding.cpython-310.pyc,,
skimage/filters/_fft_based.py,sha256=il1xfAZSWe9MU1zLV3vQSCO1OIgUoxDTZVmXVij8g0U,4804
skimage/filters/_gabor.py,sha256=YRvlkWyfPTDRXoz1DdrAIQ5bYJgxd4dvjOF-yRqc6lA,7904
skimage/filters/_gaussian.py,sha256=tx0rwlYdUdWXmgoSF6e5vG6sPpqjnNj7BXyKxQ_REE4,6669
skimage/filters/_median.py,sha256=xEgWRXNSSh4YROoBdP69PrBuI1yGqBy3r1XxI5mAgIo,3173
skimage/filters/_multiotsu.cp310-win_amd64.pyd,sha256=u3bsmsir3_EQyL3RQk2v0TKeMVtDKQuTPh-bnLCtuCo,146432
skimage/filters/_multiotsu.pyx,sha256=fBkOgSEWT1Uw3_jfRzktq657_4jgB5apF9ld_mT0Rjw,16759
skimage/filters/_rank_order.py,sha256=29ZguuUFbA9bWWYBrLVrkeSEPIJqwqrEGkJU62HjYXM,2158
skimage/filters/_sparse.py,sha256=7ZNTNcGl-6Er0IxXJQEqdqNswPC4gW1O35JgPOIFyK4,4736
skimage/filters/_unsharp_mask.py,sha256=yEeswjSVN7fZ4U0rz3rwESOdWNw9Ef9KiMnmB7ujQ3E,6030
skimage/filters/_window.py,sha256=aDTDAdqLcC8h1pwMqXRFTaqIDD8QEVC3KFv9mxk2zGs,4476
skimage/filters/edges.py,sha256=0UdOPGot_MBWGO-sPMRAGeUEunBMr2f4P-hJ0adwCPQ,25989
skimage/filters/lpi_filter.py,sha256=VIp5TsJLSpaV-EWm58SL8bIxo9WldR_ecgQTQ8EYDkU,7853
skimage/filters/rank/__init__.py,sha256=H6s7bbfh2c8Mp3fBa6m6LPup1lC_u7j3HNkzmgP4jvc,2016
skimage/filters/rank/__pycache__/__init__.cpython-310.pyc,,
skimage/filters/rank/__pycache__/_percentile.cpython-310.pyc,,
skimage/filters/rank/__pycache__/bilateral.cpython-310.pyc,,
skimage/filters/rank/__pycache__/generic.cpython-310.pyc,,
skimage/filters/rank/_percentile.py,sha256=qWgLjeLatECmfr9MzkmHGrBBRhmc0YagSLSWThG_5ac,15114
skimage/filters/rank/bilateral.py,sha256=asYXAZBGNuFvj9Mqq216gtmaE74ZsLm_x8PIG7kPjiw,8094
skimage/filters/rank/bilateral_cy.cp310-win_amd64.pyd,sha256=-4kc03IsgLaGvAyZ0aTXpLg0Hdo5ha-EYvQs84f2e-U,281088
skimage/filters/rank/bilateral_cy.pyx,sha256=cVkTYg0Ra8p44kZkBABBrHkPNL0B3HQKHfU5o2FmKF8,3617
skimage/filters/rank/core_cy.cp310-win_amd64.pyd,sha256=OkJ1st-RTV_Wqdi4Z8kMtpSoR_Pgfb3mV62izVuZeig,336384
skimage/filters/rank/core_cy.pxd,sha256=XvLgYGqR7O-UXKA5_udgfU1w9e3SegEQSsUveYCLeIM,874
skimage/filters/rank/core_cy.pyx,sha256=bVBgfUvKoGGHMRc3GGmZCMN7qci4YF3wpTMYeP8aLNY,9131
skimage/filters/rank/core_cy_3d.cp310-win_amd64.pyd,sha256=nGOy3u5yv5KrnIWiKLYl-0XrVOqhrC_rSOn3UjFiVz8,227328
skimage/filters/rank/core_cy_3d.pxd,sha256=qdkm2QvWJ4JRkXmtc6cdhKI_KIFAC6QzjxqVJd86Wyg,940
skimage/filters/rank/core_cy_3d.pyx,sha256=JzaHm_qclRiOsSctmiTSIxPvESJ6IQg-ytopw2mtpKw,11950
skimage/filters/rank/generic.py,sha256=BF5cVBI1HqBWwy89y5oGQJkwpzvqGre2l8Bod4cc3p4,56628
skimage/filters/rank/generic_cy.cp310-win_amd64.pyd,sha256=gY70EBGG-EvWEhyNXUVNjEDne4OQghC7SeRF3pMypNc,1668608
skimage/filters/rank/generic_cy.pyx,sha256=fwSlEYSwiGXlCevZASbvF8JjdtbW0gPNW6jGG53mko8,29006
skimage/filters/rank/percentile_cy.cp310-win_amd64.pyd,sha256=hLNwpyLgux5DYg9Gp6tN8iZIAltqJ9YGjxoY0gqUqa8,562176
skimage/filters/rank/percentile_cy.pyx,sha256=KItZLZvn0k9tEGppJdqlefqaP6aO1yariCQHYl2qdeE,11959
skimage/filters/rank/tests/__init__.py,sha256=x4utlIkLvCHJYcMb8dnd0PtysMkBGOqj__prYGPe9Co,137
skimage/filters/rank/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/filters/rank/tests/__pycache__/test_rank.cpython-310.pyc,,
skimage/filters/rank/tests/test_rank.py,sha256=xwOQ1k0HIJfV_2qVemltlIzNYcJLqtKleX_6eh40Lmk,36057
skimage/filters/ridges.py,sha256=cDCVpSclRINJ0bhJS2PGX5w3iK9jtvq_tNhiUlNdSRU,19828
skimage/filters/setup.py,sha256=X9d37PIlrq8QXd8GONJR-fU7ugGJipCBxS-R4hcfBjA,1870
skimage/filters/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/filters/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_correlate.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_edges.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_fft_based.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_gabor.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_gaussian.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_lpi_filter.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_median.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_ridges.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_thresholding.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_unsharp_mask.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_window.cpython-310.pyc,,
skimage/filters/tests/test_correlate.py,sha256=J3CNo9rBjcULZK6zpb3oOdE3fpPBSQbcfncNysU7Uqw,2048
skimage/filters/tests/test_edges.py,sha256=lgTgtgR_HKu3zx-CS0aDZx1XtrtA1c02R6YnnKIF7s0,20862
skimage/filters/tests/test_fft_based.py,sha256=golH0zcDCapkiQe_UIDz6KnTTPHlzsQjh_qfBQT7BFw,12654
skimage/filters/tests/test_gabor.py,sha256=NTAgXkj2h1dXfg364Ad8sxGN5p7pTVh3gTikhymyD9M,3878
skimage/filters/tests/test_gaussian.py,sha256=LQG_b_-Kn3I7JilEifpmF5ul5kAzHTJrRU6P_Yo5yfU,6907
skimage/filters/tests/test_lpi_filter.py,sha256=zhpp3sYpp3snsmhsDFvAkS7tpk96B8vbWfdmSqt3YjQ,2767
skimage/filters/tests/test_median.py,sha256=77sNXxqRFAPQcI-g6UKxVfDLKOCWi-cA6JNYQ7Fo0lY,2239
skimage/filters/tests/test_ridges.py,sha256=fy0540juDrHs15wklOSgPcYzVpUULP421p8sN5y-Oac,10333
skimage/filters/tests/test_thresholding.py,sha256=oclgkYRt471ikNhX0wosqZ5845LE8ZNMKOBGqh_uz5w,28121
skimage/filters/tests/test_unsharp_mask.py,sha256=WkDn3RiNiYIcPNveMJUdqu3VQHuk5cmSbRO891V9C2k,5810
skimage/filters/tests/test_window.py,sha256=nlwlhEbSRl97SQISh1ZtH8Ww8y1WKtYP0Sn1Mplv1Ws,1677
skimage/filters/thresholding.py,sha256=BRbxy1XozLKPTcfjwj4mGNiLWWuB0oUU7C9CrBz_-vM,49099
skimage/future/__init__.py,sha256=Oo75u6f4w1M_DgZ47blHCGvb-y0MvXnVEf_9AbVHuFU,792
skimage/future/__pycache__/__init__.cpython-310.pyc,,
skimage/future/__pycache__/manual_segmentation.cpython-310.pyc,,
skimage/future/__pycache__/setup.cpython-310.pyc,,
skimage/future/__pycache__/trainable_segmentation.cpython-310.pyc,,
skimage/future/graph/__init__.py,sha256=sYqIw7sFc-oF-p1UVsmoyWA8VTv1qtdtvMNgFlKu0ms,400
skimage/future/graph/__pycache__/__init__.cpython-310.pyc,,
skimage/future/graph/__pycache__/_ncut.cpython-310.pyc,,
skimage/future/graph/__pycache__/graph_cut.cpython-310.pyc,,
skimage/future/graph/__pycache__/graph_merge.cpython-310.pyc,,
skimage/future/graph/__pycache__/rag.cpython-310.pyc,,
skimage/future/graph/__pycache__/setup.cpython-310.pyc,,
skimage/future/graph/_ncut.py,sha256=bLcW3Geo9RmsEgWOG2y9ehRDun9KnXDdCMu8kUcEofI,1848
skimage/future/graph/_ncut_cy.cp310-win_amd64.pyd,sha256=vkiHBY7tgBmhL-X8O10Z6y4o-eB9rpxv4JwrjoQqr9o,131072
skimage/future/graph/_ncut_cy.pyx,sha256=tFZgDtgMUihih0wJenyhoDXji8H2aR-VQVVhRhP6lWs,2020
skimage/future/graph/graph_cut.py,sha256=i2mie16H75oOww7NmGpFNbmyEFnIayQ871AWUuLm2sg,10143
skimage/future/graph/graph_merge.py,sha256=2y6wulHp5bVp_KBsB-bxk_P663hNoR_hqUBRxUMYFk4,4461
skimage/future/graph/rag.py,sha256=DsPSLGFhcfVUQPOC5Py5uclFTMNWeK3EHC3hIEwALR4,21512
skimage/future/graph/setup.py,sha256=ndyH3gizR5HTR1QxhdXfF5RiuOKn3Yo6_1FIYIm7sTY,1083
skimage/future/graph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/future/graph/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/future/graph/tests/__pycache__/test_rag.cpython-310.pyc,,
skimage/future/graph/tests/test_rag.py,sha256=fNzqpUFKgimOY-iNum9g98T1n7kDNGb8N9VRmWH_DwE,7301
skimage/future/manual_segmentation.py,sha256=4_vDLrbxTkVVU9vrasG6LK3gtgTwxf98SlurtAN5nEc,7571
skimage/future/setup.py,sha256=zRo8zp76SJ8uGntxidrv8-bSgSgv-nhakpP9mwPj9uQ,386
skimage/future/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/future/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/future/tests/__pycache__/test_trainable_segmentation.cpython-310.pyc,,
skimage/future/tests/test_trainable_segmentation.py,sha256=36zfjG28ufBVy-Zt0iQlFVTZNxhhfJfMW_akTJw8h_A,4375
skimage/future/trainable_segmentation.py,sha256=-58cqmHEQVIkG4o2-KC8ZZ4bMuwS7yY77pmZkZSezNg,5767
skimage/graph/__init__.py,sha256=PsQEvaKtVqx6JqSBPTRWADC7DF8zrtxC-H7LOSqi10w,559
skimage/graph/__pycache__/__init__.cpython-310.pyc,,
skimage/graph/__pycache__/_graph.cpython-310.pyc,,
skimage/graph/__pycache__/mcp.cpython-310.pyc,,
skimage/graph/__pycache__/setup.cpython-310.pyc,,
skimage/graph/__pycache__/spath.cpython-310.pyc,,
skimage/graph/_graph.py,sha256=l_qKN0pfY2Ytm-c6LZ1IxyUkHfB8t4Wq1sZXGktcXy8,8829
skimage/graph/_mcp.cp310-win_amd64.pyd,sha256=JBF6FxRlTfAAbUewM5XQp9KOCDgIZJ4n_3MM5z-n30I,303104
skimage/graph/_mcp.pxd,sha256=KSfWG1ZwMbLQYzrmVhsr9dOKSsqV24HXEA0aPkRIwnw,1622
skimage/graph/_mcp.pyx,sha256=-8oJqZzW_1RFHp0vyEh3Xta1t2Xm_XI-qMCw_6KxZBM,37916
skimage/graph/_spath.cp310-win_amd64.pyd,sha256=Jm_C3uOmy329jir2oAEGsf9mvEriMlauAuFeetfjVpk,137728
skimage/graph/_spath.pyx,sha256=AftZmx8LwwLe3fFBoazjBsUVQWM-ib479es0U_08K68,1079
skimage/graph/heap.cp310-win_amd64.pyd,sha256=bxbs3A3r2haOnZheQsWHE8218wZ2270og-rWWBRQgMU,58368
skimage/graph/heap.pxd,sha256=Vf5MLZadNkRvcwNFBe2tPoiulH_vDZZEYiwsIECLdJ4,1337
skimage/graph/heap.pyx,sha256=rLSIM8_UjrLusUNSh9mi-DnCPJCSrlMCUSA3RSBCCD4,25324
skimage/graph/mcp.py,sha256=k8SQ388sWoWuH8DC0zZrXu_-cRqP6R-Xu-sSqquDxBo,3281
skimage/graph/setup.py,sha256=wR4eFLQCLQY7uNoOAAa9I5YIO4T3UgYvksnmhnwCo6s,1371
skimage/graph/spath.py,sha256=ZQGsvoG9DrtthGo0mac3SgbN8RoweG5YsdCZwJPl07M,3578
skimage/graph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/graph/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_anisotropy.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_connect.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_flexible.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_heap.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_mcp.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_pixel_graph.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_spath.cpython-310.pyc,,
skimage/graph/tests/test_anisotropy.py,sha256=2WuY1eWH97EJmqN9ieJPp6qEQUgw6mjZEkuTPi18xjU,2159
skimage/graph/tests/test_connect.py,sha256=8t3BXGmME6G2C4kWdAU2VY8-L9FaEjkIgGL8apK1kaA,2503
skimage/graph/tests/test_flexible.py,sha256=FZVkC0b-8bpuW4384nHk9liJnz-RDpa5ItY4MvzffUI,1750
skimage/graph/tests/test_heap.py,sha256=Ez_U_XN__443hEZBpKrg29mmrUCYE0eYSbQmaxvX2gA,1152
skimage/graph/tests/test_mcp.py,sha256=PQ2tW3M6C0eGbZMjYTWfjkT266Tv5JdB_p5q1kReEqA,6290
skimage/graph/tests/test_pixel_graph.py,sha256=kYk04t2z9CYX6K4q-st42I-4vz_BZUGE3Zc-ptMoA48,1846
skimage/graph/tests/test_spath.py,sha256=db5_shbeRgvoI9lD3XcxHAqSPwENsUK_VeFbRmOCpAQ,911
skimage/io/__init__.py,sha256=sl9MP9IAHlDCvhuwcbHSwn2h4locAOJZkrkcaXI66HE,1831
skimage/io/__pycache__/__init__.cpython-310.pyc,,
skimage/io/__pycache__/_image_stack.cpython-310.pyc,,
skimage/io/__pycache__/_io.cpython-310.pyc,,
skimage/io/__pycache__/collection.cpython-310.pyc,,
skimage/io/__pycache__/manage_plugins.cpython-310.pyc,,
skimage/io/__pycache__/setup.cpython-310.pyc,,
skimage/io/__pycache__/sift.cpython-310.pyc,,
skimage/io/__pycache__/util.cpython-310.pyc,,
skimage/io/_image_stack.py,sha256=FibEhJ_OW8gvyco8XAC2bq0gBn6Z7x0ZM4I4p9Q4ExA,605
skimage/io/_io.py,sha256=F0q-ZzfVr-CHPHZkpXrv1tkPjASA4jbvP2vQDFUwqf4,6614
skimage/io/_plugins/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/io/_plugins/__pycache__/__init__.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/fits_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/gdal_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/gtk_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/imageio_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/imread_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/matplotlib_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/pil_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/q_color_mixer.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/q_histogram.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/qt_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/simpleitk_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/skivi.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/tifffile_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/util.cpython-310.pyc,,
skimage/io/_plugins/_colormixer.cp310-win_amd64.pyd,sha256=msmxn1pOMzrF1KkBc7BgiFP2FWaTDkkgAJRzwQqXokk,71680
skimage/io/_plugins/_colormixer.pyx,sha256=M69hmWTjHqfVGT2aoFULoFLjOZZgesQ0qukteHkAQCw,13313
skimage/io/_plugins/_histograms.cp310-win_amd64.pyd,sha256=3JpMSxiHCDgDlw6rZnOFCpuP7bEw5ZDphhzsjMIivX8,51712
skimage/io/_plugins/_histograms.pyx,sha256=A-Tr-muTO7EUhzoS4fp2vgcjMJ5TTQYpDjH4UkK4y2I,2086
skimage/io/_plugins/fits_plugin.ini,sha256=lcgVwH76YrdMYR5ooxTr0S5CjVosGOLR0toB2o6L0m4,93
skimage/io/_plugins/fits_plugin.py,sha256=1M5YJGiiBwh-hGfhHrIMeT2s0x8lLEVRDtVrSo2vLuY,4572
skimage/io/_plugins/gdal_plugin.ini,sha256=k5btXp2BMfnBqo805tMRPeMKfS1FToYlrGQN8O874_Y,94
skimage/io/_plugins/gdal_plugin.py,sha256=4yjFNv9jixY66_3dYcuqPCF2sfTiYx8RmM19tmEQ1_U,384
skimage/io/_plugins/gtk_plugin.ini,sha256=SdHuAJwPntXZ382jEMl49b64q9ZlX5_UE-9HEIKsEws,95
skimage/io/_plugins/gtk_plugin.py,sha256=hIw0-Rdq3nlOuLuG0zt8FA1A-dwIBm8kB-u-yhpFYWI,1771
skimage/io/_plugins/imageio_plugin.ini,sha256=lIKNERENcVlj25kL_FWDGpA5GuUjEOR7b64ivA4hLNo,91
skimage/io/_plugins/imageio_plugin.py,sha256=4RYh2-6FPBQdKhFeVZD5aPneOmJiMnFtpmMEPTBTG8Q,424
skimage/io/_plugins/imread_plugin.ini,sha256=IB6IQ0YrAURcdZUVLIZdo27qQyIUdxXcSywi7iWdsLM,89
skimage/io/_plugins/imread_plugin.py,sha256=iLfRdJMB3W2alFfDbn-BD4wuhbtTxw64rRqbCT1dyQk,1014
skimage/io/_plugins/matplotlib_plugin.ini,sha256=5HSDnyPHYcK8KunSBSn4FJJKnidqxxtRGsngnQYRVCY,126
skimage/io/_plugins/matplotlib_plugin.py,sha256=p744ozof32gcM2xih50rhdC7db73Lo9oZga8YvA1HBk,6636
skimage/io/_plugins/pil_plugin.ini,sha256=75uxuOky9BKlc0PF9w71zmp_ilVmeHx5UhZouuHCV48,94
skimage/io/_plugins/pil_plugin.py,sha256=42CUBmlI074dxg1mM3Yly6Vydperyoqv2NnJUdckSbk,8584
skimage/io/_plugins/q_color_mixer.py,sha256=vsWYF1ik4FAmKCGndtgQzqL27iuswZRkLF-VyASUMak,12342
skimage/io/_plugins/q_histogram.py,sha256=hfd2SJEaNSwEpNjgbEwVk492pmtzPmnzVK7pYiA1-xo,5013
skimage/io/_plugins/qt_plugin.ini,sha256=3qKFfDhcZ0ornanR3Q61q-DDhZzerKBkGlILycGjPB8,158
skimage/io/_plugins/qt_plugin.py,sha256=SG--RJoc0Uh0AugB1oSvbPejEFoSzwV3ududM60xYz0,5798
skimage/io/_plugins/simpleitk_plugin.ini,sha256=e24xygfzDppazgLPAILifdt41t6QzayN47p9vPQKEkw,95
skimage/io/_plugins/simpleitk_plugin.py,sha256=A6r2oiXk_X7YNBPbjE_e09TuSG1kCiHJvnbzAe4o4rg,608
skimage/io/_plugins/skivi.py,sha256=XO7Wje-6utnB1_5G77OHNHkBewSr2ES66vfux0zz7uw,8139
skimage/io/_plugins/tifffile_plugin.ini,sha256=4nMhFJOanbVy1UDXNFMWBNXIA15l6yAb0IaZVwur-4g,113
skimage/io/_plugins/tifffile_plugin.py,sha256=JNVjruUy_nTBubq-ah80pi7sI812ZJ34s7wnacYnFyQ,782
skimage/io/_plugins/util.py,sha256=wS7Jae77QwuLRnSP9paR1rGhU-4KGRjdi0b8LMl3F6E,13656
skimage/io/collection.py,sha256=Es_HuAFRt6FDuv5rv9CqgpM5v1syfV2uYMOiTcBsd8Q,15038
skimage/io/manage_plugins.py,sha256=260kaoNbcZJRprrfs0uT3y49ODiugc_ZWNJAF7al0rE,10890
skimage/io/setup.py,sha256=DaMRwc1kPwMsrTuUB-puNR5f7ACKQ90AT86SNnF3VCM,1387
skimage/io/sift.py,sha256=5yLsQw4FGNawgbMpDl-5OhQ88RoJCr1A_rwpIewtaes,2526
skimage/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/io/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_collection.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_colormixer.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_fits.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_histograms.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_imageio.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_imread.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_io.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_mpl_imshow.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_multi_image.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_pil.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_plugin.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_plugin_util.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_sift.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_simpleitk.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_tifffile.cpython-310.pyc,,
skimage/io/tests/test_collection.py,sha256=XXXPfoHy_CtXTxKpCiAtfvuguLnFpgZujLqO9EnFb6E,4755
skimage/io/tests/test_colormixer.py,sha256=D9eqy6dKgdrBa4JV-pmuNb5rbrjD9vaZciXrj7UCIBs,4619
skimage/io/tests/test_fits.py,sha256=bumj-u_XE7T6-ULYvrziQDM6JsqL2gvOg3S3ZxyM-kE,951
skimage/io/tests/test_histograms.py,sha256=fEWQFPDjqqEkPblZ1vGLFdxJRy1OA9lxY3zlL79eFRc,822
skimage/io/tests/test_imageio.py,sha256=YDbmX0Lap5gox0F2zTRMriub9WddTKMxzxJcM1NnvvQ,2368
skimage/io/tests/test_imread.py,sha256=XRzIGmrgf3FbSOoJUYDIc_rn262spV2BPguGs1nFra8,1960
skimage/io/tests/test_io.py,sha256=YtiQ2YeHrGYYWbpRg9bb8G7ymjwa-8K4bIdkLODqxH0,4014
skimage/io/tests/test_mpl_imshow.py,sha256=1C6dsGj6WzajLjMjT1x6VokngHLy1bQ7BgX7xmxXYao,4340
skimage/io/tests/test_multi_image.py,sha256=pcQryGBW4mhYN6N8gGcJLQmxcf2i7i-xiX3KRFLn2ys,2598
skimage/io/tests/test_pil.py,sha256=D2UTxerKkz7eghx3ODmoftKS2ZoRYYVD8lMiWS3HjiQ,9111
skimage/io/tests/test_plugin.py,sha256=V5AdrRAdror_yLjLZgszpn1n8K9YHBRr9l728URHyjI,2403
skimage/io/tests/test_plugin_util.py,sha256=NXu7wlEnnGGp5OL6xsFaAFcyBW7ZPuUuBaOBjuHLbQg,1963
skimage/io/tests/test_sift.py,sha256=vAxNQTTfXrgDGj8pbg1ZtpfhZsBLm888f8_VM6kju1o,3319
skimage/io/tests/test_simpleitk.py,sha256=Jn2dz9TeSFABxtz1Jh9U2tcxOGsPk_Sdln4eo-fvVfI,2476
skimage/io/tests/test_tifffile.py,sha256=aw2fOZRb4I9TtHVit-bzL8cKpbEu6vjEuy-OjiruaPs,2359
skimage/io/util.py,sha256=r2hJWO09wb7k2TW5rXiE4KcN289J0zCu3tIBhXDx0ug,1345
skimage/measure/__init__.py,sha256=U6XunPhJCRqeQ0dVB-xrCiC4_854yTQbBgFQNn05uv0,1738
skimage/measure/__pycache__/__init__.cpython-310.pyc,,
skimage/measure/__pycache__/_blur_effect.cpython-310.pyc,,
skimage/measure/__pycache__/_find_contours.cpython-310.pyc,,
skimage/measure/__pycache__/_label.cpython-310.pyc,,
skimage/measure/__pycache__/_marching_cubes_classic.cpython-310.pyc,,
skimage/measure/__pycache__/_marching_cubes_lewiner.cpython-310.pyc,,
skimage/measure/__pycache__/_marching_cubes_lewiner_luts.cpython-310.pyc,,
skimage/measure/__pycache__/_moments.cpython-310.pyc,,
skimage/measure/__pycache__/_polygon.cpython-310.pyc,,
skimage/measure/__pycache__/_regionprops.cpython-310.pyc,,
skimage/measure/__pycache__/_regionprops_utils.cpython-310.pyc,,
skimage/measure/__pycache__/block.cpython-310.pyc,,
skimage/measure/__pycache__/entropy.cpython-310.pyc,,
skimage/measure/__pycache__/fit.cpython-310.pyc,,
skimage/measure/__pycache__/pnpoly.cpython-310.pyc,,
skimage/measure/__pycache__/profile.cpython-310.pyc,,
skimage/measure/__pycache__/setup.cpython-310.pyc,,
skimage/measure/_blur_effect.py,sha256=JNji7-IoNRYEDwwPPGkkbeDG08JDcVVtsHjSFo0wolk,3001
skimage/measure/_ccomp.cp310-win_amd64.pyd,sha256=zs5t3OoFoiWQmtugBY4pT_1D1vrtnp4Jv1LSGNH9FQI,77824
skimage/measure/_ccomp.pxd,sha256=BM_DTd3c67rnRclDZSb8Cx-L5fanqNKTHb3RuYkQVJM,288
skimage/measure/_ccomp.pyx,sha256=pLqC7pxASl1jryq4IaexiGaoIU_G3U1dRUIrqcC2Bu0,27920
skimage/measure/_find_contours.py,sha256=XwTpkUszil4Y2fh5gV1Uz5_9dsaYdQqdMbhcUbWG9Lk,10120
skimage/measure/_find_contours_cy.cp310-win_amd64.pyd,sha256=QsQQa_jRT-alBly1LYL19whO-1NcYGX4W5LSkuvBhCI,129024
skimage/measure/_find_contours_cy.pyx,sha256=dB_aNEXP_OoZE1FpLI0-QFXIzIYU7fzNZ5oAU196Jd4,6757
skimage/measure/_label.py,sha256=hvmrd2bLmbKUMcX_rgfo9a5o2P-pVxzjPiCAeEqvL0E,4190
skimage/measure/_marching_cubes_classic.py,sha256=ulUYz7jkKeScxBr68OACimwJQiC3SLCRklst_Elxvqs,7873
skimage/measure/_marching_cubes_classic_cy.cp310-win_amd64.pyd,sha256=6FFPDgiPtX9RznUVo4AvJH5nbdJ3r4nsgSY0fLITzhc,270336
skimage/measure/_marching_cubes_classic_cy.pyx,sha256=0y_wrAzzVDWtMuOVnYSbf3JsyOoIvRFLdtKDIScElKg,40665
skimage/measure/_marching_cubes_lewiner.py,sha256=Nb-UJoOb2cwXPJmq8PetBy2_-McUAYuOw6jmMr33nZE,12452
skimage/measure/_marching_cubes_lewiner_cy.cp310-win_amd64.pyd,sha256=KOXSmIFYB6PwwA6jR32esDjPsMrjz5BvBCYzTgy6vyg,215040
skimage/measure/_marching_cubes_lewiner_cy.pyx,sha256=3_-zhyne2Bh1CuMoEE7TYslQd2Iaa-xgchNShdlNqak,56349
skimage/measure/_marching_cubes_lewiner_luts.py,sha256=2k_Fr5aV3vjKFK4MliQ8_5BgcKD64bGGby-Tdyh0YMo,27548
skimage/measure/_moments.py,sha256=469poOm--hg8ZsTuQCg-tVv0dZC0YCcUp6wo21E6BGI,17085
skimage/measure/_moments_cy.cp310-win_amd64.pyd,sha256=rmcOqed4OESAAnTbx1DbPABPWjLKrCsvpJZ44hQS_88,148480
skimage/measure/_moments_cy.pyx,sha256=_eurOgyhEZLRg5wh5j5EjBrVp-6retGB789P54dtsMk,1024
skimage/measure/_pnpoly.cp310-win_amd64.pyd,sha256=NnBGV0BLl5fQ376OMYRLiwTIlgi4PJeH4k8wEy3d5Fw,140288
skimage/measure/_pnpoly.pyx,sha256=1e9zBA2ftrQELUWb8G4qCGYBUlxWG50pka9fNPiCLdw,2499
skimage/measure/_polygon.py,sha256=ENZXrJ10Q-pmJ52P8N2A4rMATxx8lBaqS03PuBr3Bj8,5521
skimage/measure/_regionprops.py,sha256=Fi7UibbtUI3gsVZTSq4R4ZEHIBDNSh1cASWRz0AQmVM,50407
skimage/measure/_regionprops_utils.py,sha256=FhaPnv0vYSTQxGZ0XiY6zdT7grf-5E421xfnhd--ymY,13836
skimage/measure/block.py,sha256=nS_CDuQNgeZvJRyipKXGUCThxYqEO4xqqNWwAwhFJs8,3310
skimage/measure/entropy.py,sha256=wpiLNoEWmLLlLl9Jzo2wuFx9oRSq7DcQfnY9omqzJtg,1185
skimage/measure/fit.py,sha256=xwskItYPxKDLkBUQ7xqMNIEWqWeOpowIvt9W_yUS968,31487
skimage/measure/pnpoly.py,sha256=zB4rSxjHJyY1OsGZ7QfFQqxIEDLGK8CWPehwutoFqm4,1400
skimage/measure/profile.py,sha256=K-iyKeeKWNTgwZwspS4-D_nq1QCE6lAaDMgePRjH06g,6927
skimage/measure/setup.py,sha256=D3JEkePGDXMKAJVEMwxq3D2cDOmlYdp-Axcw3eLVQSY,1940
skimage/measure/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/measure/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_block.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_blur_effect.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_ccomp.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_entropy.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_find_contours.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_fit.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_label.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_marching_cubes.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_moments.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_pnpoly.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_polygon.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_profile.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_regionprops.cpython-310.pyc,,
skimage/measure/tests/test_block.py,sha256=f6g78GDqdaLcJuYWyc1_vfuz8f7dJvVIU0r-8pnpoRU,4518
skimage/measure/tests/test_blur_effect.py,sha256=YBUxCKW1PvimAvaDF-AZ9Ne_23djtzxtNsU-plT-hD8,1839
skimage/measure/tests/test_ccomp.py,sha256=Y8Jru5-3sZNbzY9SDe0So3ZzvBRrcCycoNoBdYCfrnQ,9859
skimage/measure/tests/test_entropy.py,sha256=hpC3ilAoeWlmJtyz1Hf5cWzuVXUlR2ywN8gc_QZZgHQ,416
skimage/measure/tests/test_find_contours.py,sha256=gj_gV4Fa89K9oh0Wyysb6koCCysBWTnk1H19GLjuNZk,5399
skimage/measure/tests/test_fit.py,sha256=aLAW49szK2Va6h50cVU-pW2I8KkEO3rv0FnXjHlEpNY,17105
skimage/measure/tests/test_label.py,sha256=N-BxUvx_-vAg7_4ifnhMfIch_xl02z_sQ8c8kl-jWeY,1842
skimage/measure/tests/test_marching_cubes.py,sha256=ojzaa-obrEQQkYTWdxSfrOzUFdqVJ91WpY-DtuY6ofI,7442
skimage/measure/tests/test_moments.py,sha256=1sqjB2yrvzMJrYPodtoIKNSbgIBoOOuAo0VS4GwTEYk,8209
skimage/measure/tests/test_pnpoly.py,sha256=ql2L7_28C4I36UAy6j-hech1FFbeAS1yxyQG89ZpFzg,1063
skimage/measure/tests/test_polygon.py,sha256=RM3vYa042dYLpXnbAGh477NzmrD5Rkpjzr7Jv4lfMpI,2335
skimage/measure/tests/test_profile.py,sha256=pqBCKX-SLQOkSN1JruiJT5GCC21IlWDV-keSzFvhnRk,7871
skimage/measure/tests/test_regionprops.py,sha256=6uE8BjHLNMFC-jXWnM69lHohpFxJmlfU5JgvAFhEJNI,27486
skimage/metrics/__init__.py,sha256=_mvh5x7Zr5XfJqs3fhEPbrZmzER8e57nQfOEptWD84s,911
skimage/metrics/__pycache__/__init__.cpython-310.pyc,,
skimage/metrics/__pycache__/_adapted_rand_error.cpython-310.pyc,,
skimage/metrics/__pycache__/_contingency_table.cpython-310.pyc,,
skimage/metrics/__pycache__/_structural_similarity.cpython-310.pyc,,
skimage/metrics/__pycache__/_variation_of_information.cpython-310.pyc,,
skimage/metrics/__pycache__/set_metrics.cpython-310.pyc,,
skimage/metrics/__pycache__/simple_metrics.cpython-310.pyc,,
skimage/metrics/_adapted_rand_error.py,sha256=FKziSWDhMAhaZ_Yzc7BMILO7BqkOcawyn8xiqL6EqVk,2805
skimage/metrics/_contingency_table.py,sha256=_x9hhXwbXmfoWe-eeafhxowxIQj4Gi1kkChrXzt0l2s,1285
skimage/metrics/_structural_similarity.py,sha256=vkU3CJPi1CG-JkEG_f1ZaCpRLzyn9tlZsBIgrZh2me8,9341
skimage/metrics/_variation_of_information.py,sha256=JJas7LsACc0ZD3utQm4SnI_YAEbQYiFjpNmGrEkxX7U,4398
skimage/metrics/set_metrics.py,sha256=w0xkqS58zgz5eQK6wPKV8dzYquPspIDQLzdbvzo4B2g,3814
skimage/metrics/simple_metrics.py,sha256=F0flrcIEjdheTVfJHnWGjP8w6XxYqKQ3OmhOnG2w8BM,8428
skimage/metrics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/metrics/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/metrics/tests/__pycache__/test_segmentation_metrics.cpython-310.pyc,,
skimage/metrics/tests/__pycache__/test_set_metrics.cpython-310.pyc,,
skimage/metrics/tests/__pycache__/test_simple_metrics.cpython-310.pyc,,
skimage/metrics/tests/__pycache__/test_structural_similarity.cpython-310.pyc,,
skimage/metrics/tests/test_segmentation_metrics.py,sha256=NHjFVPdpk2ZpfVtdLbAFGtvCU3VRMSAtRUN9jr5EGYw,1704
skimage/metrics/tests/test_set_metrics.py,sha256=PxQ89VNp5RdXDQmnGCpIlJjN0S2tbEbqGnL9CbAXe1w,5983
skimage/metrics/tests/test_simple_metrics.py,sha256=PpM9AXSJsTw5JGSpnQbXBmyes8YofBRdXZeNW-2Qm8k,5146
skimage/metrics/tests/test_structural_similarity.py,sha256=dtDtC_mO4NmitUJN4phs1V7KsfjiyyPY41KsMgzBrDo,9558
skimage/morphology/__init__.py,sha256=HL1Lg6BcZp8fVnDRQuqV8Yrnz2uPZeW_6j5sx2a7LAg,1991
skimage/morphology/__pycache__/__init__.cpython-310.pyc,,
skimage/morphology/__pycache__/_flood_fill.cpython-310.pyc,,
skimage/morphology/__pycache__/_skeletonize.cpython-310.pyc,,
skimage/morphology/__pycache__/_util.cpython-310.pyc,,
skimage/morphology/__pycache__/binary.cpython-310.pyc,,
skimage/morphology/__pycache__/convex_hull.cpython-310.pyc,,
skimage/morphology/__pycache__/extrema.cpython-310.pyc,,
skimage/morphology/__pycache__/footprints.cpython-310.pyc,,
skimage/morphology/__pycache__/gray.cpython-310.pyc,,
skimage/morphology/__pycache__/grayreconstruct.cpython-310.pyc,,
skimage/morphology/__pycache__/grey.cpython-310.pyc,,
skimage/morphology/__pycache__/greyreconstruct.cpython-310.pyc,,
skimage/morphology/__pycache__/max_tree.cpython-310.pyc,,
skimage/morphology/__pycache__/misc.cpython-310.pyc,,
skimage/morphology/__pycache__/selem.cpython-310.pyc,,
skimage/morphology/__pycache__/setup.cpython-310.pyc,,
skimage/morphology/_convex_hull.cp310-win_amd64.pyd,sha256=Nl3VuPlzMndpjnJpZO7K9nB-G0hNty2ZxFE1KmA2prA,124416
skimage/morphology/_convex_hull.pyx,sha256=Fk6jV6cNenUyqRt6lILDXMptiLZFGJLbjCGcJOr2KXk,2229
skimage/morphology/_extrema_cy.cp310-win_amd64.pyd,sha256=4otHLEzu1q3HTd4WS_mKeHgP3pZigBewiJFXJVrFLMA,205312
skimage/morphology/_extrema_cy.pyx,sha256=XhSNqLQ8eyxLITE6_AT1hGbuAyaeUNLDXwNylzlW5Ew,8191
skimage/morphology/_flood_fill.py,sha256=a4tSTNV3lJfArsZQLe1sw69pkxV71M1_8Hp-DsgPmZE,10987
skimage/morphology/_flood_fill_cy.cp310-win_amd64.pyd,sha256=iciW_gEoqXTwKKdX7uZ-hFL9wqcVM3HCTRtO6kiUDUo,243200
skimage/morphology/_flood_fill_cy.pyx,sha256=YBtc8DEk-oxSOAn6Dt-xUao98FAIE4V2aAvtcHatpAI,5007
skimage/morphology/_grayreconstruct.cp310-win_amd64.pyd,sha256=ytUxNnP6QxHkh9hrdwESaKMzQnq-kfy6MIpmO3Xl6Qc,46080
skimage/morphology/_grayreconstruct.pyx,sha256=-bukJwt4sAzyPlRV4mQ1cBR9MC7E6EAkyMqC3JaAVkQ,4567
skimage/morphology/_max_tree.cp310-win_amd64.pyd,sha256=fQG5e9_j5A31ydhQBwBVNPWKezyiBtqaD9E2DhhfSwQ,612352
skimage/morphology/_max_tree.pyx,sha256=JfUFRFgRv2BCt9g1EKKPxJkPjcRIppMacFoWpFSRUS8,17022
skimage/morphology/_queue_with_history.pxi,sha256=3kHZr5bMrz9sk7VwWA0szFHtP9cJcMH7y3mTrkyGQ2o,4320
skimage/morphology/_skeletonize.py,sha256=_Q9EVxVUuZDud2mHYc4SUwHiIS-HyCg97g4FSAyzXds,23982
skimage/morphology/_skeletonize_3d_cy.cp310-win_amd64.pyd,sha256=aFxGbWhb_RVVAPnvYUiy15SAsXrR-OJ0y8sQiZUNl0w,139776
skimage/morphology/_skeletonize_3d_cy.pyx,sha256=N9UeV-3HPFUiKlg9R-4JzBwZd1qqk1HQrUXy4BSjS-0,29056
skimage/morphology/_skeletonize_cy.cp310-win_amd64.pyd,sha256=sc9clWp33Lgj9y1ubk4MRgd82nR6L0dkxDKWN8xxJGg,140800
skimage/morphology/_skeletonize_cy.pyx,sha256=5ARNEjDg8jgXBoStarh5ozYH8QSvxGsJ2U-uhtMP0pY,12226
skimage/morphology/_util.py,sha256=ZHO9KnKkeJJu159dY4hmm7c5BtkjR9hIB2PGYLOHoFQ,10466
skimage/morphology/binary.py,sha256=T_aGRVm37qfOUo1pMJFOmFlKGkunvsY0qw02u1bVqW4,5524
skimage/morphology/convex_hull.py,sha256=U_69bZTisVlGXz0Y8VtzNjvSZDw_Z5N7rwUvuk5nU4o,8477
skimage/morphology/extrema.py,sha256=B3fP3j3bbxL4CxhVD_XRxJHbS6Dq_e_uCRbms4qoJ6g,22130
skimage/morphology/footprints.py,sha256=TxqzoS7PF-qE-wruCzPvSh_xLEOajCbiWr5CMHnPxGk,10184
skimage/morphology/gray.py,sha256=Iv5QXIXhuhGER8WRhNlkMIzlWBMtYVYLHKx3dYaXmZo,17389
skimage/morphology/grayreconstruct.py,sha256=312pLbyMdCg1eJ7wV-pKYhhx5RTXGa7OTF-Rql-cOEM,9586
skimage/morphology/grey.py,sha256=YditCKMH6Vm2T7b0v44uXMrRsxNEuabVfIrHhjzHmW8,400
skimage/morphology/greyreconstruct.py,sha256=VVAueviL7Hx5ZqU_bY2qm8UxVYFCeb8AfLZC9Vs4Zzw,246
skimage/morphology/max_tree.py,sha256=5Qx0BdhRaVfI4Ga2KkIWkL1mi8rBldimonjQ41R2dfA,27910
skimage/morphology/misc.py,sha256=WLhDx0H8wSBOI6997tV6U-JhnW79dTg2M448Q008B70,8706
skimage/morphology/selem.py,sha256=Xcz-oM5AD1APuEg7MYeR_aRdnlrfFXHJifvu3ISDdqA,376
skimage/morphology/setup.py,sha256=8oA3BRZJoXUmG36Fcv21oaxd9ISUrsJWOHtIgUPhMz4,2318
skimage/morphology/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/morphology/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_binary.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_convex_hull.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_extrema.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_flood_fill.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_footprints.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_gray.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_max_tree.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_misc.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_reconstruction.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_skeletonize.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_skeletonize_3d.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_util.cpython-310.pyc,,
skimage/morphology/tests/test_binary.py,sha256=1bql-DvvfS4qgEBa0J7B-rb6i__76w8Jpw2UpX7ssiY,6509
skimage/morphology/tests/test_convex_hull.py,sha256=rXHdTKSxVkaX7O0PAXaqTn5f4bJ0Tfydt9N-wdEDVfo,6708
skimage/morphology/tests/test_extrema.py,sha256=iaSqRZYKZfWJj5VO-454APuGsCbxfjYd0sIORpjIPws,27885
skimage/morphology/tests/test_flood_fill.py,sha256=pkW3-ts9CG54koQIcqMfAeUKEZQr6dt0OhPe9yJCPhk,8465
skimage/morphology/tests/test_footprints.py,sha256=Qj_7zzJ7u1YzhO1KMI5eQucr8O3ZX0QiQXTQHWDYgvc,6822
skimage/morphology/tests/test_gray.py,sha256=vN7RJCbMq4b3Lr5uH50s7ym2AOwKjfeI4GB94CPglfA,11063
skimage/morphology/tests/test_max_tree.py,sha256=RRBhwDmD7b1XLwJ00dif4Y-PQtuaBTam5ZytjrLjgn0,23110
skimage/morphology/tests/test_misc.py,sha256=fe5hQYqBOjdFdo4UkNNorfvbwx-GZvAOjWabDvd27oU,9713
skimage/morphology/tests/test_reconstruction.py,sha256=M1MMaZuV2sakeqEsYJDDE9XmNyrD6B9VnnD7xfhUWHs,5774
skimage/morphology/tests/test_skeletonize.py,sha256=-wdRnCZV0VwlhYrAqieWrAFkwCkfeSGDhdfd6q208mA,9437
skimage/morphology/tests/test_skeletonize_3d.py,sha256=bzYW2FCp8iu9Tm7XS6Vsa1iGbfeTqmFuTuImQyJaHRM,6733
skimage/morphology/tests/test_util.py,sha256=9cZConH49ITm1ChO1PHPqbEbXi57jmtXIH4fzJP40YU,4651
skimage/registration/__init__.py,sha256=hh6AGsP_8YAyLvgU4FX1IQmhd18wgrZGoU2CdHiBs4Y,231
skimage/registration/__pycache__/__init__.cpython-310.pyc,,
skimage/registration/__pycache__/_masked_phase_cross_correlation.cpython-310.pyc,,
skimage/registration/__pycache__/_optical_flow.cpython-310.pyc,,
skimage/registration/__pycache__/_optical_flow_utils.cpython-310.pyc,,
skimage/registration/__pycache__/_phase_cross_correlation.cpython-310.pyc,,
skimage/registration/_masked_phase_cross_correlation.py,sha256=yEM3SoDAeFQgslI-kHDDGU7Mz8pB5p-Kv3mU9sORjhI,12723
skimage/registration/_optical_flow.py,sha256=U9-3IaopYRHxs4x8aRtStdXdErMr7kvVA_fpY3ejS4U,14892
skimage/registration/_optical_flow_utils.py,sha256=qcVU3EZCKB4I1v6S2JW67wi0EbbHz5VMznRByuaSgsU,3966
skimage/registration/_phase_cross_correlation.py,sha256=GjT6crf1ogiVsa2ipAnMXHDccbN8znK_ee1gG2HdSpU,14318
skimage/registration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/registration/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/registration/tests/__pycache__/test_ilk.cpython-310.pyc,,
skimage/registration/tests/__pycache__/test_masked_phase_cross_correlation.cpython-310.pyc,,
skimage/registration/tests/__pycache__/test_phase_cross_correlation.cpython-310.pyc,,
skimage/registration/tests/__pycache__/test_tvl1.cpython-310.pyc,,
skimage/registration/tests/test_ilk.py,sha256=Hc-6nmr0X_iQXOLvIzJtKzvrc11ISS9BslezurgqX9I,3221
skimage/registration/tests/test_masked_phase_cross_correlation.py,sha256=iGn_Yh5AEqa6aYzA-RDlY5jLaZFkAQ92Ft_ypXI0-3A,10359
skimage/registration/tests/test_phase_cross_correlation.py,sha256=VA49j7d4b2DKKqi5soE-YG87o3JJZYCmJtTPtVYgVuA,7066
skimage/registration/tests/test_tvl1.py,sha256=T5qKW4jtVwgQ9rKhc0Z_py57T9mQhq8jhIsrxQCeUHI,3671
skimage/restoration/__init__.py,sha256=fd5uLFHs7Hv3hznB6DXBhPPAHh_DkPRXBcuXcTsXWfg,1040
skimage/restoration/__pycache__/__init__.cpython-310.pyc,,
skimage/restoration/__pycache__/_cycle_spin.cpython-310.pyc,,
skimage/restoration/__pycache__/_denoise.cpython-310.pyc,,
skimage/restoration/__pycache__/deconvolution.cpython-310.pyc,,
skimage/restoration/__pycache__/inpaint.cpython-310.pyc,,
skimage/restoration/__pycache__/j_invariant.cpython-310.pyc,,
skimage/restoration/__pycache__/non_local_means.cpython-310.pyc,,
skimage/restoration/__pycache__/rolling_ball.cpython-310.pyc,,
skimage/restoration/__pycache__/setup.cpython-310.pyc,,
skimage/restoration/__pycache__/uft.cpython-310.pyc,,
skimage/restoration/__pycache__/unwrap.cpython-310.pyc,,
skimage/restoration/_cycle_spin.py,sha256=bXZoKn0kbGGLkPaygVTMuyRANclnLobxokBXTIQIKCk,6279
skimage/restoration/_denoise.py,sha256=BlIw-cWRDVKSlXRqZSPdkMG2mZfMguh-oeXe2z43Uog,41778
skimage/restoration/_denoise_cy.cp310-win_amd64.pyd,sha256=D2A72zCtOTBm114Hp1QV7lDqNBwBkMVUwX74tl8AIrg,202240
skimage/restoration/_denoise_cy.pyx,sha256=LKIYCfnSaiUtrN31qXaBTkGwXDwGUTxEOhYMpjx3Ing,7880
skimage/restoration/_inpaint.cp310-win_amd64.pyd,sha256=NzsR3edCTQWNpifcqm74qclQ7Tx619iDW1oxCgrg_-I,159232
skimage/restoration/_inpaint.pyx,sha256=T3I0bY7aBTlzZc9son7E5QkkrWlksN8amW_THnwxbgA,1972
skimage/restoration/_nl_means_denoising.cp310-win_amd64.pyd,sha256=ekNGUYyaCMV-iTtm-Er6j4i3TTbRIV-L0NjWqYZFWgE,359936
skimage/restoration/_nl_means_denoising.pyx,sha256=YHxeKSGtvC0odEkyvi__8eUwOyxw1ElNWqQ6ogb8HTU,48054
skimage/restoration/_rolling_ball_cy.cp310-win_amd64.pyd,sha256=bvPunWeoZlf9JKyNLTeSch_pRPUx9_O_trHwBkdnyoo,186368
skimage/restoration/_rolling_ball_cy.pyx,sha256=_yvMzJOvp4O6BAhJhaLJuC1uYrNgCgLOxAsG_7ygkMY,8951
skimage/restoration/_unwrap_1d.cp310-win_amd64.pyd,sha256=q-5iGeiyKKzZS4eOWk2YAY-FCQrLupx-CQTW986k2xE,116736
skimage/restoration/_unwrap_1d.pyx,sha256=8xTd-h4B0Io9TNJ06lKQSGlM1kU2QPnVJ_d5qfVvLTk,692
skimage/restoration/_unwrap_2d.cp310-win_amd64.pyd,sha256=6w57_ih-MYUhTpZmSdPlryEML-FwRqeX_6RHuGKWiJg,127488
skimage/restoration/_unwrap_2d.pyx,sha256=suPHJo6gfJXW2BwA8cC9fNFv4Y5CxgqhD2YY-tTCQ2Y,1193
skimage/restoration/_unwrap_3d.cp310-win_amd64.pyd,sha256=4r6xOGufQlhGzh4IKt1WAc6xfH8zHKMNPZptQUz3XH0,139776
skimage/restoration/_unwrap_3d.pyx,sha256=NEZlKqzApmzio-oqGlXMhUjeH0xG59HoRB9Dbgm8yWA,1346
skimage/restoration/deconvolution.py,sha256=O9068Xyw0FqqomOexqWd6ObOsQRLhkXCm4hwl0J7XjQ,17155
skimage/restoration/inpaint.py,sha256=zbJdt91yT7qJzRFFLgETbJsjBZ91-pR5cMqeA9AT3XQ,13546
skimage/restoration/j_invariant.py,sha256=fI-lHvBwE16fsmVX_llxTj6Ia62uSR9PDHGQK4lFXjE,11443
skimage/restoration/non_local_means.py,sha256=oJu-KG1ycmJfCIvPeUy032dbmTHS62sYlLTo92gqp1A,8354
skimage/restoration/rolling_ball.py,sha256=RSS-vdrKRgOaGVciMAawdIEk0zHcFqqFPfS_7t8sRGk,6562
skimage/restoration/setup.py,sha256=xq1hUcZyV86G07CooDAulXEC_4PZFCOX0Wmi_KTFfOo,2225
skimage/restoration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/restoration/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_denoise.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_inpaint.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_j_invariant.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_restoration.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_rolling_ball.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_unwrap.cpython-310.pyc,,
skimage/restoration/tests/test_denoise.py,sha256=KfRF_ubcTXIwbBMEBAAMUCeGsugLvsFuEqMz94aybhI,50305
skimage/restoration/tests/test_inpaint.py,sha256=N440chmTxDsf_TS1lI7bkW9tHP8kJAShBbsE5EwfDF0,7925
skimage/restoration/tests/test_j_invariant.py,sha256=N2cLJn_NfxNZhuqd4eE045SN52Mp2FX4bWUD7QFsFJM,3721
skimage/restoration/tests/test_restoration.py,sha256=OwjxsHpBxVlsF625T6vkX_ScCiOKWjnwMdFzs0EOVUM,6765
skimage/restoration/tests/test_rolling_ball.py,sha256=KtzOK6zn5CexPXxX8K8ko6HBPcf8Hl8L6i9XShrfImY,3237
skimage/restoration/tests/test_unwrap.py,sha256=Nfv8HCmqVICS0LTn5GlMc7krmiAXBsoI6w5yoqQCaBU,8683
skimage/restoration/uft.py,sha256=vxWmdnUUUzRpaAamZp4VuEXLbDF05Mdw1o1cDIEvcXU,13215
skimage/restoration/unwrap.py,sha256=cgJKaXIJGfLaQQnutsfhDgZUKoejYBaJsxeVk_dqb_0,4842
skimage/scripts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/scripts/__pycache__/__init__.cpython-310.pyc,,
skimage/scripts/__pycache__/skivi.cpython-310.pyc,,
skimage/scripts/skivi.py,sha256=4QVo2EZKNr744EYxv5BuskTeZPkIiN1JiAJgs8Pecys,539
skimage/segmentation/__init__.py,sha256=PAJnuXPJntH6hDUu0tcKSMLmcvMTeREf-8TeWhp-7pA,1244
skimage/segmentation/__pycache__/__init__.cpython-310.pyc,,
skimage/segmentation/__pycache__/_chan_vese.cpython-310.pyc,,
skimage/segmentation/__pycache__/_clear_border.cpython-310.pyc,,
skimage/segmentation/__pycache__/_expand_labels.cpython-310.pyc,,
skimage/segmentation/__pycache__/_felzenszwalb.cpython-310.pyc,,
skimage/segmentation/__pycache__/_join.cpython-310.pyc,,
skimage/segmentation/__pycache__/_quickshift.cpython-310.pyc,,
skimage/segmentation/__pycache__/_watershed.cpython-310.pyc,,
skimage/segmentation/__pycache__/active_contour_model.cpython-310.pyc,,
skimage/segmentation/__pycache__/boundaries.cpython-310.pyc,,
skimage/segmentation/__pycache__/morphsnakes.cpython-310.pyc,,
skimage/segmentation/__pycache__/random_walker_segmentation.cpython-310.pyc,,
skimage/segmentation/__pycache__/setup.cpython-310.pyc,,
skimage/segmentation/__pycache__/slic_superpixels.cpython-310.pyc,,
skimage/segmentation/_chan_vese.py,sha256=Chxy8Byh_AXj1m4jiVSDh584TktlcO-AT9r7zru2FFk,13224
skimage/segmentation/_clear_border.py,sha256=Jh1ec-A7kG-Knf1s2s-C86jvRLKPbSv04i9qWhvNIEo,4568
skimage/segmentation/_expand_labels.py,sha256=qQ_KaNryyCu8RZzq7-7HB_vQQ5guZsBJegFSgVb6uig,3970
skimage/segmentation/_felzenszwalb.py,sha256=5GkmDo8WOKAPsG8pnM32ehR4j_aW0-EO9gLJjGmq7Ws,3003
skimage/segmentation/_felzenszwalb_cy.cp310-win_amd64.pyd,sha256=VIejPwEJ2Xls_2H-SPhmYTnsXYPe0sHUu_FY_Rwn6jA,78336
skimage/segmentation/_felzenszwalb_cy.pyx,sha256=08lr4jLREABQJ-ZkEKXh-lQvx0lelMyJmUdNnjPWtFA,6120
skimage/segmentation/_join.py,sha256=O7UvMSF8zCvU45grSxtzb6l_k2bhVv-LkyIilZ-seuw,5986
skimage/segmentation/_quickshift.py,sha256=LYhl9KpL3BVFko9jV1wdbM_cwPLT-n1pYlRTdv66d1o,3312
skimage/segmentation/_quickshift_cy.cp310-win_amd64.pyd,sha256=HThTCbCmd7_LOoPAW3XfakHNpLTluYqBYPQbLP0W5Mg,179200
skimage/segmentation/_quickshift_cy.pyx,sha256=jS-01w_eztN0LJzxqxVy1zW5JH07ntBG0LC79CKA9HQ,6277
skimage/segmentation/_slic.cp310-win_amd64.pyd,sha256=btRG66xF_FduCwU4ZJYwtHQmTLX2yT_336ilTpehUDQ,196608
skimage/segmentation/_slic.pyx,sha256=9-rYWqVr4LzHOmhPuf7uhcTaAee6XvKV9-vSEQhkS-k,13897
skimage/segmentation/_watershed.py,sha256=XKzokaYQ_J7gyPMMIeqZ6PRIEXTsvD9zUlA7MuEoLTg,9687
skimage/segmentation/_watershed_cy.cp310-win_amd64.pyd,sha256=iEje-PI1J47HwJfzwsF-NCZ34ygypMSTlSVWRXZhTJI,131072
skimage/segmentation/_watershed_cy.pyx,sha256=wRtuFOw__YTIBdygTE9gHDdwsx625_MRSWKuluJlz2Y,7932
skimage/segmentation/active_contour_model.py,sha256=zTj1-vt2O9dzDuKbIitxiP_S1o3bvQvQtPzHsNDpkWo,8753
skimage/segmentation/boundaries.py,sha256=Ieq_Tc4f-2KHl66xp_Q9Kv_A9_lqrqiwMUJF5t0kuaI,10395
skimage/segmentation/heap_general.pxi,sha256=mKBNl6QG5KiD7J2i6r67dQk6wQ1AxpC8Y5ET_XHgElA,4063
skimage/segmentation/heap_watershed.pxi,sha256=oae3jEkbD_6_QlekuxIz5_G3y_EIYH05-OoMD0DDt8s,614
skimage/segmentation/morphsnakes.py,sha256=f2er3lZSwKDn4VTjXm0lzbUsRuKhkoXYJKCHfOYv1PQ,15804
skimage/segmentation/random_walker_segmentation.py,sha256=MaKGI3tMxyvhtZDr0hdCzrSCCCrjem8i8mbstyxeGvw,21174
skimage/segmentation/setup.py,sha256=FDlvAbC0oqA4QnIoIMRtrdYGk3vMI2BHOsPnfb-k8gM,1486
skimage/segmentation/slic_superpixels.py,sha256=B7AXL9rHaer_z3kPqo0UP-YkOV7b29hsUlpJyImP4wk,15728
skimage/segmentation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/segmentation/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_active_contour_model.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_boundaries.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_chan_vese.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_clear_border.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_expand_labels.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_felzenszwalb.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_join.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_morphsnakes.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_quickshift.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_random_walker.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_slic.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_watershed.cpython-310.pyc,,
skimage/segmentation/tests/test_active_contour_model.py,sha256=_Rt99_ZipEIillUilylhdXBjjLP7-FrzWDtEqW_Xhcc,6688
skimage/segmentation/tests/test_boundaries.py,sha256=Ac7uedH_O2of5H-y0DpUjW-sZhYkp-jVXAwj-qURKws,5611
skimage/segmentation/tests/test_chan_vese.py,sha256=FDND4EQJfqHO8gVYv2xo8UK2VD4ZhPV-Km266TcLMPo,3770
skimage/segmentation/tests/test_clear_border.py,sha256=_FZNz-tbG649bpQDSIHKa_eDKyKG6ZxRWPkKAsjCTG8,6709
skimage/segmentation/tests/test_expand_labels.py,sha256=w8M6y7zBSoVxq1EHHeEHumBbKo_DUfDZeQJ3PKQxsug,6408
skimage/segmentation/tests/test_felzenszwalb.py,sha256=PfTHpKQKwEMspIhS-m8Giisyr80Nx-RdsF8RwrinaD0,3364
skimage/segmentation/tests/test_join.py,sha256=9Q97CKNoDpX70QvnQlMHsbozT5YqRCpzxfddbeg2z_c,7425
skimage/segmentation/tests/test_morphsnakes.py,sha256=srCtr3Qh0drW0mxAJrWbpoIq0C20GKrFHipBnC0XTvI,6094
skimage/segmentation/tests/test_quickshift.py,sha256=LC3TrhVHzuNi8ON3m-fGXtCjiEZYLjxJSEV6TuTQSQI,2183
skimage/segmentation/tests/test_random_walker.py,sha256=I_a2Zvt7vrJsoTs4UsZKhwbvqbQoWJfeO-BATNY3AaM,22942
skimage/segmentation/tests/test_slic.py,sha256=IHD6xo5hSM5HcW5KU7k34o8CcqqUZvp8o5x8cM0M67E,19501
skimage/segmentation/tests/test_watershed.py,sha256=0jeilVYLpL5t5jxavGlw7LQOY1CwVXJ1iMVBb8J7120,24682
skimage/setup.py,sha256=WM-sSZqtRXxSOtT9VA8aN-p-ptGiMOkv9Dso9oBc8oc,990
skimage/transform/__init__.py,sha256=h47muHF3fk9mj1wmLCVJ8y-uX9t0eRRue_jVm-xil7g,2187
skimage/transform/__pycache__/__init__.cpython-310.pyc,,
skimage/transform/__pycache__/_geometric.cpython-310.pyc,,
skimage/transform/__pycache__/_warps.cpython-310.pyc,,
skimage/transform/__pycache__/finite_radon_transform.cpython-310.pyc,,
skimage/transform/__pycache__/hough_transform.cpython-310.pyc,,
skimage/transform/__pycache__/integral.cpython-310.pyc,,
skimage/transform/__pycache__/pyramids.cpython-310.pyc,,
skimage/transform/__pycache__/radon_transform.cpython-310.pyc,,
skimage/transform/__pycache__/setup.cpython-310.pyc,,
skimage/transform/_geometric.py,sha256=S-MSq-Dd1ZnyPzyhJmbNZ-aMNxMvSJArXzz4kNs2vb4,55250
skimage/transform/_hough_transform.cp310-win_amd64.pyd,sha256=TbceUgC26lHWHUvEia1NDv-OeDeq5bkAKV-5ZM4hkc0,197120
skimage/transform/_hough_transform.pyx,sha256=XkZYAeG68-r0hi7dWKmHu53UTB5tjPmQxfYvND38rEc,19906
skimage/transform/_radon_transform.cp310-win_amd64.pyd,sha256=1izEYxprAnFxjl_FgRnrzh-TIkfVKOEXp0P2R0mSoOw,162304
skimage/transform/_radon_transform.pyx,sha256=89aLo59kRzrcEKNTFkPMYavfRuj-b4VFJzJUmVAx6O8,8544
skimage/transform/_warps.py,sha256=pLrqNoxBZXz_lCWPKHfyMpAEeYmKUh2ml3x_ZGxRWz4,52290
skimage/transform/_warps_cy.cp310-win_amd64.pyd,sha256=D8Mn1gRtDt-dd33ybyzKb6zFl7VqOdfFIARL38YIGqw,170496
skimage/transform/_warps_cy.pyx,sha256=AfG6R-5389lmQJmQLmntEhT5vV82SMaAzgP9f70ZdO8,6420
skimage/transform/finite_radon_transform.py,sha256=TsF8Yf6NAyvMx7j001owppc_TSNs2AkmCz_G6xTeTxM,3362
skimage/transform/hough_transform.py,sha256=JTL2WGrb0sz6QY5QZkmABTZxYyMEyvuAyspl2WE_sos,15671
skimage/transform/integral.py,sha256=RIyPoZ-tIpPmalaORa-5W9Nx7E0FzPiLvBhDDdfgQAg,5141
skimage/transform/pyramids.py,sha256=Q79msVwBwvCRBdMnODpJchlMAZZLSLNftgsVw609r1E,14911
skimage/transform/radon_transform.py,sha256=I6LuOjULG7QlJKOxngwfohC-N605wjLOfMqTd77lS8w,21398
skimage/transform/setup.py,sha256=BUgRFLfGC7Zk2Kij-XeM9sIlxlpnfyMK4vn3C_xP6EU,1393
skimage/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/transform/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_finite_radon_transform.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_geometric.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_hough_transform.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_integral.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_pyramids.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_radon_transform.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_warps.cpython-310.pyc,,
skimage/transform/tests/test_finite_radon_transform.py,sha256=ZNeiCPF_QpmfipLBeyHruQAgDVa_w2uOra9l-tgVVTA,329
skimage/transform/tests/test_geometric.py,sha256=kWSZMpog3gwh6Zuu8V-M_e9W77E7599-NlYE2Asqm1A,27900
skimage/transform/tests/test_hough_transform.py,sha256=cwIyZfPZ5uxacblN_sweQpYENlTWxRcGFFTdQGNmFEs,20298
skimage/transform/tests/test_integral.py,sha256=1epSRdeIfluEMT-S0PGHvTxg0nMD5q4qOQUPZo2s3mc,2442
skimage/transform/tests/test_pyramids.py,sha256=fK2eMmbuHr8wJ4qjRCBbpHHv1DlhyHRJ2TZC1h25tQk,11414
skimage/transform/tests/test_radon_transform.py,sha256=Q81J_zYFH3Qp-X3X1scni-YaOQHTxmhPgy32dTL8T5c,18962
skimage/transform/tests/test_warps.py,sha256=bWoLoPOiy3ykvzBVW0etRFJRqUWCl_bYU0l2cwjcwEE,34795
skimage/util/__init__.py,sha256=13UFtr-TXiMb1Gm5Ai1WuE51SfyYNf06hTdR4BKQZ1U,1238
skimage/util/__pycache__/__init__.cpython-310.pyc,,
skimage/util/__pycache__/_invert.cpython-310.pyc,,
skimage/util/__pycache__/_label.cpython-310.pyc,,
skimage/util/__pycache__/_map_array.cpython-310.pyc,,
skimage/util/__pycache__/_montage.cpython-310.pyc,,
skimage/util/__pycache__/_regular_grid.cpython-310.pyc,,
skimage/util/__pycache__/apply_parallel.cpython-310.pyc,,
skimage/util/__pycache__/arraycrop.cpython-310.pyc,,
skimage/util/__pycache__/compare.cpython-310.pyc,,
skimage/util/__pycache__/dtype.cpython-310.pyc,,
skimage/util/__pycache__/lookfor.cpython-310.pyc,,
skimage/util/__pycache__/noise.cpython-310.pyc,,
skimage/util/__pycache__/setup.cpython-310.pyc,,
skimage/util/__pycache__/shape.cpython-310.pyc,,
skimage/util/__pycache__/unique.cpython-310.pyc,,
skimage/util/_invert.py,sha256=T5-15Tcgo4lr_vKiS_t1VzellymN2FKHoFyBkRMr5pg,2634
skimage/util/_label.py,sha256=jX0i2nwgGOpql7MAZceUhxuR_QpOQ9iakNjzyUNHwJw,1645
skimage/util/_map_array.py,sha256=a6sMz9rj21fU9W9HN3er4SQ3tmhjjRM7F7iEGlwLacA,6537
skimage/util/_montage.py,sha256=xQqvsxIiauwT65ZQPut4NfpiVXnopIFlq9lsc_UBpLU,5197
skimage/util/_regular_grid.py,sha256=6xaWjcveYYeo59zJl9oL-WL3gq0X3tgU3P0muXQtVpE,4101
skimage/util/_remap.cp310-win_amd64.pyd,sha256=vCMD1DG-QQFo4OltFp_HcjcuJMYpuwjWTul5t0y8vRc,469504
skimage/util/_remap.pyx,sha256=dSX17lNU5HQ0uN7n6far__tFs0JGtXwoEpg7Ane3jao,958
skimage/util/apply_parallel.py,sha256=eV_sGy3KcH3RFTWR7LgR55a7QH0cdkS-JXH002tRZmE,8541
skimage/util/arraycrop.py,sha256=DdHUK9T830g3tfKe1OfJlLByPy0egjRwQ_G1MwiNXks,2578
skimage/util/compare.py,sha256=FKMPvVdTuQH6c2UKaEAyyLm44l2TiWRiFNMRqE0JwmE,2078
skimage/util/dtype.py,sha256=sxfQgZzLBJuOjjea1Vo7aLDCuCwC4Wc_y6KCSd1aFTE,18815
skimage/util/lookfor.py,sha256=qjLCpmpgsuZXFHjMR8qJa1PD_KebrRh53qGa8PlVPm4,625
skimage/util/noise.py,sha256=hkGgyHGjsPZ_bwUySGOi3OBOB6aOTtEXCnCut4dR3AE,8933
skimage/util/setup.py,sha256=SRUUGuEWICNUIS5m0Ukm7ZR3Hx5EePKhkhDY-dAQRk4,1206
skimage/util/shape.py,sha256=VgYgSY58rd4eWPxhKlbiLs18nEYqfx_h6cHcWNfJiuE,8086
skimage/util/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/util/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_apply_parallel.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_arraycrop.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_compare.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_dtype.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_invert.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_labels.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_map_array.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_montage.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_random_noise.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_regular_grid.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_shape.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_unique_rows.cpython-310.pyc,,
skimage/util/tests/test_apply_parallel.py,sha256=G-d3Uw79NSZmbTYU0P1H8dn_FdiljmraoJVAIpJmeW8,5341
skimage/util/tests/test_arraycrop.py,sha256=WAoSg7V0f17CRTx6y4p6GX1wsAJof4QIXCnDSOb6-7c,1923
skimage/util/tests/test_compare.py,sha256=2ijlkFG58CXJJq6JmGcR3S6iUJATwUAI97e6ND3VrCA,2395
skimage/util/tests/test_dtype.py,sha256=9TJQDv9KEqI0dfDLl7MzaqPjHHTGEcNEHLa2lY5w2aw,6389
skimage/util/tests/test_invert.py,sha256=OU0HBLhqVFBwXTrqSeLke9c5pv63ZszorBj19ihs350,2522
skimage/util/tests/test_labels.py,sha256=Ekg9TSIvnlaa1dROfzycw4Z1c-YVB9Y9_3uqRDrGu5k,2277
skimage/util/tests/test_map_array.py,sha256=7lrKMUrg3zAiPx1It4HPTSrB9bRd7W_CLZBLKNM7gxM,1889
skimage/util/tests/test_montage.py,sha256=T8vjwLLVT_VeWZPsuHYMvoLERoZeitwhQIQStCMOYZ0,6157
skimage/util/tests/test_random_noise.py,sha256=yx8YySwY7iWIUjJiZCFBCJK_y5CV_3Y3oHnmxaLXhTU,8399
skimage/util/tests/test_regular_grid.py,sha256=F-miSdkH1BGqYHhzHMpv_QpEOUFSyvYc6R_CZ2o6Ww0,1023
skimage/util/tests/test_shape.py,sha256=huidc3EmeZKPfQilSt-LihHTqw8jUwAg8m-G_HhoxyY,5683
skimage/util/tests/test_unique_rows.py,sha256=eZB3TEy0WoHAhXFlqdB7pzW0yshACyFfj2RubAkv11A,1156
skimage/util/unique.py,sha256=FqQtB1uZlXrcXmQmAF5vMa6czd5WoWNtzt1RKMMmw5Q,1556
skimage/viewer/__init__.py,sha256=QRCKkJzLL_47yZP69pAyrwYNv2f4CPU8fEuvpWAOOp0,525
skimage/viewer/__pycache__/__init__.cpython-310.pyc,,
skimage/viewer/__pycache__/qt.cpython-310.pyc,,
skimage/viewer/canvastools/__init__.py,sha256=t2HSuptp8ykATxK4eO58UwohjZiA6qhPwrLVumc9HXo,118
skimage/viewer/canvastools/__pycache__/__init__.cpython-310.pyc,,
skimage/viewer/canvastools/__pycache__/base.cpython-310.pyc,,
skimage/viewer/canvastools/__pycache__/linetool.cpython-310.pyc,,
skimage/viewer/canvastools/__pycache__/painttool.cpython-310.pyc,,
skimage/viewer/canvastools/__pycache__/recttool.cpython-310.pyc,,
skimage/viewer/canvastools/base.py,sha256=UKiP04JzZcEHUxcbGuljQ3uykEd0M5BpHJ8KIMH1sHg,4012
skimage/viewer/canvastools/linetool.py,sha256=gjf8j-xEQRnMX4gP_ahvm4bxb4NxtbORy6PILztUJZI,7123
skimage/viewer/canvastools/painttool.py,sha256=KZHLxl05jc91iTXdWQNh6MO4ba4-lQ7QjWnWSWxqhJI,7718
skimage/viewer/canvastools/recttool.py,sha256=IIfB6jUtgWT2QhBz2pv75ggVB8EUBhr7zByTqJH-BfE,9131
skimage/viewer/plugins/__init__.py,sha256=w5v-AE_A2QIY-Ex96N6CyOYzl0Dm85vksZFRHndW6M8,312
skimage/viewer/plugins/__pycache__/__init__.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/base.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/canny.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/color_histogram.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/crop.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/labelplugin.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/lineprofile.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/measure.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/overlayplugin.cpython-310.pyc,,
skimage/viewer/plugins/__pycache__/plotplugin.cpython-310.pyc,,
skimage/viewer/plugins/base.py,sha256=Xp2bZOVlh8w5axsL2mC9b_7T8uP1YDOl5oA0DtlY1-g,9656
skimage/viewer/plugins/canny.py,sha256=Bnd0EikVGm7tK8DLMMn3wj73YjR_5zUjhTDzvZrrzUs,1304
skimage/viewer/plugins/color_histogram.py,sha256=tdQftXG3xfXyLWyO8e44DyBN57Nb8o3bpnbyO_DevzI,3388
skimage/viewer/plugins/crop.py,sha256=9wDpZXEuNvTqvsbaQEwuj0mO9dKeYQQBU44p5dkUCE4,1424
skimage/viewer/plugins/labelplugin.py,sha256=KF0W39yFtSjof9kl7cjFhDU0LmqaIQ4vxHrHS-8Mrkc,1943
skimage/viewer/plugins/lineprofile.py,sha256=0UUuv3qR750_btx4dFIbsTWZpXvKzNxC_Q3X703DTBc,6214
skimage/viewer/plugins/measure.py,sha256=Kqa7-QdEXJScOFrKhW5dPxBzJHD2dQ8v9_PUFBY-djo,1349
skimage/viewer/plugins/overlayplugin.py,sha256=b4BbSH7y8PB27S9tsK04vmjSVxUhFKIf10wqyrscRxg,3462
skimage/viewer/plugins/plotplugin.py,sha256=zhVig5gsS3OumKYgDoCSPaQ2EGQwlCr_cBE6wBCoO7g,2479
skimage/viewer/qt.py,sha256=ULbXnNoxAG6CcOaNiMEIqN5bujT7i0TPO7CqAQvxOXk,1325
skimage/viewer/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/viewer/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/viewer/tests/__pycache__/test_plugins.cpython-310.pyc,,
skimage/viewer/tests/__pycache__/test_tools.cpython-310.pyc,,
skimage/viewer/tests/__pycache__/test_utils.cpython-310.pyc,,
skimage/viewer/tests/__pycache__/test_viewer.cpython-310.pyc,,
skimage/viewer/tests/__pycache__/test_widgets.cpython-310.pyc,,
skimage/viewer/tests/test_plugins.py,sha256=8-C3QrM46yEbw9FxkMUMFpkKO8gnL6T_o7967VqyJUQ,5730
skimage/viewer/tests/test_tools.py,sha256=DFgIBlpsLB7PKXf3-RiLI8scv0-usoigMTwiUhyvTts,6207
skimage/viewer/tests/test_utils.py,sha256=iO5AKdS3rslZaAUFG0Pp_1dww0X1Z67oZKQy24VruEQ,1198
skimage/viewer/tests/test_viewer.py,sha256=PiW9cgqCze5os_rZqV8FkVGio6TmyDiZ-gNXYsUh8ew,2331
skimage/viewer/tests/test_widgets.py,sha256=HZ6b6bOBQtKAcCN-0TkFAOoRyr_hQmeRkJYp1yxBqpc,3478
skimage/viewer/utils/__init__.py,sha256=nE8jJjGJaKuJKCyuf6QLfrj8xPj8IqGKeH2VXP3FMzY,21
skimage/viewer/utils/__pycache__/__init__.cpython-310.pyc,,
skimage/viewer/utils/__pycache__/canvas.cpython-310.pyc,,
skimage/viewer/utils/__pycache__/core.cpython-310.pyc,,
skimage/viewer/utils/__pycache__/dialogs.cpython-310.pyc,,
skimage/viewer/utils/canvas.py,sha256=j12tWOmQ2XRORsind8i1VVvcbFKXDQ5Ys2Tc8KlyUdI,3230
skimage/viewer/utils/core.py,sha256=ErRWN0LkZq9A5SremXZnRCrQQt_8_ejKC2MOAZzWB5M,6757
skimage/viewer/utils/dialogs.py,sha256=eD5RUGoQgFEhpRV_7cd-YIRZHOmE8S7-OIjsIEbZb-4,972
skimage/viewer/viewers/__init__.py,sha256=yukIrnDgODxk4R98-ghFU5nHeUobxqNJfwTWJINM37Q,49
skimage/viewer/viewers/__pycache__/__init__.cpython-310.pyc,,
skimage/viewer/viewers/__pycache__/core.cpython-310.pyc,,
skimage/viewer/viewers/core.py,sha256=Tf0WJmVRNcW2c8xa6mbXSkEtB4nbuELeLLgorblo40g,14191
skimage/viewer/widgets/__init__.py,sha256=Sh153SvY_lmdHQTBF-YM9fSFz7diCs7_bcRhHPgV1p4,660
skimage/viewer/widgets/__pycache__/__init__.cpython-310.pyc,,
skimage/viewer/widgets/__pycache__/core.cpython-310.pyc,,
skimage/viewer/widgets/__pycache__/history.cpython-310.pyc,,
skimage/viewer/widgets/core.py,sha256=cw7iD_xWmno7YM0azRSOrIXf-xc3FfXNgGG3-XnAec4,10804
skimage/viewer/widgets/history.py,sha256=UnrU3VQUq5KqVBw-H7bmgo9qUEnxE8Xe3eo_nckYsiM,3473
