README for CUDA Compiler SDK Samples
====================================

Introduction
------------

Here are three simple samples illustrating the use of libNVVM.

simple
   reads in a NVVM IR program from a file, compiles it to PTX, and launches
   the program on GPU using CUDA driver APIs.

ptxgen
   is a standalone NVVM IR program to PTX compiler. It will link the 
   libDevice library with the input NVVM IR program, verify the IR for
   conformance to the NVVM IR specification, and then generate PTX.

cuda-c-linking
   builds an NVVM IR program using the LLVM IR build APIs, links the 
   generated PTX with a PTX generated by nvcc, and launches the linked
   program on GPU using CUDA driver APIs.
   


Steps of Building the Samples
-----------------------------

The following environment variables can be used to control the build
process for the samples.  If not specified, CUDA_HOME will be derived by
looking for nvcc in your PATH.

CUDA_HOME    : The directory where the CUDA toolkit is installed, 
               e.g., /usr/local/cuda.
LIBNVVM_HOME : The directory where libNVVM components are located. 
               e.g., $CUDA_HOME/nvvm. 
LLVM_HOME    : This should point to the install directory if you built llvm
               locally.  This is only required for building the cuda-c-linking
               sample (see the cuda-c-linking note below).

After setting the environment variables and adding the path to the cmake tool
in the PATH environment variable, sample script build.sh (for Linux) or
build.bat (for Windows) may be executed. This script will use build directory
"build" to build the samples, and then install them in the "install" directory.

If you chose to build using Visual Studio and its integrated CMake support,
then simply run "Build All" and "Install libnvvm-samples."  The installed
samples will be copied to out/install/<build architecture>/bin/


A Note About the cuda-c-linkng Sample
-------------------------------------
The LLVM_HOME environment variable is required for users who wish to build the
cuda-c-linking sample and have a locally built copy of LLVM that they wish to
use.  That sample requires the development package of LLVM with the LLVM header
files and libraries.  Windows users should download the LLVM 14 sources from
llvm.org and build+install LLVM locally.  Using the llvm.org provided Windows
installer lacks some of the required header files and libraries that the
cuda-c-linking sample depends on.  For Ubuntu users, the "llvm-dev" package
contains the LLVM headers and libraries cuda-c-linking requires.   Windows
users will want to build this sample using the same cmake build mode as they
built LLVM with.  For instance if they built LLVM in Release mode, then this
sample should also be built in release mode.  The build.bat can be updated to
reflect this: Add "-DCMAKE_BUILD_TYPE=Release" to the cmake invocation.


Building
--------
The included build.sh and build.bat scripts demonstrate how to build the
samples, the former for Linux and the latter for Windows.
