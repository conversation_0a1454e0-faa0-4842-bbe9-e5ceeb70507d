{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-cudart_win-64 12.8.90 0", "cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart-12.8.90-0", "files": ["Library/bin/cudart64_12.dll"], "fn": "cuda-cudart-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart-12.8.90-0", "type": 1}, "md5": "fbcfe7eb784563866c095b0a5c44ca55", "name": "cuda-cudart", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart-12.8.90-0.conda", "paths_data": {"paths": [{"_path": "Library/bin/cudart64_12.dll", "path_type": "hardlink", "sha256": "c2c9a9c22a9bcba90e261825968836787b331038047a26770cffb7a583c28344", "sha256_in_prefix": "c2c9a9c22a9bcba90e261825968836787b331038047a26770cffb7a583c28344", "size_in_bytes": 573952}], "paths_version": 1}, "requested_spec": "None", "sha256": "55de8f6966a1d71b069d416fde627db13c67a0a910886aa55bf7a0f5cf385408", "size": 161181, "subdir": "win-64", "timestamp": 1739448584000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-cudart-12.8.90-0.conda", "version": "12.8.90"}