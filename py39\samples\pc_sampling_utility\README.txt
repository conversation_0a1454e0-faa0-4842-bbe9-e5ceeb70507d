
1. Building the sample
    1.1 Change directory to pc_sampling_utility sample directory.
    1.2 Run make. The executable pc_sampling_utility should be created in the folder.
2. Usage
    2.1 This utility takes the pc sampling data file generated by the "pc_sampling_continuous" sample as input.  It prints the stall reason counter values for each GPU kernel at the assembly instruction level. It also does assembly to CUDA-C source correlation and shows the CUDA-C source file name and line number.
    2.2 For source correlation CUDA cubins are needed. These can be extracted from the CUDA application executable or library files using "cuobjdump -xelf all <exectable/library>". Note that the "cuobjdump" utility version should be same as the CUDA Toolkit version used to build the CUDA application executable or library files.
    2.3 The extracted cubin files should be renamed as follows "1.cubin",  "2.cubin",  ...
    2.4 See "pc_sampling_utility -help" for utility options.
