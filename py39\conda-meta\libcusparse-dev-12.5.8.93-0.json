{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "libcusparse 12.5.8.93 0", "libnvjitlink", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcusparse-dev-12.5.8.93-0", "files": ["Library/include/cusparse.h", "Library/include/cusparse_v2.h", "Library/lib/cusparse.lib"], "fn": "libcusparse-dev-12.5.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcusparse-dev-12.5.8.93-0", "type": 1}, "md5": "c8886d22eb9bd1a507f114d5f57c8ea0", "name": "libcusparse-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcusparse-dev-12.5.8.93-0.conda", "paths_data": {"paths": [{"_path": "Library/include/cusparse.h", "path_type": "hardlink", "sha256": "c0f72cdfb030010e289938caaa5a7884c61124d9de8ed3ecc440e61ad2ed8fcb", "sha256_in_prefix": "c0f72cdfb030010e289938caaa5a7884c61124d9de8ed3ecc440e61ad2ed8fcb", "size_in_bytes": 302926}, {"_path": "Library/include/cusparse_v2.h", "path_type": "hardlink", "sha256": "a7a7078e7a404315e0b68d0834edb87094b91129ea3e683dbb8009dbfc29697f", "sha256_in_prefix": "a7a7078e7a404315e0b68d0834edb87094b91129ea3e683dbb8009dbfc29697f", "size_in_bytes": 2641}, {"_path": "Library/lib/cusparse.lib", "path_type": "hardlink", "sha256": "7149422963c971d505832f27603bd8aaac0359e2187dc77c39737129d2a4a681", "sha256_in_prefix": "7149422963c971d505832f27603bd8aaac0359e2187dc77c39737129d2a4a681", "size_in_bytes": 108364}], "paths_version": 1}, "requested_spec": "None", "sha256": "53fa4a8ffe5534ac413f0f14dad8114955a05fbfac36798762cb4f878fb649cd", "size": 41124, "subdir": "win-64", "timestamp": 1740561258000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/libcusparse-dev-12.5.8.93-0.conda", "version": "12.5.8.93"}