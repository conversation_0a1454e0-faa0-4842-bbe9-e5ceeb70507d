{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-crt-tools 12.8.93 0", "cuda-nvvm-tools 12.8.93 0", "cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvcc-tools-12.8.93-0", "files": [], "fn": "cuda-nvcc-tools-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvcc-tools-12.8.93-0", "type": 1}, "md5": "997371588ccf2dd5c35e013717437d2a", "name": "cuda-nvcc-tools", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvcc-tools-12.8.93-0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "40e888a3812e25b62f38f67048a59eb0db9f491c3bdeb487fcca7f362cc39c50", "size": 16896, "subdir": "win-64", "timestamp": 1740205332000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-nvcc-tools-12.8.93-0.conda", "version": "12.8.93"}