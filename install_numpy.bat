@echo off
chcp 65001 > nul
echo 正在安装numpy以解决LLVM错误...

REM 设置MKL环境变量
set MKL_DEBUG_CPU_TYPE=5
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1
set MKL_THREADING_LAYER=INTEL
set KMP_DUPLICATE_LIB_OK=TRUE

REM 设置Python路径
set PYTHON_PATH=%cd%\py39
set PATH=%PYTHON_PATH%;%PYTHON_PATH%\Scripts;%PATH%

echo 当前Python环境: %PYTHON_PATH%
echo.

echo 检查当前numpy状态...
%PYTHON_PATH%\python.exe -c "try: import numpy; print('NumPy已安装，版本:', numpy.__version__); except ImportError: print('NumPy未安装')"
echo.

echo 尝试安装numpy...
%PYTHON_PATH%\python.exe -m pip install numpy

echo.
echo 验证numpy安装...
%PYTHON_PATH%\python.exe -c "try: import numpy; print('NumPy安装成功，版本:', numpy.__version__); except ImportError: print('NumPy安装失败')"

echo.
echo 检查MKL支持...
%PYTHON_PATH%\python.exe -c "try: import numpy; print('NumPy MKL信息:'); numpy.show_config(); except: print('无法获取MKL信息')"

pause
