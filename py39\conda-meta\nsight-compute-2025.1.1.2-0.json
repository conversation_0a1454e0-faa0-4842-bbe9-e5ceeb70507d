{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "expat >=2.6.4,<3.0a0", "fontconfig >=2.14.1,<3.0a0", "freetype >=2.12.1,<3.0a0", "libglib >=2.78.4,<3.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\nsight-compute-2025.1.1.2-0", "files": ["Library/nsight-compute/2025.1.1/docs/Archives/index.html", "Library/nsight-compute/2025.1.1/docs/CopyrightAndLicenses/index.html", "Library/nsight-compute/2025.1.1/docs/CustomizationGuide/index.html", "Library/nsight-compute/2025.1.1/docs/Notices/notices.html", "Library/nsight-compute/2025.1.1/docs/NsightCompute/index.html", "Library/nsight-compute/2025.1.1/docs/NsightComputeCli/index.html", "Library/nsight-compute/2025.1.1/docs/NvRulesAPI/index.html", "Library/nsight-compute/2025.1.1/docs/ProfilingGuide/index.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/index.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/gpu-support.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/known-issues.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/library-support-optix.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/library-support.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/platform-support.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/release-notes-older-versions.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/release-notes.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/support.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/system-requirements.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-3-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-3.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-4.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-5-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-5-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-5-3.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-5.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-1-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-1-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-2-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-3-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-3.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-1-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-3.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-4.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-5.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-6.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-7.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-8.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-9.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-3-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-3.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-1-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-2-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-3.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-4-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-4.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-1-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-2-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-2-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-3-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-3.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-1-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-2-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-3-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-3-2.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-3.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-4.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2025-1-1.html", "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2025-1.html", "Library/nsight-compute/2025.1.1/docs/Training/index.html", "Library/nsight-compute/2025.1.1/docs/VERSION", "Library/nsight-compute/2025.1.1/docs/_images/add-remote-connection-private-key.png", "Library/nsight-compute/2025.1.1/docs/_images/add-remote-connection.png", "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-cam.png", "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-display-filter.png", "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-open-button.png", "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-property-filter.png", "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-rendering-options.png", "Library/nsight-compute/2025.1.1/docs/_images/as-viewer.png", "Library/nsight-compute/2025.1.1/docs/_images/baselines-multiple.png", "Library/nsight-compute/2025.1.1/docs/_images/baselines-tool-window.png", "Library/nsight-compute/2025.1.1/docs/_images/baselines.png", "Library/nsight-compute/2025.1.1/docs/_images/connection-dialog.png", "Library/nsight-compute/2025.1.1/docs/_images/cubin-viewer.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-11.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-12.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-13.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-14.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-15.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-16.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-2.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-3.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-5.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-6.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-7.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-8.png", "Library/nsight-compute/2025.1.1/docs/_images/filter-example-9.png", "Library/nsight-compute/2025.1.1/docs/_images/green-contexts-details-page.png", "Library/nsight-compute/2025.1.1/docs/_images/green-contexts-resource-tool-window-with-tpc-mask.png", "Library/nsight-compute/2025.1.1/docs/_images/green-contexts-session-page.png", "Library/nsight-compute/2025.1.1/docs/_images/hw-model-l1tex-ga100-global.png", "Library/nsight-compute/2025.1.1/docs/_images/hw-model-l1tex.png", "Library/nsight-compute/2025.1.1/docs/_images/hw-model-lts-ga100.png", "Library/nsight-compute/2025.1.1/docs/_images/hw-model-lts.png", "Library/nsight-compute/2025.1.1/docs/_images/integration-1.png", "Library/nsight-compute/2025.1.1/docs/_images/integration-2.png", "Library/nsight-compute/2025.1.1/docs/_images/integration-3.png", "Library/nsight-compute/2025.1.1/docs/_images/integration-4.png", "Library/nsight-compute/2025.1.1/docs/_images/main-menu.png", "Library/nsight-compute/2025.1.1/docs/_images/memory-chart-a100.png", "Library/nsight-compute/2025.1.1/docs/_images/memory-peak-mapping.png", "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-dram.png", "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-l1.png", "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-l2-evict-policy.png", "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-l2.png", "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-smem.png", "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-activity.png", "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-from-header.png", "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-from-section.png", "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-gpu-data.png", "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-graphs.png", "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-tables.png", "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-utilization.png", "Library/nsight-compute/2025.1.1/docs/_images/options-profile.png", "Library/nsight-compute/2025.1.1/docs/_images/pmsampling-misaligned-passes.png", "Library/nsight-compute/2025.1.1/docs/_images/pmsampling-tensor-example.png", "Library/nsight-compute/2025.1.1/docs/_images/profile-series-action.png", "Library/nsight-compute/2025.1.1/docs/_images/profile-series-dialog.png", "Library/nsight-compute/2025.1.1/docs/_images/profiled-process.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-header-filter-dialog.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-header.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-callstack-python.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-callstack.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-details-comments.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-details-source-table.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-details-timeline.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-fix-column.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-inline-functions-table.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-nvtx.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-section-bodies.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-section-rooflines.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-section-with-rule.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-collapse.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-column-chooser.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-heatmap.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-markers-table.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-markers.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-navigate-by.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-profiles-button.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-profiles.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-register-dependencies.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-rel-abs.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-resolve.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-search.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-statistics.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-summary-rules.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-summary-table.png", "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-summary.png", "Library/nsight-compute/2025.1.1/docs/_images/progress-log.png", "Library/nsight-compute/2025.1.1/docs/_images/projects-explorer.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-baseline.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-api-stream.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-attach.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-connect.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-connected.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-next-launch.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-profiling-connect.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-profiling-options-sections.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-report-summary.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-report.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-rule.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-system-trace-connect.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-system-trace-options.png", "Library/nsight-compute/2025.1.1/docs/_images/quick-start-system-trace-timeline.png", "Library/nsight-compute/2025.1.1/docs/_images/regular-application-process.png", "Library/nsight-compute/2025.1.1/docs/_images/replay-application-kernel-matching.png", "Library/nsight-compute/2025.1.1/docs/_images/replay-application-range.png", "Library/nsight-compute/2025.1.1/docs/_images/replay-application.png", "Library/nsight-compute/2025.1.1/docs/_images/replay-kernel.png", "Library/nsight-compute/2025.1.1/docs/_images/replay-range.png", "Library/nsight-compute/2025.1.1/docs/_images/replay-regular-execution.png", "Library/nsight-compute/2025.1.1/docs/_images/roofline-analysis.png", "Library/nsight-compute/2025.1.1/docs/_images/roofline-overview.png", "Library/nsight-compute/2025.1.1/docs/_images/section-files-2.png", "Library/nsight-compute/2025.1.1/docs/_images/section-files.png", "Library/nsight-compute/2025.1.1/docs/_images/sm-selection-dialog.png", "Library/nsight-compute/2025.1.1/docs/_images/source-comparison-diff-by-menu.png", "Library/nsight-compute/2025.1.1/docs/_images/source-comparison-document.png", "Library/nsight-compute/2025.1.1/docs/_images/source-comparison-from-header.png", "Library/nsight-compute/2025.1.1/docs/_images/source-comparison-navigation-buttons.png", "Library/nsight-compute/2025.1.1/docs/_images/source-counters.png", "Library/nsight-compute/2025.1.1/docs/_images/status-banner.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-api-statistics.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-api-stream.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-baselines.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-callstack.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-launch-details.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-metric-details.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-nvtx-resources.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-nvtx.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-resources.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-section-sets.png", "Library/nsight-compute/2025.1.1/docs/_images/tool-window-sections.png", "Library/nsight-compute/2025.1.1/docs/_images/welcome-page.png", "Library/nsight-compute/2025.1.1/docs/_sphinx_design_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "Library/nsight-compute/2025.1.1/docs/_sphinx_design_static/design-tabs.js", "Library/nsight-compute/2025.1.1/docs/_static/NVIDIA-LogoBlack.svg", "Library/nsight-compute/2025.1.1/docs/_static/NVIDIA-LogoWhite.svg", "Library/nsight-compute/2025.1.1/docs/_static/api-styles-dark.css", "Library/nsight-compute/2025.1.1/docs/_static/api-styles.css", "Library/nsight-compute/2025.1.1/docs/_static/basic.css", "Library/nsight-compute/2025.1.1/docs/_static/css/badge_only.css", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/Roboto-Slab-Bold.woff", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/Roboto-Slab-Bold.woff2", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/Roboto-Slab-Regular.woff", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/Roboto-Slab-Regular.woff2", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.eot", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.svg", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.ttf", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.woff", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.woff2", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-bold-italic.woff", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-bold-italic.woff2", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-bold.woff", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-bold.woff2", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-normal-italic.woff", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-normal-italic.woff2", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-normal.woff", "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-normal.woff2", "Library/nsight-compute/2025.1.1/docs/_static/css/theme.css", "Library/nsight-compute/2025.1.1/docs/_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "Library/nsight-compute/2025.1.1/docs/_static/design-tabs.js", "Library/nsight-compute/2025.1.1/docs/_static/doctools.js", "Library/nsight-compute/2025.1.1/docs/_static/documentation_options.js", "Library/nsight-compute/2025.1.1/docs/_static/favicon.ico", "Library/nsight-compute/2025.1.1/docs/_static/file.png", "Library/nsight-compute/2025.1.1/docs/_static/jquery-3.5.1.js", "Library/nsight-compute/2025.1.1/docs/_static/jquery.js", "Library/nsight-compute/2025.1.1/docs/_static/js/badge_only.js", "Library/nsight-compute/2025.1.1/docs/_static/js/html5shiv-printshiv.min.js", "Library/nsight-compute/2025.1.1/docs/_static/js/html5shiv.min.js", "Library/nsight-compute/2025.1.1/docs/_static/js/theme.js", "Library/nsight-compute/2025.1.1/docs/_static/language_data.js", "Library/nsight-compute/2025.1.1/docs/_static/lunr.min.js", "Library/nsight-compute/2025.1.1/docs/_static/lunr_search.js", "Library/nsight-compute/2025.1.1/docs/_static/main_ov_logo_rect.png", "Library/nsight-compute/2025.1.1/docs/_static/main_ov_logo_square.png", "Library/nsight-compute/2025.1.1/docs/_static/mermaid-init.js", "Library/nsight-compute/2025.1.1/docs/_static/minus.png", "Library/nsight-compute/2025.1.1/docs/_static/nsight-compute.ico", "Library/nsight-compute/2025.1.1/docs/_static/nsight-compute.png", "Library/nsight-compute/2025.1.1/docs/_static/omni-style-dark.css", "Library/nsight-compute/2025.1.1/docs/_static/omni-style.css", "Library/nsight-compute/2025.1.1/docs/_static/plus.png", "Library/nsight-compute/2025.1.1/docs/_static/pygments.css", "Library/nsight-compute/2025.1.1/docs/_static/searchtools.js", "Library/nsight-compute/2025.1.1/docs/_static/social-media.js", "Library/nsight-compute/2025.1.1/docs/_static/theme-setter.js", "Library/nsight-compute/2025.1.1/docs/_static/theme-switcher-general.css", "Library/nsight-compute/2025.1.1/docs/_static/twemoji.css", "Library/nsight-compute/2025.1.1/docs/_static/twemoji.js", "Library/nsight-compute/2025.1.1/docs/_static/underscore-1.13.1.js", "Library/nsight-compute/2025.1.1/docs/_static/underscore.js", "Library/nsight-compute/2025.1.1/docs/_static/version.js", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IAction.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IBaseContext.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IContext.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IController.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IEvaluator.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IFrontend.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IMessageVault.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IMetric.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1INvtxDomainInfo.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1INvtxRange.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1INvtxState.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IRange.html", "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1ISourceInfo.html", "Library/nsight-compute/2025.1.1/docs/api/data-structures.html", "Library/nsight-compute/2025.1.1/docs/api/group__NVRULES__HW.html", "Library/nsight-compute/2025.1.1/docs/api/group__NVRULES__LW.html", "Library/nsight-compute/2025.1.1/docs/api/group__NVRULES__NM.html", "Library/nsight-compute/2025.1.1/docs/api/modules.html", "Library/nsight-compute/2025.1.1/docs/api/namespaceNV.html", "Library/nsight-compute/2025.1.1/docs/api/namespaceNV_1_1Rules.html", "Library/nsight-compute/2025.1.1/docs/api/namespaces.html", "Library/nsight-compute/2025.1.1/docs/genindex.html", "Library/nsight-compute/2025.1.1/docs/index.html", "Library/nsight-compute/2025.1.1/docs/objects.inv", "Library/nsight-compute/2025.1.1/docs/project.json", "Library/nsight-compute/2025.1.1/docs/search.html", "Library/nsight-compute/2025.1.1/docs/searchindex.js", "Library/nsight-compute/2025.1.1/extras/FileFormat/CpuStacktrace.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/Nvtx.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/NvtxCategories.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerMetricOptions.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerReport.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerReportCommon.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerResultsCommon.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerSection.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerStringTable.proto", "Library/nsight-compute/2025.1.1/extras/FileFormat/RuleResults.proto", "Library/nsight-compute/2025.1.1/extras/RuleTemplates/AdvancedRuleTemplate.py", "Library/nsight-compute/2025.1.1/extras/RuleTemplates/BasicKernelInfo.py", "Library/nsight-compute/2025.1.1/extras/RuleTemplates/BasicRuleTemplate.py", "Library/nsight-compute/2025.1.1/extras/RuleTemplates/RuleTemplate.section", "Library/nsight-compute/2025.1.1/extras/RuleTemplates/RuleTemplate2_table.chart", "Library/nsight-compute/2025.1.1/extras/RuleTemplates/RuleTemplate_bar.chart", "Library/nsight-compute/2025.1.1/extras/RuleTemplates/RuleTemplate_table.chart", "Library/nsight-compute/2025.1.1/extras/RuleTemplates/SpeedupWithFocusMetrics.py", "Library/nsight-compute/2025.1.1/extras/python/_ncu_report.pyd", "Library/nsight-compute/2025.1.1/extras/python/ncu_report.py", "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/README.TXT", "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/instructionMix.cu", "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/instructionMix.pdf", "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/sobelDouble.ncu-rep", "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/sobelFloat.ncu-rep", "Library/nsight-compute/2025.1.1/extras/samples/interKernelCommunication/README.TXT", "Library/nsight-compute/2025.1.1/extras/samples/interKernelCommunication/interKernelCommunication.cu", "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/README.TXT", "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/sharedBankConflicts.cu", "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/sharedBankConflicts.pdf", "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/transposeCoalesced.ncu-rep", "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/transposeNoBankConflicts.ncu-rep", "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/README.TXT", "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/addConstDouble.ncu-rep", "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/addConstDouble3.ncu-rep", "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/uncoalescedGlobalAccesses.cu", "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/uncoalescedGlobalAccesses.pdf", "Library/nsight-compute/2025.1.1/host/target-windows-x64/CudaGpuInfoDumper.exe", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ad10x-gfxt.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ad10x.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga100.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga10b.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga10x-gfxact.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga10x-gfxt.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga10x.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/gb10x.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/gb20x.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/gh100-ct.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/gh100.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/index.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/tu10x-gfxt.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/tu10x.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/tu11x.config", "Library/nsight-compute/2025.1.1/host/target-windows-x64/NsysVsIntegration.xml", "Library/nsight-compute/2025.1.1/host/target-windows-x64/PythonFunctionsTrace/annotations.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjection64.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionHelper64.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionPythonBacktrace64.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionPythonGilTracing64.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionPythonNvtxAnnotations64.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionWindowsHook64.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/atlas.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/bifrost.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/bifrost_loader.2.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/bifrost_plugin.2.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/config.ini", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_100.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_101.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_102.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_110.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_111.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_112.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_113.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_114.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_115.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_116.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_117.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_118.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_128.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/etw_providers_template.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/exporter/export_schema_version_notes.txt", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nsight-sys-service.exe", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nsys.exe", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvlog.config.template", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvperf_grfx_host.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvsym.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExt.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtCuda.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtCudaRt.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtOpenCL.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtSync.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtx3.hpp", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImpl.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCore.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCudaRt_v3.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCuda_v3.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplOpenCL_v3.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplSync_v3.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInit.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInitDecls.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInitDefs.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxLinkOnce.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxTypes.h", "Library/nsight-compute/2025.1.1/host/target-windows-x64/odin.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/openxr-layers/XR_APILAYER_NV_nsight-sys_windows.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/bin/python.exe", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/lib/gpustats.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/lib/kernel_helper.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/lib/nsysstats.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/Dockerfile", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/__init__.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/__main__.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/clean.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/data_service.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/format.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/install.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/__init__.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/args.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/collective_loader.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/cuda.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/data_reader.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/data_utils.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/exceptions.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/export.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/gpu_metrics.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/heatmap.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/helpers.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/network.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_display.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_path.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_pres.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nvtx.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/overlap.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/pace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/recipe.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/recipe_loader.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/summary.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/table_config.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/log.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/nsys_constants.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/pyproject.toml", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/cuda_api_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/analysis.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/cuda_api_sync.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_hist/cuda_gpu_kern_hist.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_hist/histogram.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_hist/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/cuda_gpu_kern_pace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/pace.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/cuda_gpu_kern_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/cuda_gpu_mem_size_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/cuda_gpu_mem_time_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/cuda_gpu_time_util_map.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/heatmap.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/analysis.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/cuda_memcpy_async.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/analysis.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/cuda_memcpy_sync.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/analysis.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/cuda_memset_sync.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/diff.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/diff.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/analysis.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/dx12_mem_ops.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/analysis.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/gpu_gaps.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/gpu_metric_util_map.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/heatmap.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/analysis.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/gpu_time_util.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/heatmap.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/mpi_gpu_time_util_map.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/mpi_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_overlap_trace/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_overlap_trace/nccl_gpu_overlap_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_overlap_trace/trace.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/nccl_gpu_proj_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/heatmap.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/nccl_gpu_time_util_map.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/nccl_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_map_aws/heatmap.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_map_aws/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_map_aws/network_map_aws.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_sum/network_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_traffic_map/heatmap.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_traffic_map/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_traffic_map/network_traffic_map.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvlink_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvlink_sum/nvlink_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvlink_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/nvtx_gpu_proj_pace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/pace.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/nvtx_gpu_proj_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/topN.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/nvtx_gpu_proj_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/trace.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/nvtx_pace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/pace.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/nvtx_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/osrt_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/stats.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/heatmap.ipynb", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/metadata.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/ucx_gpu_time_util_map.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/common.txt", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/dask.txt", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/jupyter.txt", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/_sqlite3.cpython-312-x86_64-linux-gnu.so", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/sqlite3/__init__.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/sqlite3/__main__.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/sqlite3/dbapi2.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/sqlite3/dump.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/_sqlite3.cpython-310-x86_64-linux-gnu.so", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/__init__.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/dbapi2.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/dump.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/README.txt", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/_sql.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/_sqlfile.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/_tbl.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/_values.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_api_gpu_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_api_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_api_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_kern_gb_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_kern_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_mem_size_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_mem_time_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_kern_exec_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_kern_exec_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/dx11_pix_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/dx12_gpu_marker_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/dx12_pix_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/mpi_event_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/mpi_event_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/network_congestion.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_gpu_proj_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_gpu_proj_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_kern_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_pushpop_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_pushpop_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_startend_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvvideo_api_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/openacc_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/opengl_khr_gpu_range_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/opengl_khr_range_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/openmp_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/osrt_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/syscall_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/um_cpu_page_faults_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/um_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/um_total_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/vulkan_api_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/vulkan_api_trace.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/vulkan_gpu_marker_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/vulkan_marker_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/wddm_queue_sum.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/README.txt", "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/cuda_api_sync.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/cuda_memcpy_async.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/cuda_memcpy_sync.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/cuda_memset_sync.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/dx12_mem_ops.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/gpu_gaps.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/gpu_time_util.py", "Library/nsight-compute/2025.1.1/host/target-windows-x64/sqlite3.dll", "Library/nsight-compute/2025.1.1/host/target-windows-x64/sqlite3.exe", "Library/nsight-compute/2025.1.1/host/target-windows-x64/targetsettings.xml", "Library/nsight-compute/2025.1.1/host/target-windows-x64/vulkan-layers/VkLayer_nsight-sys_systemwide_windows.json", "Library/nsight-compute/2025.1.1/host/target-windows-x64/vulkan-layers/VkLayer_nsight-sys_windows.json", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AgentAPI.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Analysis.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AnalysisContainersData.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AnalysisData.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AnalysisProto.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AppLib.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AppLibInterfaces.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Assert.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/CommonProtoServices.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/CommonProtoStreamSections.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Core.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/CrashReporter.exe", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/CudaDrvApiWrapper.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/DeviceProperty.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/DevicePropertyProto.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ETWEventsHandlers.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/EventSource.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/EventsView.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ExternalIntegration.xml", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/GenericHierarchy.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/GpuInfo.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/GpuTraits.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/HostCommon.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InjectionCommunicator.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceData.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceShared.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceSharedBase.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceSharedCore.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceSharedLoggers.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NetworkInfo.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NvLog.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NvQtGui.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NvmlWrapper.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NvtxExtData.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/CorePlugin/CorePlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/ExternalIntegrationPlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/LinuxPlatformPlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/Manifest.js", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/QuadDPlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/default.layout", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/RebelPlugin/RebelPlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/SassDebuggerPlugin/SassDebuggerPlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/TPSConnectionPlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/TPSSystemServerPlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/TimelinePlugin/Manifest.js", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/TimelinePlugin/TimelinePlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/WindowsPlatformPlugin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qgif.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qico.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qjpeg.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qsvg.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qtga.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qtiff.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qwbmp.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/platforms/qwindows.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/tls/qcertonlybackend.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/tls/qopensslbackend.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ProcessLauncher.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ProtobufComm.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ProtobufCommClient.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ProtobufCommProto.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QdstrmImporter.exe", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Charts.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Concurrent.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Core.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Designer.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6DesignerComponents.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Gui.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Help.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Multimedia.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6MultimediaQuick.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6MultimediaWidgets.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Network.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6OpenGL.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6OpenGLWidgets.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Positioning.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6PrintSupport.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Qml.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6QmlModels.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Quick.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6QuickParticles.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6QuickTest.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6QuickWidgets.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Sensors.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Sql.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6StateMachine.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Svg.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6SvgWidgets.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Test.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6UiTools.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6WebChannel.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6WebEngineCore.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6WebEngineWidgets.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6WebSockets.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Widgets.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Xml.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QtPropertyBrowser.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QtWebEngineProcess.exe", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QuiverContainers.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QuiverEvents.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/SshClient.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/StreamSections.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/SymbolAnalyzerLight.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/SymbolDemangler.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TelemetryQuadDClient.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TimelineAssert.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TimelineCommon.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TimelineUIUtils.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TimelineWidget.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-file-l1-2-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-file-l2-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-localization-l1-2-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-processthreads-l1-1-1.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-synch-l1-2-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-timezone-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-convert-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-environment-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-filesystem-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-heap-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-locale-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-math-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-multibyte-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-runtime-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-stdio-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-string-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-time-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-utility-l1-1-0.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/arrow.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_atomic-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_chrono-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_container-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_date_time-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_filesystem-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_iostreams-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_locale-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_program_options-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_python310-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_regex-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_serialization-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_system-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_thread-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_timer-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/exporter.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/libcrypto-3-x64.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/libssl-3-x64.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/msdia140.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ncu-ui.exe", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/nvsym.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/odin.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/opengl32sw.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/parquet.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/protobuf-shared.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/qt.conf", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/icudtl.dat", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_devtools_resources.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources_100p.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources_200p.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/v8_context_snapshot.bin", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/sqlite3.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ssh.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/am.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ar.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/bg.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/bn.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ca.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/cs.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/da.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/de.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/el.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/en-GB.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/en-US.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/es-419.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/es.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/et.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fa.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fi.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fil.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fr.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/gu.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/he.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hi.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hr.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hu.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/id.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/it.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ja.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/kn.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ko.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/lt.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/lv.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ml.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/mr.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ms.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/nb.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/nl.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pl.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pt-BR.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pt-PT.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ro.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ru.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sk.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sl.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sr.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sv.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sw.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ta.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/te.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/th.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/tr.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/uk.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/vi.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/zh-CN.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/zh-TW.pak", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ucrtbase.dll", "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/zlib.dll", "Library/nsight-compute/2025.1.1/ncu-ui.bat", "Library/nsight-compute/2025.1.1/ncu.bat", "Library/nsight-compute/2025.1.1/sections/AchievedOccupancy.py", "Library/nsight-compute/2025.1.1/sections/C2CLink.section", "Library/nsight-compute/2025.1.1/sections/CPIStall.py", "Library/nsight-compute/2025.1.1/sections/Compute.py", "Library/nsight-compute/2025.1.1/sections/ComputeWorkloadAnalysis.section", "Library/nsight-compute/2025.1.1/sections/FPInstructions.py", "Library/nsight-compute/2025.1.1/sections/HighPipeUtilization.py", "Library/nsight-compute/2025.1.1/sections/InstructionStatistics.section", "Library/nsight-compute/2025.1.1/sections/IssueSlotUtilization.py", "Library/nsight-compute/2025.1.1/sections/LaunchStatistics.py", "Library/nsight-compute/2025.1.1/sections/LaunchStatistics.section", "Library/nsight-compute/2025.1.1/sections/Memory.py", "Library/nsight-compute/2025.1.1/sections/MemoryApertureUsage.py", "Library/nsight-compute/2025.1.1/sections/MemoryCacheAccessPattern.py", "Library/nsight-compute/2025.1.1/sections/MemoryL2Compression.py", "Library/nsight-compute/2025.1.1/sections/MemoryWorkloadAnalysis.section", "Library/nsight-compute/2025.1.1/sections/MemoryWorkloadAnalysis_Chart.section", "Library/nsight-compute/2025.1.1/sections/MemoryWorkloadAnalysis_Tables.section", "Library/nsight-compute/2025.1.1/sections/NumaAffinity.section", "Library/nsight-compute/2025.1.1/sections/NvRules.py", "Library/nsight-compute/2025.1.1/sections/Nvlink.section", "Library/nsight-compute/2025.1.1/sections/Nvlink_Tables.section", "Library/nsight-compute/2025.1.1/sections/Nvlink_Topology.section", "Library/nsight-compute/2025.1.1/sections/Occupancy.section", "Library/nsight-compute/2025.1.1/sections/PCSamplingData.py", "Library/nsight-compute/2025.1.1/sections/PMSamplingData.py", "Library/nsight-compute/2025.1.1/sections/PmSampling.section", "Library/nsight-compute/2025.1.1/sections/PmSampling_WarpStates.section", "Library/nsight-compute/2025.1.1/sections/RequestedMetrics.py", "Library/nsight-compute/2025.1.1/sections/SchedulerStatistics.section", "Library/nsight-compute/2025.1.1/sections/SharedMemoryConflicts.py", "Library/nsight-compute/2025.1.1/sections/SlowPipeLimiter.py", "Library/nsight-compute/2025.1.1/sections/SourceCounters.section", "Library/nsight-compute/2025.1.1/sections/SpeedOfLight.py", "Library/nsight-compute/2025.1.1/sections/SpeedOfLight.section", "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_HierarchicalDoubleRooflineChart.section", "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_HierarchicalHalfRooflineChart.section", "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_HierarchicalSingleRooflineChart.section", "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_HierarchicalTensorRooflineChart.section", "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_Roofline.py", "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_RooflineChart.section", "Library/nsight-compute/2025.1.1/sections/TheoreticalOccupancy.py", "Library/nsight-compute/2025.1.1/sections/ThreadDivergence.py", "Library/nsight-compute/2025.1.1/sections/UncoalescedAccess.chart", "Library/nsight-compute/2025.1.1/sections/UncoalescedAccess.py", "Library/nsight-compute/2025.1.1/sections/UncoalescedSharedAccess.chart", "Library/nsight-compute/2025.1.1/sections/UncoalescedSharedAccess.py", "Library/nsight-compute/2025.1.1/sections/WarpStateStatistics.section", "Library/nsight-compute/2025.1.1/sections/WorkloadDistribution.section", "Library/nsight-compute/2025.1.1/sections/WorkloadImbalance.py", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/TreeLauncherSubreaper", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/TreeLauncherTargetLdPreloadHelper", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libInterceptorInjectionTarget.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherPlaceholder.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherTargetInjection.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherTargetUpdatePreloadInjection.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libcuda-injection.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libnvperf_host.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libnvperf_target.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/ncu", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/TreeLauncherTargetLdPreloadHelper", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/libInterceptorInjectionTarget.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherPlaceholder.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherTargetInjection.so", "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherTargetUpdatePreloadInjection.so", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/InterceptorInjectionTarget.dll", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/TreeLauncherTargetInjection.dll", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/cuda-injection.dll", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/icudt71.dll", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/icuuc71.dll", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/ncu.exe", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/nvperf_host.dll", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/nvperf_target.dll", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x86/InterceptorInjectionTarget.dll", "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x86/TreeLauncherTargetInjection.dll", "Scripts/CrashReporter.bat", "Scripts/CudaGpuInfoDumper.bat", "Scripts/QdstrmImporter.bat", "Scripts/QtWebEngineProcess.bat", "Scripts/ncu-ui.bat", "Scripts/ncu.bat", "Scripts/nsight-sys-service.bat", "Scripts/nsys.bat", "Scripts/python.bat", "Scripts/sqlite3.bat"], "fn": "nsight-compute-2025.1.1.2-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\nsight-compute-2025.1.1.2-0", "type": 1}, "md5": "0b5391a855a747bea965efad59356a44", "name": "nsight-compute", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\nsight-compute-2025.1.1.2-0.conda", "paths_data": {"paths": [{"_path": "Library/nsight-compute/2025.1.1/docs/Archives/index.html", "path_type": "hardlink", "sha256": "0fd2305ae06a5df3c43e7b5ddb7b751632ff1ebbaf77bff3d83c2bf60edddbdc", "sha256_in_prefix": "0fd2305ae06a5df3c43e7b5ddb7b751632ff1ebbaf77bff3d83c2bf60edddbdc", "size_in_bytes": 12305}, {"_path": "Library/nsight-compute/2025.1.1/docs/CopyrightAndLicenses/index.html", "path_type": "hardlink", "sha256": "0937927eaa9abf963f3f4535393a5b8962cf6a673a25ef992751c65e3114f230", "sha256_in_prefix": "0937927eaa9abf963f3f4535393a5b8962cf6a673a25ef992751c65e3114f230", "size_in_bytes": 117555}, {"_path": "Library/nsight-compute/2025.1.1/docs/CustomizationGuide/index.html", "path_type": "hardlink", "sha256": "021db2f78d285c65a24b2e303e921704236204aeba03644550fb08bf1d5d1c46", "sha256_in_prefix": "021db2f78d285c65a24b2e303e921704236204aeba03644550fb08bf1d5d1c46", "size_in_bytes": 77943}, {"_path": "Library/nsight-compute/2025.1.1/docs/Notices/notices.html", "path_type": "hardlink", "sha256": "25b7b7734b4dcef3ffb4c0e77e9458581cd619e5da0bdea9051a0adba96f1433", "sha256_in_prefix": "25b7b7734b4dcef3ffb4c0e77e9458581cd619e5da0bdea9051a0adba96f1433", "size_in_bytes": 7831}, {"_path": "Library/nsight-compute/2025.1.1/docs/NsightCompute/index.html", "path_type": "hardlink", "sha256": "8b9d550b3881216ed7ceef68941e229d8b126b61af5c85efc3f4b7904b973016", "sha256_in_prefix": "8b9d550b3881216ed7ceef68941e229d8b126b61af5c85efc3f4b7904b973016", "size_in_bytes": 231276}, {"_path": "Library/nsight-compute/2025.1.1/docs/NsightComputeCli/index.html", "path_type": "hardlink", "sha256": "813ce12d46546cbe2d006b4f94a00cddea038d071ca07a8425202c6cac29236c", "sha256_in_prefix": "813ce12d46546cbe2d006b4f94a00cddea038d071ca07a8425202c6cac29236c", "size_in_bytes": 168124}, {"_path": "Library/nsight-compute/2025.1.1/docs/NvRulesAPI/index.html", "path_type": "hardlink", "sha256": "f42422ab381c9fd5c3b315a06a8a3ef5e910fbc03dc72a3d43c57d3544e87246", "sha256_in_prefix": "f42422ab381c9fd5c3b315a06a8a3ef5e910fbc03dc72a3d43c57d3544e87246", "size_in_bytes": 8931}, {"_path": "Library/nsight-compute/2025.1.1/docs/ProfilingGuide/index.html", "path_type": "hardlink", "sha256": "309d59d7a2700478127871990b66fdb53ee2db88143411a82f3e83f77c7b35cf", "sha256_in_prefix": "309d59d7a2700478127871990b66fdb53ee2db88143411a82f3e83f77c7b35cf", "size_in_bytes": 268961}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/index.html", "path_type": "hardlink", "sha256": "0cb0d022ce53ce395364d487e8c1b8f47749c3ef1c421a9e130aa0937f4fe9dc", "sha256_in_prefix": "0cb0d022ce53ce395364d487e8c1b8f47749c3ef1c421a9e130aa0937f4fe9dc", "size_in_bytes": 172593}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/gpu-support.html", "path_type": "hardlink", "sha256": "48c68551a9135e71746728edd243276351532c4fbb478276ffb11f2ed130063e", "sha256_in_prefix": "48c68551a9135e71746728edd243276351532c4fbb478276ffb11f2ed130063e", "size_in_bytes": 8453}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/known-issues.html", "path_type": "hardlink", "sha256": "5a6dccd80d9b04e94c0765e5662dd7bd767227078a53a3c2f5e0b28123e00605", "sha256_in_prefix": "5a6dccd80d9b04e94c0765e5662dd7bd767227078a53a3c2f5e0b28123e00605", "size_in_bytes": 22479}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/library-support-optix.html", "path_type": "hardlink", "sha256": "6d61da76653f9ec8bfdec7260dee574da86b0b3965f527a7d00dac6bdb238a0f", "sha256_in_prefix": "6d61da76653f9ec8bfdec7260dee574da86b0b3965f527a7d00dac6bdb238a0f", "size_in_bytes": 9475}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/library-support.html", "path_type": "hardlink", "sha256": "74757754fe4ae19bf6f8dde746ff0d0c69cee9dea9bcd4c590374f7904daeeaf", "sha256_in_prefix": "74757754fe4ae19bf6f8dde746ff0d0c69cee9dea9bcd4c590374f7904daeeaf", "size_in_bytes": 7005}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/platform-support.html", "path_type": "hardlink", "sha256": "ee05773c0216a79ebabee401ac8541114f6e82f96a768dd9b3c2ea919c0b22e6", "sha256_in_prefix": "ee05773c0216a79ebabee401ac8541114f6e82f96a768dd9b3c2ea919c0b22e6", "size_in_bytes": 8902}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/release-notes-older-versions.html", "path_type": "hardlink", "sha256": "80a21f9fad08d92b287f2cdd8d38cb595d9c3976c617f8fa5c9efef7046b74b9", "sha256_in_prefix": "80a21f9fad08d92b287f2cdd8d38cb595d9c3976c617f8fa5c9efef7046b74b9", "size_in_bytes": 6595}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/release-notes.html", "path_type": "hardlink", "sha256": "007a3d38033fb997f6272d2fbcfd35c4a75995807ce6648c3c52beb8f52d558c", "sha256_in_prefix": "007a3d38033fb997f6272d2fbcfd35c4a75995807ce6648c3c52beb8f52d558c", "size_in_bytes": 6590}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/support.html", "path_type": "hardlink", "sha256": "5a1e3be016347093cf8c33f7294834e462a104b9a8abd361699720e9e80658ac", "sha256_in_prefix": "5a1e3be016347093cf8c33f7294834e462a104b9a8abd361699720e9e80658ac", "size_in_bytes": 6612}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/system-requirements.html", "path_type": "hardlink", "sha256": "554b3dea8c676560fb1fb8db4e538ace87c0d82bd74eabfccdc591ae35c79878", "sha256_in_prefix": "554b3dea8c676560fb1fb8db4e538ace87c0d82bd74eabfccdc591ae35c79878", "size_in_bytes": 10404}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-1.html", "path_type": "hardlink", "sha256": "4c23e1b6dc4d6c419ad05dab10b0a0334ed04fb0c77d42daf55f6caef1851870", "sha256_in_prefix": "4c23e1b6dc4d6c419ad05dab10b0a0334ed04fb0c77d42daf55f6caef1851870", "size_in_bytes": 10047}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-2.html", "path_type": "hardlink", "sha256": "10990088d4a8416962f6942cf8d57b6240e83089c48beeeba0cfaccf3bc0c5b9", "sha256_in_prefix": "10990088d4a8416962f6942cf8d57b6240e83089c48beeeba0cfaccf3bc0c5b9", "size_in_bytes": 9034}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-3-1.html", "path_type": "hardlink", "sha256": "7b4b9caa2425c3460a0b167f072fb9e49b72eaf08dbd5332c1b28a7ee6af6909", "sha256_in_prefix": "7b4b9caa2425c3460a0b167f072fb9e49b72eaf08dbd5332c1b28a7ee6af6909", "size_in_bytes": 8369}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-3.html", "path_type": "hardlink", "sha256": "101c8643306891959b94a5ce319c2a025e38b80d61494e1cf6d313717b5ec22e", "sha256_in_prefix": "101c8643306891959b94a5ce319c2a025e38b80d61494e1cf6d313717b5ec22e", "size_in_bytes": 9034}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-4.html", "path_type": "hardlink", "sha256": "a0280de40acc5bfe8fcedb406b9c7fb83e93a560189027c09e9cf367c1155c58", "sha256_in_prefix": "a0280de40acc5bfe8fcedb406b9c7fb83e93a560189027c09e9cf367c1155c58", "size_in_bytes": 10338}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-5-1.html", "path_type": "hardlink", "sha256": "09c011eb0cb9988fb5f373449b1719bcc63e751b8121b9c5824aa6ca9353ea2d", "sha256_in_prefix": "09c011eb0cb9988fb5f373449b1719bcc63e751b8121b9c5824aa6ca9353ea2d", "size_in_bytes": 6753}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-5-2.html", "path_type": "hardlink", "sha256": "aae571085acb031e25b7efc504e1436a4cc6a86ee7623cf808ac9253b8a4b8eb", "sha256_in_prefix": "aae571085acb031e25b7efc504e1436a4cc6a86ee7623cf808ac9253b8a4b8eb", "size_in_bytes": 6704}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-5-3.html", "path_type": "hardlink", "sha256": "fa09e043cae4d7b1b595bec3af4a5039e0a370a7778c468f0cdee6e2a638563f", "sha256_in_prefix": "fa09e043cae4d7b1b595bec3af4a5039e0a370a7778c468f0cdee6e2a638563f", "size_in_bytes": 6742}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2019-5.html", "path_type": "hardlink", "sha256": "427ebf6b01497e73bc7de441cce07ad78d0cae738598059cb7dc2d438e489f29", "sha256_in_prefix": "427ebf6b01497e73bc7de441cce07ad78d0cae738598059cb7dc2d438e489f29", "size_in_bytes": 9712}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-1-1.html", "path_type": "hardlink", "sha256": "c6f298251991766930deb22f99ab02a19816f0f2a2dafd569a3df622149c676d", "sha256_in_prefix": "c6f298251991766930deb22f99ab02a19816f0f2a2dafd569a3df622149c676d", "size_in_bytes": 8609}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-1-2.html", "path_type": "hardlink", "sha256": "9662b3d1faf1d18d8a378fe07f5d1017340f7ab466727300344573364579c6c3", "sha256_in_prefix": "9662b3d1faf1d18d8a378fe07f5d1017340f7ab466727300344573364579c6c3", "size_in_bytes": 7343}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-1.html", "path_type": "hardlink", "sha256": "84993e2d7994f34b69383bc474486f0623c97df68adee3067593d877e24d1c0d", "sha256_in_prefix": "84993e2d7994f34b69383bc474486f0623c97df68adee3067593d877e24d1c0d", "size_in_bytes": 11050}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-2-1.html", "path_type": "hardlink", "sha256": "e57527e918b0f10931a7d4c15f96c1def1075619889f33392fa8f0102095745e", "sha256_in_prefix": "e57527e918b0f10931a7d4c15f96c1def1075619889f33392fa8f0102095745e", "size_in_bytes": 7705}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-2.html", "path_type": "hardlink", "sha256": "80c22db7ec6e4a721f762d136f7fa19d726fd437a0652b6ca5870524c749f940", "sha256_in_prefix": "80c22db7ec6e4a721f762d136f7fa19d726fd437a0652b6ca5870524c749f940", "size_in_bytes": 9645}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-3-1.html", "path_type": "hardlink", "sha256": "c23b04a5ea15b73368c06bae5115710a30b6b7f71abf144a0784e5223ad05c64", "sha256_in_prefix": "c23b04a5ea15b73368c06bae5115710a30b6b7f71abf144a0784e5223ad05c64", "size_in_bytes": 7976}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2020-3.html", "path_type": "hardlink", "sha256": "80c4f292e6cd1369d68353723a20f1625eeb1d698ad674695d95d1b24f5022fa", "sha256_in_prefix": "80c4f292e6cd1369d68353723a20f1625eeb1d698ad674695d95d1b24f5022fa", "size_in_bytes": 11363}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-1-1.html", "path_type": "hardlink", "sha256": "bf4cccc76b31ad85ebd5f9ab36cd9f07be6bc5ca1be8b2f1914b52950e0982da", "sha256_in_prefix": "bf4cccc76b31ad85ebd5f9ab36cd9f07be6bc5ca1be8b2f1914b52950e0982da", "size_in_bytes": 8894}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-1.html", "path_type": "hardlink", "sha256": "b48d830db627cb9ce6051dc4adcb2d5d5e91b798558a24dfa689a04078343c55", "sha256_in_prefix": "b48d830db627cb9ce6051dc4adcb2d5d5e91b798558a24dfa689a04078343c55", "size_in_bytes": 9856}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-1.html", "path_type": "hardlink", "sha256": "d9322948ffee3d9666614022fcff0dd63a718437fa3dd333663752e8f6fbe9b7", "sha256_in_prefix": "d9322948ffee3d9666614022fcff0dd63a718437fa3dd333663752e8f6fbe9b7", "size_in_bytes": 7261}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-2.html", "path_type": "hardlink", "sha256": "0b28b2e759c68bf94e0fdf01330025ee8d24b830101da84c3c6a7eaccb3c5ae9", "sha256_in_prefix": "0b28b2e759c68bf94e0fdf01330025ee8d24b830101da84c3c6a7eaccb3c5ae9", "size_in_bytes": 7072}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-3.html", "path_type": "hardlink", "sha256": "241ac0599b972622a8c1d9bef2180f0c267518e864904088ffa782bf56ba126c", "sha256_in_prefix": "241ac0599b972622a8c1d9bef2180f0c267518e864904088ffa782bf56ba126c", "size_in_bytes": 6900}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-4.html", "path_type": "hardlink", "sha256": "c7a35080e6deb425cd38e5bfa350f95176afb23a39134bb09589d961eb566cea", "sha256_in_prefix": "c7a35080e6deb425cd38e5bfa350f95176afb23a39134bb09589d961eb566cea", "size_in_bytes": 6795}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-5.html", "path_type": "hardlink", "sha256": "16729d0e7509810bf12eb45f5389c3391a756ab8d93034c0611d116f6bc44812", "sha256_in_prefix": "16729d0e7509810bf12eb45f5389c3391a756ab8d93034c0611d116f6bc44812", "size_in_bytes": 6785}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-6.html", "path_type": "hardlink", "sha256": "72c7948663b6ad3ad27ecdd209fe8326ea36c76acac4c9f5cb2fec26734f65a3", "sha256_in_prefix": "72c7948663b6ad3ad27ecdd209fe8326ea36c76acac4c9f5cb2fec26734f65a3", "size_in_bytes": 6881}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-7.html", "path_type": "hardlink", "sha256": "0f7c5d7dc819288b1fd3b4053169e924882295889323d0d53702520af2a9a3ea", "sha256_in_prefix": "0f7c5d7dc819288b1fd3b4053169e924882295889323d0d53702520af2a9a3ea", "size_in_bytes": 6759}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-8.html", "path_type": "hardlink", "sha256": "6c04875dcdc05052600283a42b2e1f6054b6d39d6a66c684a7987f26ee550f4e", "sha256_in_prefix": "6c04875dcdc05052600283a42b2e1f6054b6d39d6a66c684a7987f26ee550f4e", "size_in_bytes": 6738}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2-9.html", "path_type": "hardlink", "sha256": "2856abe4895097225613f6e5d07282bdf300b2536205cfc5bd2fe09a28eec391", "sha256_in_prefix": "2856abe4895097225613f6e5d07282bdf300b2536205cfc5bd2fe09a28eec391", "size_in_bytes": 6800}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-2.html", "path_type": "hardlink", "sha256": "b135a8aeded6a8157346747b45710382556678ed8dc5993b3f2c640ff8ebf10d", "sha256_in_prefix": "b135a8aeded6a8157346747b45710382556678ed8dc5993b3f2c640ff8ebf10d", "size_in_bytes": 12146}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-3-1.html", "path_type": "hardlink", "sha256": "196738f9523fdfe16029ae391f9772026ca1c299ffb5cf908e9ad2aff0d497c6", "sha256_in_prefix": "196738f9523fdfe16029ae391f9772026ca1c299ffb5cf908e9ad2aff0d497c6", "size_in_bytes": 7391}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2021-3.html", "path_type": "hardlink", "sha256": "18359c1897629b35ee567d29d5ae7e15ea2a1569faa3b2f9c4e7bcc7cd4b2fa4", "sha256_in_prefix": "18359c1897629b35ee567d29d5ae7e15ea2a1569faa3b2f9c4e7bcc7cd4b2fa4", "size_in_bytes": 11173}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-1-1.html", "path_type": "hardlink", "sha256": "78b58772c348df25cbca8a735bff908bbdedd5313427ad6e2fdad31a4740fb6f", "sha256_in_prefix": "78b58772c348df25cbca8a735bff908bbdedd5313427ad6e2fdad31a4740fb6f", "size_in_bytes": 8181}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-1.html", "path_type": "hardlink", "sha256": "86a7110b98197ffd897b89a8e73efc4ff96d1526f20411f81f60b7348b3980fe", "sha256_in_prefix": "86a7110b98197ffd897b89a8e73efc4ff96d1526f20411f81f60b7348b3980fe", "size_in_bytes": 10533}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-2-1.html", "path_type": "hardlink", "sha256": "2ff174927effb6ba21db85deb0fe1c764c191708c797440195ab3bd5b87111e1", "sha256_in_prefix": "2ff174927effb6ba21db85deb0fe1c764c191708c797440195ab3bd5b87111e1", "size_in_bytes": 7351}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-2.html", "path_type": "hardlink", "sha256": "32a76c05f5b33f734409b74f170e1b3ea0ec3740651239d609a964bc49a7742c", "sha256_in_prefix": "32a76c05f5b33f734409b74f170e1b3ea0ec3740651239d609a964bc49a7742c", "size_in_bytes": 11459}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-3.html", "path_type": "hardlink", "sha256": "b03d63292c7b6d785e01344aa07e3124f09c0961f2dabf42b6010537e3a52dde", "sha256_in_prefix": "b03d63292c7b6d785e01344aa07e3124f09c0961f2dabf42b6010537e3a52dde", "size_in_bytes": 10680}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-4-1.html", "path_type": "hardlink", "sha256": "456baec788f76a2ccd93a5db1e8651a2dfd5d6f8aabb0c29b1e4743db72f92a1", "sha256_in_prefix": "456baec788f76a2ccd93a5db1e8651a2dfd5d6f8aabb0c29b1e4743db72f92a1", "size_in_bytes": 7301}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2022-4.html", "path_type": "hardlink", "sha256": "4a40c56ae5a4a107f6039c5aa2b3f46f1a03c4c5e6288e7c82826bd9c7397111", "sha256_in_prefix": "4a40c56ae5a4a107f6039c5aa2b3f46f1a03c4c5e6288e7c82826bd9c7397111", "size_in_bytes": 13060}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-1-1.html", "path_type": "hardlink", "sha256": "b75a7ce505fff9dd371fa8ae5d9f29dad6fd1bd524ea4fd553bcb4590f815896", "sha256_in_prefix": "b75a7ce505fff9dd371fa8ae5d9f29dad6fd1bd524ea4fd553bcb4590f815896", "size_in_bytes": 8833}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-1.html", "path_type": "hardlink", "sha256": "c51bddac930558e2b87e9d670da6526adde14f925eef4f68e6534e6e6f5a86a3", "sha256_in_prefix": "c51bddac930558e2b87e9d670da6526adde14f925eef4f68e6534e6e6f5a86a3", "size_in_bytes": 11804}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-2-1.html", "path_type": "hardlink", "sha256": "5ce6e4d9a41c20734bfeb6acd4b680a9c8b7f7c1ec67145478d0b418352a5574", "sha256_in_prefix": "5ce6e4d9a41c20734bfeb6acd4b680a9c8b7f7c1ec67145478d0b418352a5574", "size_in_bytes": 6947}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-2-2.html", "path_type": "hardlink", "sha256": "a4c18323c34e96e22717bd60d582732411ebe7628ce97e2eabfb5233f72b1d39", "sha256_in_prefix": "a4c18323c34e96e22717bd60d582732411ebe7628ce97e2eabfb5233f72b1d39", "size_in_bytes": 7050}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-2.html", "path_type": "hardlink", "sha256": "9a4a86c9aca2df1f374f1b8a1b695a21c912e202c6f83e1c39b091d910299a01", "sha256_in_prefix": "9a4a86c9aca2df1f374f1b8a1b695a21c912e202c6f83e1c39b091d910299a01", "size_in_bytes": 10404}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-3-1.html", "path_type": "hardlink", "sha256": "3ecdc93c4a73d794ee1f44dc65fd59642bc829e550f1aa1b7987defca38275b3", "sha256_in_prefix": "3ecdc93c4a73d794ee1f44dc65fd59642bc829e550f1aa1b7987defca38275b3", "size_in_bytes": 8048}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2023-3.html", "path_type": "hardlink", "sha256": "a404970c6ea86e2d0369774f04ca9d701b2121584b5916176c105145d6a73340", "sha256_in_prefix": "a404970c6ea86e2d0369774f04ca9d701b2121584b5916176c105145d6a73340", "size_in_bytes": 11753}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-1-1.html", "path_type": "hardlink", "sha256": "d4a31aca9fe1f929db33e56f7e21ab8ae5e0581d95b17f3966de2d79776af6c6", "sha256_in_prefix": "d4a31aca9fe1f929db33e56f7e21ab8ae5e0581d95b17f3966de2d79776af6c6", "size_in_bytes": 7539}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-1.html", "path_type": "hardlink", "sha256": "3748c778cade5792b4ad9a35d46b8964615a0b6fb0a13341da405cbdbc8d478d", "sha256_in_prefix": "3748c778cade5792b4ad9a35d46b8964615a0b6fb0a13341da405cbdbc8d478d", "size_in_bytes": 11695}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-2-1.html", "path_type": "hardlink", "sha256": "9424bd9d2a5acb4ca14b829bfadcd9ef1b4a0e127db790953dd06db940dca4cf", "sha256_in_prefix": "9424bd9d2a5acb4ca14b829bfadcd9ef1b4a0e127db790953dd06db940dca4cf", "size_in_bytes": 7842}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-2.html", "path_type": "hardlink", "sha256": "c0c870f1de6f8d2f0eea59bc697d2994a84c69cfa74432913dd7cabec58995e6", "sha256_in_prefix": "c0c870f1de6f8d2f0eea59bc697d2994a84c69cfa74432913dd7cabec58995e6", "size_in_bytes": 10589}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-3-1.html", "path_type": "hardlink", "sha256": "2f27a9e7387218148fb920c105ab67edb3a06ade447e7bc524d88a5fdf35fbac", "sha256_in_prefix": "2f27a9e7387218148fb920c105ab67edb3a06ade447e7bc524d88a5fdf35fbac", "size_in_bytes": 8090}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-3-2.html", "path_type": "hardlink", "sha256": "9b7342807e945e00e38afb76f1eb42b8088ada4d700b4b31cf8a7dbbe1553051", "sha256_in_prefix": "9b7342807e945e00e38afb76f1eb42b8088ada4d700b4b31cf8a7dbbe1553051", "size_in_bytes": 7364}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-3.html", "path_type": "hardlink", "sha256": "5295c9f079fa3a823fe408c622aa13c44f1696d2245c3324611da96a8e269981", "sha256_in_prefix": "5295c9f079fa3a823fe408c622aa13c44f1696d2245c3324611da96a8e269981", "size_in_bytes": 8601}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2024-4.html", "path_type": "hardlink", "sha256": "0c221fb1a5e1451e250fb8fba7d8df95e7dc8134a68454c8b0998a27a0e7e6fd", "sha256_in_prefix": "0c221fb1a5e1451e250fb8fba7d8df95e7dc8134a68454c8b0998a27a0e7e6fd", "size_in_bytes": 10728}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2025-1-1.html", "path_type": "hardlink", "sha256": "0d07250416c6985f8a0602cf7d5be1471bf58050b8905f24ea6635b890f83b8c", "sha256_in_prefix": "0d07250416c6985f8a0602cf7d5be1471bf58050b8905f24ea6635b890f83b8c", "size_in_bytes": 7337}, {"_path": "Library/nsight-compute/2025.1.1/docs/ReleaseNotes/topics/updates-2025-1.html", "path_type": "hardlink", "sha256": "97ed07f6f60dd1dc39601d48495243e496d55d2451eb01395f8d7eb075b7096b", "sha256_in_prefix": "97ed07f6f60dd1dc39601d48495243e496d55d2451eb01395f8d7eb075b7096b", "size_in_bytes": 11116}, {"_path": "Library/nsight-compute/2025.1.1/docs/Training/index.html", "path_type": "hardlink", "sha256": "2f0d037966714ba8c108864151f0cd9e6f48d8cebf2c8821eb2cbcdd9c2a92d3", "sha256_in_prefix": "2f0d037966714ba8c108864151f0cd9e6f48d8cebf2c8821eb2cbcdd9c2a92d3", "size_in_bytes": 18037}, {"_path": "Library/nsight-compute/2025.1.1/docs/VERSION", "path_type": "hardlink", "sha256": "48828656ad190e01c214fbf91d1583a24efa794e6f59629300c523f59a9c4d19", "sha256_in_prefix": "48828656ad190e01c214fbf91d1583a24efa794e6f59629300c523f59a9c4d19", "size_in_bytes": 5}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/add-remote-connection-private-key.png", "path_type": "hardlink", "sha256": "b0d7b350f0029497b04fe25f03b726c803573642f140bae3d67887f6b8f342fd", "sha256_in_prefix": "b0d7b350f0029497b04fe25f03b726c803573642f140bae3d67887f6b8f342fd", "size_in_bytes": 82982}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/add-remote-connection.png", "path_type": "hardlink", "sha256": "5fb0b685b144ce1ec9f25c2902a04d5c9f9b26330df83939a3536ea95547ecec", "sha256_in_prefix": "5fb0b685b144ce1ec9f25c2902a04d5c9f9b26330df83939a3536ea95547ecec", "size_in_bytes": 86395}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-cam.png", "path_type": "hardlink", "sha256": "0c44ebb00bbfd1bac6b8e2802b1655d552a02bc08827cee78f56af0ac0c2699d", "sha256_in_prefix": "0c44ebb00bbfd1bac6b8e2802b1655d552a02bc08827cee78f56af0ac0c2699d", "size_in_bytes": 29258}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-display-filter.png", "path_type": "hardlink", "sha256": "6e97758b342f391273311e8e5f212272ff8d7c3125ffd0d92564c9a9f7545453", "sha256_in_prefix": "6e97758b342f391273311e8e5f212272ff8d7c3125ffd0d92564c9a9f7545453", "size_in_bytes": 40531}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-open-button.png", "path_type": "hardlink", "sha256": "459a207becec2003d7dd312a76ede4c393cba4f4dc2dd0c47c1fbb044da2b926", "sha256_in_prefix": "459a207becec2003d7dd312a76ede4c393cba4f4dc2dd0c47c1fbb044da2b926", "size_in_bytes": 32173}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-property-filter.png", "path_type": "hardlink", "sha256": "bc4a4bee8528de24eec2f1bf9e9d47dbefbc514ddf9f2452ad3bade2227f5692", "sha256_in_prefix": "bc4a4bee8528de24eec2f1bf9e9d47dbefbc514ddf9f2452ad3bade2227f5692", "size_in_bytes": 13188}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/as-viewer-rendering-options.png", "path_type": "hardlink", "sha256": "2673534e36e906c921a62e2a7eda0eedfedba39bf5d7a8d11c827f10e291c817", "sha256_in_prefix": "2673534e36e906c921a62e2a7eda0eedfedba39bf5d7a8d11c827f10e291c817", "size_in_bytes": 17085}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/as-viewer.png", "path_type": "hardlink", "sha256": "04a7c168a4eb0066c3ca1ac1c62d1e157a6c7c857fed75e389c3129f6542c615", "sha256_in_prefix": "04a7c168a4eb0066c3ca1ac1c62d1e157a6c7c857fed75e389c3129f6542c615", "size_in_bytes": 607821}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/baselines-multiple.png", "path_type": "hardlink", "sha256": "48aa5adae712d28ab131b9fbe5887f7b2ee04a51b0d105d13388c446266d71ce", "sha256_in_prefix": "48aa5adae712d28ab131b9fbe5887f7b2ee04a51b0d105d13388c446266d71ce", "size_in_bytes": 112358}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/baselines-tool-window.png", "path_type": "hardlink", "sha256": "e4bcba2e59f109e4dac2374b6724171961dbe436cc4b0a9a8d4bc54c61d8a98b", "sha256_in_prefix": "e4bcba2e59f109e4dac2374b6724171961dbe436cc4b0a9a8d4bc54c61d8a98b", "size_in_bytes": 180022}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/baselines.png", "path_type": "hardlink", "sha256": "07c4e7e68eeeda773070526af55098ad1838beb931a5cd6ae9cc614ea2f4cabf", "sha256_in_prefix": "07c4e7e68eeeda773070526af55098ad1838beb931a5cd6ae9cc614ea2f4cabf", "size_in_bytes": 100245}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/connection-dialog.png", "path_type": "hardlink", "sha256": "8c4a97e736dee5dda9ddaf7626818b08cb51ec7b483bae33ad139bb84de5d2dc", "sha256_in_prefix": "8c4a97e736dee5dda9ddaf7626818b08cb51ec7b483bae33ad139bb84de5d2dc", "size_in_bytes": 55616}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/cubin-viewer.png", "path_type": "hardlink", "sha256": "bd8154ae2473c8d66bde1096626b972dfb4a9d460d04b8d0b5f990cc217b687c", "sha256_in_prefix": "bd8154ae2473c8d66bde1096626b972dfb4a9d460d04b8d0b5f990cc217b687c", "size_in_bytes": 97809}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-11.png", "path_type": "hardlink", "sha256": "1bc89a4d53ddb1896f05d6e2b2986e602f8d6611182530103d9b174e64735ecc", "sha256_in_prefix": "1bc89a4d53ddb1896f05d6e2b2986e602f8d6611182530103d9b174e64735ecc", "size_in_bytes": 16609}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-12.png", "path_type": "hardlink", "sha256": "97a6ccbadb5fcd999e5cb5b2e95d3b0b52c96637bd377b843f5ceccc518028cf", "sha256_in_prefix": "97a6ccbadb5fcd999e5cb5b2e95d3b0b52c96637bd377b843f5ceccc518028cf", "size_in_bytes": 15005}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-13.png", "path_type": "hardlink", "sha256": "ebf289807dde357b3073ffa0a9d54f777bccbdbdd42ff73bf81a0c6016491df7", "sha256_in_prefix": "ebf289807dde357b3073ffa0a9d54f777bccbdbdd42ff73bf81a0c6016491df7", "size_in_bytes": 22352}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-14.png", "path_type": "hardlink", "sha256": "22131ced2ba0034b020d15eda3a7438c1e7e9f35c504a6d8c3088d29b1ff71d0", "sha256_in_prefix": "22131ced2ba0034b020d15eda3a7438c1e7e9f35c504a6d8c3088d29b1ff71d0", "size_in_bytes": 19416}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-15.png", "path_type": "hardlink", "sha256": "20210f5550f397b8befd0485881e79a04e8128076e815d6b3826ea7f80a898d6", "sha256_in_prefix": "20210f5550f397b8befd0485881e79a04e8128076e815d6b3826ea7f80a898d6", "size_in_bytes": 35160}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-16.png", "path_type": "hardlink", "sha256": "161c0deadabea3df1d5e7763c009c3ada8b88a646c6bce12dac1f1eb14dcdb73", "sha256_in_prefix": "161c0deadabea3df1d5e7763c009c3ada8b88a646c6bce12dac1f1eb14dcdb73", "size_in_bytes": 32152}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-2.png", "path_type": "hardlink", "sha256": "b405e8b942581b67d6a094b97577180ef622db06cf5ecc69a76ff706830a606f", "sha256_in_prefix": "b405e8b942581b67d6a094b97577180ef622db06cf5ecc69a76ff706830a606f", "size_in_bytes": 19687}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-3.png", "path_type": "hardlink", "sha256": "18e4aa3f07bc5d328902190e6e8db0e731ed58ce1ada787caa8f4222bc87f711", "sha256_in_prefix": "18e4aa3f07bc5d328902190e6e8db0e731ed58ce1ada787caa8f4222bc87f711", "size_in_bytes": 20414}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-5.png", "path_type": "hardlink", "sha256": "3bb4debdae0262faa4c6c26399293197df0b567ba97a3fb645188d286e7c31c4", "sha256_in_prefix": "3bb4debdae0262faa4c6c26399293197df0b567ba97a3fb645188d286e7c31c4", "size_in_bytes": 10367}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-6.png", "path_type": "hardlink", "sha256": "edd8c656bdd9e11db0fe7badf0cf31ffe57a5bc632781632cde52c6e5402b488", "sha256_in_prefix": "edd8c656bdd9e11db0fe7badf0cf31ffe57a5bc632781632cde52c6e5402b488", "size_in_bytes": 11725}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-7.png", "path_type": "hardlink", "sha256": "a95cfc343197afa2b9ac1144783ef3a86df1f977ada195d4c52eb6f6541cae27", "sha256_in_prefix": "a95cfc343197afa2b9ac1144783ef3a86df1f977ada195d4c52eb6f6541cae27", "size_in_bytes": 10594}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-8.png", "path_type": "hardlink", "sha256": "8a05c81e08a3f6333bd777f346bd3d99b59cc5efa8ab50b26c60aceb0e47ba72", "sha256_in_prefix": "8a05c81e08a3f6333bd777f346bd3d99b59cc5efa8ab50b26c60aceb0e47ba72", "size_in_bytes": 12922}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/filter-example-9.png", "path_type": "hardlink", "sha256": "1bee0a5700143ea6e71356f284dc6c03b8b15448ff00692abbd52eab761f1f08", "sha256_in_prefix": "1bee0a5700143ea6e71356f284dc6c03b8b15448ff00692abbd52eab761f1f08", "size_in_bytes": 12205}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/green-contexts-details-page.png", "path_type": "hardlink", "sha256": "ac1d9a56a8286cf82afff650a4373890998f1443e7c9d6caae2964db2bcfdd68", "sha256_in_prefix": "ac1d9a56a8286cf82afff650a4373890998f1443e7c9d6caae2964db2bcfdd68", "size_in_bytes": 188298}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/green-contexts-resource-tool-window-with-tpc-mask.png", "path_type": "hardlink", "sha256": "8208bce216504efd34064adf74c66ceaf2fe35235f8eded07cd1e50550fa8538", "sha256_in_prefix": "8208bce216504efd34064adf74c66ceaf2fe35235f8eded07cd1e50550fa8538", "size_in_bytes": 53565}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/green-contexts-session-page.png", "path_type": "hardlink", "sha256": "a5c38f03291568974a78da99e92a1326124d3ec263641d55cfd82e98bac0d321", "sha256_in_prefix": "a5c38f03291568974a78da99e92a1326124d3ec263641d55cfd82e98bac0d321", "size_in_bytes": 27648}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/hw-model-l1tex-ga100-global.png", "path_type": "hardlink", "sha256": "dd4407b3e325f56779a26799d661f2452d74d487257e02d5b7f5301dda22afed", "sha256_in_prefix": "dd4407b3e325f56779a26799d661f2452d74d487257e02d5b7f5301dda22afed", "size_in_bytes": 293093}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/hw-model-l1tex.png", "path_type": "hardlink", "sha256": "80d78ea525a7b4390fea36fae6b32fb3d75f0ad51d27225f6bc5eb38961099cc", "sha256_in_prefix": "80d78ea525a7b4390fea36fae6b32fb3d75f0ad51d27225f6bc5eb38961099cc", "size_in_bytes": 98145}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/hw-model-lts-ga100.png", "path_type": "hardlink", "sha256": "c51f43da9a5c587bee8ec778091b6849feb1543dd42a34c5224a664e542e07ba", "sha256_in_prefix": "c51f43da9a5c587bee8ec778091b6849feb1543dd42a34c5224a664e542e07ba", "size_in_bytes": 246445}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/hw-model-lts.png", "path_type": "hardlink", "sha256": "8550103de558005351d4b9f0af748fef3277afec57a4daae6bebb978ed942e36", "sha256_in_prefix": "8550103de558005351d4b9f0af748fef3277afec57a4daae6bebb978ed942e36", "size_in_bytes": 57637}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/integration-1.png", "path_type": "hardlink", "sha256": "32f851b64b744b0f71fb5e266694da5f23ebd50fce3c78df38c4bc25db4d2968", "sha256_in_prefix": "32f851b64b744b0f71fb5e266694da5f23ebd50fce3c78df38c4bc25db4d2968", "size_in_bytes": 44337}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/integration-2.png", "path_type": "hardlink", "sha256": "ec075c2056ba16b9893d28ba1b992231a0f01bf4c030ad7395d518f6a52bb81b", "sha256_in_prefix": "ec075c2056ba16b9893d28ba1b992231a0f01bf4c030ad7395d518f6a52bb81b", "size_in_bytes": 56408}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/integration-3.png", "path_type": "hardlink", "sha256": "fa326a7c70f402bc7b45bd05d1250185f7c954290adaf1848603b55ca82821aa", "sha256_in_prefix": "fa326a7c70f402bc7b45bd05d1250185f7c954290adaf1848603b55ca82821aa", "size_in_bytes": 60613}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/integration-4.png", "path_type": "hardlink", "sha256": "f3aa5e0b63265f13fb4fd5389343c5ab3c3f0da29bb85bf2849af1ff25ad530b", "sha256_in_prefix": "f3aa5e0b63265f13fb4fd5389343c5ab3c3f0da29bb85bf2849af1ff25ad530b", "size_in_bytes": 15366}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/main-menu.png", "path_type": "hardlink", "sha256": "63a1818181c29f579bdb0f1db0d0c7b232858d927cf0d3087349ebb2a043d6a0", "sha256_in_prefix": "63a1818181c29f579bdb0f1db0d0c7b232858d927cf0d3087349ebb2a043d6a0", "size_in_bytes": 10846}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/memory-chart-a100.png", "path_type": "hardlink", "sha256": "1286eddd9d5383982d204c5d2e664104855dea244ae7d12196ab62171661f2d3", "sha256_in_prefix": "1286eddd9d5383982d204c5d2e664104855dea244ae7d12196ab62171661f2d3", "size_in_bytes": 57914}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/memory-peak-mapping.png", "path_type": "hardlink", "sha256": "c662c12e526e63e35964334747ae77e23b2778a8924edc1e9a18039992342af1", "sha256_in_prefix": "c662c12e526e63e35964334747ae77e23b2778a8924edc1e9a18039992342af1", "size_in_bytes": 250299}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-dram.png", "path_type": "hardlink", "sha256": "e67860a8f8f2237f9fa42b6a084196ac73978054098a026fd587f9ead2888e51", "sha256_in_prefix": "e67860a8f8f2237f9fa42b6a084196ac73978054098a026fd587f9ead2888e51", "size_in_bytes": 21651}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-l1.png", "path_type": "hardlink", "sha256": "c431cd8fa03c4ff77f71c05f75459b815b3eaf3a4ee9688273133f0f51585100", "sha256_in_prefix": "c431cd8fa03c4ff77f71c05f75459b815b3eaf3a4ee9688273133f0f51585100", "size_in_bytes": 44148}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-l2-evict-policy.png", "path_type": "hardlink", "sha256": "5624bb49c5d9916eda73e861cced16e8fbdc99073325a05933130f5ea3bebdf0", "sha256_in_prefix": "5624bb49c5d9916eda73e861cced16e8fbdc99073325a05933130f5ea3bebdf0", "size_in_bytes": 16177}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-l2.png", "path_type": "hardlink", "sha256": "64a3a2e300523bf4d749463f634648a209ade20517320f7ca76ab5f0ed4d3cff", "sha256_in_prefix": "64a3a2e300523bf4d749463f634648a209ade20517320f7ca76ab5f0ed4d3cff", "size_in_bytes": 30922}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/memory-tables-smem.png", "path_type": "hardlink", "sha256": "ded7953c038abe412e73b4ea92e356bf8d65b58116ef089aaf7c52bf9143906e", "sha256_in_prefix": "ded7953c038abe412e73b4ea92e356bf8d65b58116ef089aaf7c52bf9143906e", "size_in_bytes": 11049}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-activity.png", "path_type": "hardlink", "sha256": "1e472f71359e796326cf38e895c2bd59b0bd5be6bc4d83043bbf0ba6462fe2b0", "sha256_in_prefix": "1e472f71359e796326cf38e895c2bd59b0bd5be6bc4d83043bbf0ba6462fe2b0", "size_in_bytes": 36223}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-from-header.png", "path_type": "hardlink", "sha256": "e9c683697057457781e8233498f3471d8686d533523264cecd671f492307e555", "sha256_in_prefix": "e9c683697057457781e8233498f3471d8686d533523264cecd671f492307e555", "size_in_bytes": 43307}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-from-section.png", "path_type": "hardlink", "sha256": "653f03e7173da6eb2be9681bb99f1999d3bca0d11ef292760e7cab7cc0403bd6", "sha256_in_prefix": "653f03e7173da6eb2be9681bb99f1999d3bca0d11ef292760e7cab7cc0403bd6", "size_in_bytes": 17106}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-gpu-data.png", "path_type": "hardlink", "sha256": "6f78bb41890cfb4f6393ba230ac85130cf5916c3356b5780948fa200bde3692a", "sha256_in_prefix": "6f78bb41890cfb4f6393ba230ac85130cf5916c3356b5780948fa200bde3692a", "size_in_bytes": 102699}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-graphs.png", "path_type": "hardlink", "sha256": "c555ac79bdc7ab5c43d67178f1064dd88ff1abf6350ea41d1c0173c5404b9f96", "sha256_in_prefix": "c555ac79bdc7ab5c43d67178f1064dd88ff1abf6350ea41d1c0173c5404b9f96", "size_in_bytes": 369545}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-tables.png", "path_type": "hardlink", "sha256": "014e8aa9392dd7a7a53facf2a26859a7d389a236ba20ceef0b8750b0a2613252", "sha256_in_prefix": "014e8aa9392dd7a7a53facf2a26859a7d389a236ba20ceef0b8750b0a2613252", "size_in_bytes": 116972}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/occupancy-calculator-utilization.png", "path_type": "hardlink", "sha256": "951ec2ed8b4fa0d004016ec0c56da217da1655576763d95d4222f7899a7ae6ad", "sha256_in_prefix": "951ec2ed8b4fa0d004016ec0c56da217da1655576763d95d4222f7899a7ae6ad", "size_in_bytes": 83631}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/options-profile.png", "path_type": "hardlink", "sha256": "6225d41fd8a02dc680286a7cc0ddc2bb31abb54ff667d05f2f00b7c5daa2f420", "sha256_in_prefix": "6225d41fd8a02dc680286a7cc0ddc2bb31abb54ff667d05f2f00b7c5daa2f420", "size_in_bytes": 38184}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/pmsampling-misaligned-passes.png", "path_type": "hardlink", "sha256": "8037579cc6a0785a899fbdb4a3df689e00a40e84ec3a600403e696d194812275", "sha256_in_prefix": "8037579cc6a0785a899fbdb4a3df689e00a40e84ec3a600403e696d194812275", "size_in_bytes": 154921}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/pmsampling-tensor-example.png", "path_type": "hardlink", "sha256": "20f58a21eb9b230c8e65a90888518fdafbb691a0bc6c6850f27e52083da736d9", "sha256_in_prefix": "20f58a21eb9b230c8e65a90888518fdafbb691a0bc6c6850f27e52083da736d9", "size_in_bytes": 127100}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profile-series-action.png", "path_type": "hardlink", "sha256": "c1955b9e05f144a3eb38c83f2c81f116df0314d704121986488d561632839446", "sha256_in_prefix": "c1955b9e05f144a3eb38c83f2c81f116df0314d704121986488d561632839446", "size_in_bytes": 9199}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profile-series-dialog.png", "path_type": "hardlink", "sha256": "b921293a1a7c8b72b7454de09e44be03203b2ff30c203dbb4195c39202a4ab46", "sha256_in_prefix": "b921293a1a7c8b72b7454de09e44be03203b2ff30c203dbb4195c39202a4ab46", "size_in_bytes": 20064}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiled-process.png", "path_type": "hardlink", "sha256": "9be19b19ff44cdbf0354de19cf24b8686a6a3b930f18f68e57a9e5d03d3e4abb", "sha256_in_prefix": "9be19b19ff44cdbf0354de19cf24b8686a6a3b930f18f68e57a9e5d03d3e4abb", "size_in_bytes": 34816}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-header-filter-dialog.png", "path_type": "hardlink", "sha256": "e3550d9a2699262d8d94b865d338c4ccb3525681ce2be0ecf261baba75986c55", "sha256_in_prefix": "e3550d9a2699262d8d94b865d338c4ccb3525681ce2be0ecf261baba75986c55", "size_in_bytes": 14477}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-header.png", "path_type": "hardlink", "sha256": "523d9622bc5f4e199eaae73cb5aa814a66f93514865d5a8cdd9bc50862256c75", "sha256_in_prefix": "523d9622bc5f4e199eaae73cb5aa814a66f93514865d5a8cdd9bc50862256c75", "size_in_bytes": 33486}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-callstack-python.png", "path_type": "hardlink", "sha256": "921d4415713b928b503e217d03f711d37574b5e94bd9e778808f217167d4ac72", "sha256_in_prefix": "921d4415713b928b503e217d03f711d37574b5e94bd9e778808f217167d4ac72", "size_in_bytes": 135993}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-callstack.png", "path_type": "hardlink", "sha256": "b6d5def3f10f8313a1d89dc10b363520b6c4912839d24f7c055878e39fc0290f", "sha256_in_prefix": "b6d5def3f10f8313a1d89dc10b363520b6c4912839d24f7c055878e39fc0290f", "size_in_bytes": 76069}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-details-comments.png", "path_type": "hardlink", "sha256": "1a7a7233a2a84f1b64809a82c0b2a1a9e06004119f562f4ce00f2c731c7fdc53", "sha256_in_prefix": "1a7a7233a2a84f1b64809a82c0b2a1a9e06004119f562f4ce00f2c731c7fdc53", "size_in_bytes": 2764}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-details-source-table.png", "path_type": "hardlink", "sha256": "8ec13c87d1c37a34b780c79cc8d181e4536b89e67d81e422b30d4271d460a063", "sha256_in_prefix": "8ec13c87d1c37a34b780c79cc8d181e4536b89e67d81e422b30d4271d460a063", "size_in_bytes": 55263}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-details-timeline.png", "path_type": "hardlink", "sha256": "c04f64ac3063577b5571721eb883e1877a29eaeb4b43eba20aa31373b746ab26", "sha256_in_prefix": "c04f64ac3063577b5571721eb883e1877a29eaeb4b43eba20aa31373b746ab26", "size_in_bytes": 82448}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-fix-column.png", "path_type": "hardlink", "sha256": "fcc99cfb2e557570a67714d43627635a5153a06768a408b256fda540755dd999", "sha256_in_prefix": "fcc99cfb2e557570a67714d43627635a5153a06768a408b256fda540755dd999", "size_in_bytes": 4381}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-inline-functions-table.png", "path_type": "hardlink", "sha256": "242473d52782ce477a85b31228c03e37aa617de84719aee76e829102ee6e0b5e", "sha256_in_prefix": "242473d52782ce477a85b31228c03e37aa617de84719aee76e829102ee6e0b5e", "size_in_bytes": 184348}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-nvtx.png", "path_type": "hardlink", "sha256": "139952bc012c32bc1c33bdf6befcd51d007ed52161fdf6ec547fe06e1e0bdafe", "sha256_in_prefix": "139952bc012c32bc1c33bdf6befcd51d007ed52161fdf6ec547fe06e1e0bdafe", "size_in_bytes": 5645}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-section-bodies.png", "path_type": "hardlink", "sha256": "30b463cff99a4ab2100dcef8f142039e91633a5378da8882470b0af67bb8eb7a", "sha256_in_prefix": "30b463cff99a4ab2100dcef8f142039e91633a5378da8882470b0af67bb8eb7a", "size_in_bytes": 97538}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-section-rooflines.png", "path_type": "hardlink", "sha256": "975d796c1376af819a77680b1970f92e6617745b51b50e746b34f01210a2bb12", "sha256_in_prefix": "975d796c1376af819a77680b1970f92e6617745b51b50e746b34f01210a2bb12", "size_in_bytes": 61741}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-section-with-rule.png", "path_type": "hardlink", "sha256": "e4277adff66e6b70bfb6d5b26d42bb851cd1f59c3db81024706e6ea2e5d47913", "sha256_in_prefix": "e4277adff66e6b70bfb6d5b26d42bb851cd1f59c3db81024706e6ea2e5d47913", "size_in_bytes": 172578}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-collapse.png", "path_type": "hardlink", "sha256": "97ba3325b932fb41991268c8d31607ae29d73f1a9e136f4a65c155b3dfa5f4ba", "sha256_in_prefix": "97ba3325b932fb41991268c8d31607ae29d73f1a9e136f4a65c155b3dfa5f4ba", "size_in_bytes": 81745}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-column-chooser.png", "path_type": "hardlink", "sha256": "650642ce8c866908c4e41be7f4c22ac7168668a99757a7b50077e26f8777bcae", "sha256_in_prefix": "650642ce8c866908c4e41be7f4c22ac7168668a99757a7b50077e26f8777bcae", "size_in_bytes": 42229}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-heatmap.png", "path_type": "hardlink", "sha256": "511de68dd3a0cf25de7bebfd6ae2ef833d2070d62de3a06ebce3bcbc7384d035", "sha256_in_prefix": "511de68dd3a0cf25de7bebfd6ae2ef833d2070d62de3a06ebce3bcbc7384d035", "size_in_bytes": 1127}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-markers-table.png", "path_type": "hardlink", "sha256": "1d28ac61874a2715087d98ea5dda0b65531fa5cc99260bd7bc889890fd568b88", "sha256_in_prefix": "1d28ac61874a2715087d98ea5dda0b65531fa5cc99260bd7bc889890fd568b88", "size_in_bytes": 187075}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-markers.png", "path_type": "hardlink", "sha256": "9fb7dc57c7cd45743da6eeb6fae438064a127d658812ac9be24c4980a32f0400", "sha256_in_prefix": "9fb7dc57c7cd45743da6eeb6fae438064a127d658812ac9be24c4980a32f0400", "size_in_bytes": 55152}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-navigate-by.png", "path_type": "hardlink", "sha256": "ec7c030bd580a45e22dc6c5e8671848e6c516314c4b7074f752944ef3c9dc196", "sha256_in_prefix": "ec7c030bd580a45e22dc6c5e8671848e6c516314c4b7074f752944ef3c9dc196", "size_in_bytes": 43672}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-profiles-button.png", "path_type": "hardlink", "sha256": "9e9708de795d26b7bf65ad283d82103092be3a066c57c3a4b9b085ec4e3b53fb", "sha256_in_prefix": "9e9708de795d26b7bf65ad283d82103092be3a066c57c3a4b9b085ec4e3b53fb", "size_in_bytes": 19716}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-profiles.png", "path_type": "hardlink", "sha256": "a592d3d13cb216528a2f5a95c076cc93ff12fc8441c78d19f9c5d0631536187d", "sha256_in_prefix": "a592d3d13cb216528a2f5a95c076cc93ff12fc8441c78d19f9c5d0631536187d", "size_in_bytes": 20120}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-register-dependencies.png", "path_type": "hardlink", "sha256": "5f99505405e9e7e28b682aa8ad7ba173495a9a4a934c35f08ea74914fb3c59c6", "sha256_in_prefix": "5f99505405e9e7e28b682aa8ad7ba173495a9a4a934c35f08ea74914fb3c59c6", "size_in_bytes": 172524}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-rel-abs.png", "path_type": "hardlink", "sha256": "e7989015f7c22e7afd66c8905c22d77b2aaa9090e954c6d7e17a353dffccfdcc", "sha256_in_prefix": "e7989015f7c22e7afd66c8905c22d77b2aaa9090e954c6d7e17a353dffccfdcc", "size_in_bytes": 253209}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-resolve.png", "path_type": "hardlink", "sha256": "62c2d6df1c836f28645e1e033cb1bbed6be9851b159a35608fb0528995818e7a", "sha256_in_prefix": "62c2d6df1c836f28645e1e033cb1bbed6be9851b159a35608fb0528995818e7a", "size_in_bytes": 57565}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-search.png", "path_type": "hardlink", "sha256": "9dae48da10e5497571af13ec069529f5d233233b6804244be3e85692c1690d34", "sha256_in_prefix": "9dae48da10e5497571af13ec069529f5d233233b6804244be3e85692c1690d34", "size_in_bytes": 268270}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source-statistics.png", "path_type": "hardlink", "sha256": "7e8c08d21242ca1251b2e3d34e96cbcf5fafc1ed8bb83fe36a1990dde687588c", "sha256_in_prefix": "7e8c08d21242ca1251b2e3d34e96cbcf5fafc1ed8bb83fe36a1990dde687588c", "size_in_bytes": 119735}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-source.png", "path_type": "hardlink", "sha256": "c39ecda3336b9f2dbdea1c5a88bfcdc07eb469b119981e3464084f34e96c9fc6", "sha256_in_prefix": "c39ecda3336b9f2dbdea1c5a88bfcdc07eb469b119981e3464084f34e96c9fc6", "size_in_bytes": 482970}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-summary-rules.png", "path_type": "hardlink", "sha256": "0776a83c8ffcb25398e514b43bf9247370647e010465168b206ab8b96fedfb2d", "sha256_in_prefix": "0776a83c8ffcb25398e514b43bf9247370647e010465168b206ab8b96fedfb2d", "size_in_bytes": 177513}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-summary-table.png", "path_type": "hardlink", "sha256": "04299f219d8fde6bb2ee58f12ee8142f140ff76e989d8c6409d67be0e68fcd54", "sha256_in_prefix": "04299f219d8fde6bb2ee58f12ee8142f140ff76e989d8c6409d67be0e68fcd54", "size_in_bytes": 91896}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/profiler-report-pages-summary.png", "path_type": "hardlink", "sha256": "2828842512c800112b183674613978de9833dd7d7ee311b55501b95a6c9622f4", "sha256_in_prefix": "2828842512c800112b183674613978de9833dd7d7ee311b55501b95a6c9622f4", "size_in_bytes": 196171}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/progress-log.png", "path_type": "hardlink", "sha256": "f3b712b016b0069e44c401b3fa95a03e687c9a9b2028f2453931c901013c88ee", "sha256_in_prefix": "f3b712b016b0069e44c401b3fa95a03e687c9a9b2028f2453931c901013c88ee", "size_in_bytes": 51847}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/projects-explorer.png", "path_type": "hardlink", "sha256": "c37b74207f10d37049a2f3d9d5dff31103d63eac6c29ceeeae5fe7d2c502f590", "sha256_in_prefix": "c37b74207f10d37049a2f3d9d5dff31103d63eac6c29ceeeae5fe7d2c502f590", "size_in_bytes": 5408}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-baseline.png", "path_type": "hardlink", "sha256": "1396f4c9318e5898d2101b493c84f6322d8a6b8594580d544de719eea74c0210", "sha256_in_prefix": "1396f4c9318e5898d2101b493c84f6322d8a6b8594580d544de719eea74c0210", "size_in_bytes": 99197}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-api-stream.png", "path_type": "hardlink", "sha256": "5acb58b2bb3f28e09746ad2973933b991a18ca2f0f289c68a5aaf4ad42cfae8c", "sha256_in_prefix": "5acb58b2bb3f28e09746ad2973933b991a18ca2f0f289c68a5aaf4ad42cfae8c", "size_in_bytes": 49485}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-attach.png", "path_type": "hardlink", "sha256": "933740ce7683e9ff9ea9821d0b0a5e0dd8b0dd566d55c968e109326b29a8d06d", "sha256_in_prefix": "933740ce7683e9ff9ea9821d0b0a5e0dd8b0dd566d55c968e109326b29a8d06d", "size_in_bytes": 52533}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-connect.png", "path_type": "hardlink", "sha256": "0c628560b3ef830a7620fac1cfabdaafc4feb44606b849194dee5d2f725d485b", "sha256_in_prefix": "0c628560b3ef830a7620fac1cfabdaafc4feb44606b849194dee5d2f725d485b", "size_in_bytes": 56258}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-connected.png", "path_type": "hardlink", "sha256": "25bbca9658e0c58ec2373be7ef49d7fea6bced353b812b4fba667e03eb75e963", "sha256_in_prefix": "25bbca9658e0c58ec2373be7ef49d7fea6bced353b812b4fba667e03eb75e963", "size_in_bytes": 63723}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-interactive-profiling-next-launch.png", "path_type": "hardlink", "sha256": "2503edd3d107a46894365176d7c12fd9f2df92bdd951fce282f3c07d8d4cf7c2", "sha256_in_prefix": "2503edd3d107a46894365176d7c12fd9f2df92bdd951fce282f3c07d8d4cf7c2", "size_in_bytes": 49582}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-profiling-connect.png", "path_type": "hardlink", "sha256": "f045fbd36306b61767ad596bcf4526f20e3635f5e22351d2e724524fe028c118", "sha256_in_prefix": "f045fbd36306b61767ad596bcf4526f20e3635f5e22351d2e724524fe028c118", "size_in_bytes": 66919}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-profiling-options-sections.png", "path_type": "hardlink", "sha256": "1e55a5c49b57bf06a7190ce7cb6c956267068a85dbd40c11d2670a29e0da7b2a", "sha256_in_prefix": "1e55a5c49b57bf06a7190ce7cb6c956267068a85dbd40c11d2670a29e0da7b2a", "size_in_bytes": 70678}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-report-summary.png", "path_type": "hardlink", "sha256": "2828842512c800112b183674613978de9833dd7d7ee311b55501b95a6c9622f4", "sha256_in_prefix": "2828842512c800112b183674613978de9833dd7d7ee311b55501b95a6c9622f4", "size_in_bytes": 196171}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-report.png", "path_type": "hardlink", "sha256": "49ac96a818ee5d331cbbb3762c29e9eb0b1b171ed9c6efe1d15925677987be0b", "sha256_in_prefix": "49ac96a818ee5d331cbbb3762c29e9eb0b1b171ed9c6efe1d15925677987be0b", "size_in_bytes": 169483}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-rule.png", "path_type": "hardlink", "sha256": "eaba063797ee82a1342eaceb98fcf018481c6723cad29d8949acbb0130baf6e9", "sha256_in_prefix": "eaba063797ee82a1342eaceb98fcf018481c6723cad29d8949acbb0130baf6e9", "size_in_bytes": 43418}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-system-trace-connect.png", "path_type": "hardlink", "sha256": "acc2c16a700a79eb22fcb6691cb112d4a14fd4e9784ec79149752d9b793a0779", "sha256_in_prefix": "acc2c16a700a79eb22fcb6691cb112d4a14fd4e9784ec79149752d9b793a0779", "size_in_bytes": 65121}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-system-trace-options.png", "path_type": "hardlink", "sha256": "997bd193b4715246121b6cd5706fbd1747e7659ebdf0f150e055786b852c84ea", "sha256_in_prefix": "997bd193b4715246121b6cd5706fbd1747e7659ebdf0f150e055786b852c84ea", "size_in_bytes": 15044}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/quick-start-system-trace-timeline.png", "path_type": "hardlink", "sha256": "8a9b09e6a0fee86a304345aa4d73c9017f7f351c4bf5932b9ae27ad0bc7f6fae", "sha256_in_prefix": "8a9b09e6a0fee86a304345aa4d73c9017f7f351c4bf5932b9ae27ad0bc7f6fae", "size_in_bytes": 107777}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/regular-application-process.png", "path_type": "hardlink", "sha256": "c074d663f8b7c5cac3f1dd6088863e1421b880e375e24c2d0c0c0c4fe0f66129", "sha256_in_prefix": "c074d663f8b7c5cac3f1dd6088863e1421b880e375e24c2d0c0c0c4fe0f66129", "size_in_bytes": 21702}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/replay-application-kernel-matching.png", "path_type": "hardlink", "sha256": "d30746caa9cf536bfd7f3a067724ba55fd90eb5d5e5bafbb0375b1bc6e85f9ba", "sha256_in_prefix": "d30746caa9cf536bfd7f3a067724ba55fd90eb5d5e5bafbb0375b1bc6e85f9ba", "size_in_bytes": 61693}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/replay-application-range.png", "path_type": "hardlink", "sha256": "55d223b07a7e922d6ef61caae2fed5e15265e33066a8f32cd35b352e27b8c6b9", "sha256_in_prefix": "55d223b07a7e922d6ef61caae2fed5e15265e33066a8f32cd35b352e27b8c6b9", "size_in_bytes": 31910}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/replay-application.png", "path_type": "hardlink", "sha256": "1418699fee530cefd74c5807ad2ec560ca1b75079c5b8b747c335bb525f2a689", "sha256_in_prefix": "1418699fee530cefd74c5807ad2ec560ca1b75079c5b8b747c335bb525f2a689", "size_in_bytes": 16149}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/replay-kernel.png", "path_type": "hardlink", "sha256": "861acd575c0f5aef1bd112121d66bd587a0be9eb6997bc8dfcc4e5e707337c85", "sha256_in_prefix": "861acd575c0f5aef1bd112121d66bd587a0be9eb6997bc8dfcc4e5e707337c85", "size_in_bytes": 19775}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/replay-range.png", "path_type": "hardlink", "sha256": "6e2c678a6eb14901dcb0168b720413607a8f51088f73e4753cf618d756ae08da", "sha256_in_prefix": "6e2c678a6eb14901dcb0168b720413607a8f51088f73e4753cf618d756ae08da", "size_in_bytes": 29920}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/replay-regular-execution.png", "path_type": "hardlink", "sha256": "c7c9eb245d283979063dc0f9a3868601db829cba8e0bf536db7d7b4cdfd01941", "sha256_in_prefix": "c7c9eb245d283979063dc0f9a3868601db829cba8e0bf536db7d7b4cdfd01941", "size_in_bytes": 7029}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/roofline-analysis.png", "path_type": "hardlink", "sha256": "b440bd2fb7a99391c9de23ecbeb2c39e7f4aa5676cc93099a5d5a9de1b453c9c", "sha256_in_prefix": "b440bd2fb7a99391c9de23ecbeb2c39e7f4aa5676cc93099a5d5a9de1b453c9c", "size_in_bytes": 54809}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/roofline-overview.png", "path_type": "hardlink", "sha256": "35761d8c75bb3e5a10979b104c41822f6ee9c61f7f0824323d95c0cafb7c31f4", "sha256_in_prefix": "35761d8c75bb3e5a10979b104c41822f6ee9c61f7f0824323d95c0cafb7c31f4", "size_in_bytes": 59615}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/section-files-2.png", "path_type": "hardlink", "sha256": "d80421b754fa1cf4ca2589ca31c61ad6ef03cf8b65a13374c4d35abc5c7f9034", "sha256_in_prefix": "d80421b754fa1cf4ca2589ca31c61ad6ef03cf8b65a13374c4d35abc5c7f9034", "size_in_bytes": 51327}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/section-files.png", "path_type": "hardlink", "sha256": "2bba5a430958e880dea9cc78833af832b96d781a7812a39dc2115487f15fa5ab", "sha256_in_prefix": "2bba5a430958e880dea9cc78833af832b96d781a7812a39dc2115487f15fa5ab", "size_in_bytes": 2933}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/sm-selection-dialog.png", "path_type": "hardlink", "sha256": "2dbdcc46c8feea3bbfbd4a40a96f495c9aa01ca846f6d654449d34b0d1e3c68c", "sha256_in_prefix": "2dbdcc46c8feea3bbfbd4a40a96f495c9aa01ca846f6d654449d34b0d1e3c68c", "size_in_bytes": 4083}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/source-comparison-diff-by-menu.png", "path_type": "hardlink", "sha256": "b5867e7790cec5aa6bbd569f3cd74dc0608f00c0708be23422e6cba5da1dc615", "sha256_in_prefix": "b5867e7790cec5aa6bbd569f3cd74dc0608f00c0708be23422e6cba5da1dc615", "size_in_bytes": 276373}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/source-comparison-document.png", "path_type": "hardlink", "sha256": "be00bdadbde84f3fcd9f64ae17a8170f1f5ed2f522031e1b6b4a8c7ad0e72496", "sha256_in_prefix": "be00bdadbde84f3fcd9f64ae17a8170f1f5ed2f522031e1b6b4a8c7ad0e72496", "size_in_bytes": 180135}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/source-comparison-from-header.png", "path_type": "hardlink", "sha256": "7881026e0f06dde253e424eb918a7036a2273fc986ae7e8c4a0d12648268ddee", "sha256_in_prefix": "7881026e0f06dde253e424eb918a7036a2273fc986ae7e8c4a0d12648268ddee", "size_in_bytes": 72107}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/source-comparison-navigation-buttons.png", "path_type": "hardlink", "sha256": "02d7b3670c3542c83812bc0db97f349a21072c0da3548a270319248c1ed25808", "sha256_in_prefix": "02d7b3670c3542c83812bc0db97f349a21072c0da3548a270319248c1ed25808", "size_in_bytes": 217063}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/source-counters.png", "path_type": "hardlink", "sha256": "823ebe46de95d3a757e8c21140dd8723a18afaa6eba0e0db90f45a6a5eed3f12", "sha256_in_prefix": "823ebe46de95d3a757e8c21140dd8723a18afaa6eba0e0db90f45a6a5eed3f12", "size_in_bytes": 71869}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/status-banner.png", "path_type": "hardlink", "sha256": "991be324c960391b17658a3a83dd1fe4bd64eeb95fb5979d1f4a07670a3ff10d", "sha256_in_prefix": "991be324c960391b17658a3a83dd1fe4bd64eeb95fb5979d1f4a07670a3ff10d", "size_in_bytes": 11872}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-api-statistics.png", "path_type": "hardlink", "sha256": "975d1218511bb68ebe820d618278c0210b721c4c0d1f85b096996e2ed3d811de", "sha256_in_prefix": "975d1218511bb68ebe820d618278c0210b721c4c0d1f85b096996e2ed3d811de", "size_in_bytes": 26203}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-api-stream.png", "path_type": "hardlink", "sha256": "223b1a49890cb052fdadff3867094e81fb8e53e665644028d7067ab848821542", "sha256_in_prefix": "223b1a49890cb052fdadff3867094e81fb8e53e665644028d7067ab848821542", "size_in_bytes": 49637}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-baselines.png", "path_type": "hardlink", "sha256": "815ca8b6cab7e6b2c8dcd90dc8713d9be0587f05c3ece9143c76b1394065ebc5", "sha256_in_prefix": "815ca8b6cab7e6b2c8dcd90dc8713d9be0587f05c3ece9143c76b1394065ebc5", "size_in_bytes": 30112}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-callstack.png", "path_type": "hardlink", "sha256": "3ff3d405b2e347c32c307dcdae412e4351e8e7773cff82cdcd6dfedd20eb3e7b", "sha256_in_prefix": "3ff3d405b2e347c32c307dcdae412e4351e8e7773cff82cdcd6dfedd20eb3e7b", "size_in_bytes": 77203}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-launch-details.png", "path_type": "hardlink", "sha256": "7551dbe5ce4551c92ed5474e2f1b7955946722865d4d4df17742ef160c9f421b", "sha256_in_prefix": "7551dbe5ce4551c92ed5474e2f1b7955946722865d4d4df17742ef160c9f421b", "size_in_bytes": 63881}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-metric-details.png", "path_type": "hardlink", "sha256": "f1f5673434d5f5bd37e047a899f849423a67eec62db3903e0c190ef50636198e", "sha256_in_prefix": "f1f5673434d5f5bd37e047a899f849423a67eec62db3903e0c190ef50636198e", "size_in_bytes": 110068}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-nvtx-resources.png", "path_type": "hardlink", "sha256": "5a81680fd720ebe0efc6073854253bae0f6dcc46150345b06755106373ba6245", "sha256_in_prefix": "5a81680fd720ebe0efc6073854253bae0f6dcc46150345b06755106373ba6245", "size_in_bytes": 7467}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-nvtx.png", "path_type": "hardlink", "sha256": "8e1d714f01e95069dcf86674af69226d350beaae82c7d4b48aaf67230c2fdf03", "sha256_in_prefix": "8e1d714f01e95069dcf86674af69226d350beaae82c7d4b48aaf67230c2fdf03", "size_in_bytes": 8102}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-resources.png", "path_type": "hardlink", "sha256": "fef2c61e7061a0cfb656fc31a08b2fe4069813d3170244d5ec7e44a1780564bb", "sha256_in_prefix": "fef2c61e7061a0cfb656fc31a08b2fe4069813d3170244d5ec7e44a1780564bb", "size_in_bytes": 10710}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-section-sets.png", "path_type": "hardlink", "sha256": "bbc68df06a5b7b92cc67b11a19dab008bae25fd93ee02e590821a17a9d7ebdd0", "sha256_in_prefix": "bbc68df06a5b7b92cc67b11a19dab008bae25fd93ee02e590821a17a9d7ebdd0", "size_in_bytes": 100144}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/tool-window-sections.png", "path_type": "hardlink", "sha256": "6471af17f39258ecdd075235f69faea3f732bd14d43243708ba9b522945dbe13", "sha256_in_prefix": "6471af17f39258ecdd075235f69faea3f732bd14d43243708ba9b522945dbe13", "size_in_bytes": 180534}, {"_path": "Library/nsight-compute/2025.1.1/docs/_images/welcome-page.png", "path_type": "hardlink", "sha256": "441b033e4d255ad39851b3407c33c529f2caaf53a2d05009ef5def43bdb39510", "sha256_in_prefix": "441b033e4d255ad39851b3407c33c529f2caaf53a2d05009ef5def43bdb39510", "size_in_bytes": 64417}, {"_path": "Library/nsight-compute/2025.1.1/docs/_sphinx_design_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "path_type": "hardlink", "sha256": "35ef98809846a30b25ae6f30881f451a1d4ae9fabdc409eed05d502b724d33c6", "sha256_in_prefix": "35ef98809846a30b25ae6f30881f451a1d4ae9fabdc409eed05d502b724d33c6", "size_in_bytes": 48386}, {"_path": "Library/nsight-compute/2025.1.1/docs/_sphinx_design_static/design-tabs.js", "path_type": "hardlink", "sha256": "00a9989c6d54254f32a37e50c7bbc5dab737674231aa2553421f4939624b23e3", "sha256_in_prefix": "00a9989c6d54254f32a37e50c7bbc5dab737674231aa2553421f4939624b23e3", "size_in_bytes": 770}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/NVIDIA-LogoBlack.svg", "path_type": "hardlink", "sha256": "2b3902d0845adc134d656d464864abd3802a3e09bace5fc70a3c146894648c2a", "sha256_in_prefix": "2b3902d0845adc134d656d464864abd3802a3e09bace5fc70a3c146894648c2a", "size_in_bytes": 2494}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/NVIDIA-LogoWhite.svg", "path_type": "hardlink", "sha256": "ef126c03f44ba0184e158ddc9a177e8adb3f3c175ca80aea3be30d3ca8eb1126", "sha256_in_prefix": "ef126c03f44ba0184e158ddc9a177e8adb3f3c175ca80aea3be30d3ca8eb1126", "size_in_bytes": 3722}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/api-styles-dark.css", "path_type": "hardlink", "sha256": "cdbb2149e908ca91a86f3c9c17bac9b94e96945f3c6f824b69256742a086c5d7", "sha256_in_prefix": "cdbb2149e908ca91a86f3c9c17bac9b94e96945f3c6f824b69256742a086c5d7", "size_in_bytes": 1449}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/api-styles.css", "path_type": "hardlink", "sha256": "df38c2631c3d9de6cc0a2a336f27ee6c13d67f8bfb1cb98267db3cb94c700645", "sha256_in_prefix": "df38c2631c3d9de6cc0a2a336f27ee6c13d67f8bfb1cb98267db3cb94c700645", "size_in_bytes": 2436}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/basic.css", "path_type": "hardlink", "sha256": "55e3151630469fe605cc1730536cc11c0f3c5685796f4ea1392c24709b094386", "sha256_in_prefix": "55e3151630469fe605cc1730536cc11c0f3c5685796f4ea1392c24709b094386", "size_in_bytes": 14692}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/badge_only.css", "path_type": "hardlink", "sha256": "c4050fa47d8bb6297c79811f663e6cfa32cb6b783b47eaeddd6ba50d5cf1a666", "sha256_in_prefix": "c4050fa47d8bb6297c79811f663e6cfa32cb6b783b47eaeddd6ba50d5cf1a666", "size_in_bytes": 3275}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/Roboto-Slab-Bold.woff", "path_type": "hardlink", "sha256": "9fec87cadbe2413b255f1ec577573a83f1ca2e1c37aa023dbebcd3a7b864636a", "sha256_in_prefix": "9fec87cadbe2413b255f1ec577573a83f1ca2e1c37aa023dbebcd3a7b864636a", "size_in_bytes": 87624}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/Roboto-Slab-Bold.woff2", "path_type": "hardlink", "sha256": "1a0c024dd1a267c52d5575469ffe8570d1e84164de7d393cf3414bafd17d7a0c", "sha256_in_prefix": "1a0c024dd1a267c52d5575469ffe8570d1e84164de7d393cf3414bafd17d7a0c", "size_in_bytes": 67312}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/Roboto-Slab-Regular.woff", "path_type": "hardlink", "sha256": "9f32630e2c0c5135bf1e86e36cb65b3932e4410644235bc2bd995e9c7f6ff117", "sha256_in_prefix": "9f32630e2c0c5135bf1e86e36cb65b3932e4410644235bc2bd995e9c7f6ff117", "size_in_bytes": 86288}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/Roboto-Slab-Regular.woff2", "path_type": "hardlink", "sha256": "874e42222856d7af03b3f438d21d923a4280d47fe67c48510e2174a1579795ef", "sha256_in_prefix": "874e42222856d7af03b3f438d21d923a4280d47fe67c48510e2174a1579795ef", "size_in_bytes": 66444}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.eot", "path_type": "hardlink", "sha256": "7bfcab6db99d5cfbf1705ca0536ddc78585432cc5fa41bbd7ad0f009033b2979", "sha256_in_prefix": "7bfcab6db99d5cfbf1705ca0536ddc78585432cc5fa41bbd7ad0f009033b2979", "size_in_bytes": 165742}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.svg", "path_type": "hardlink", "sha256": "ad6157926c1622ba4e1d03d478f1541368524bfc46f51e42fe0d945f7ef323e4", "sha256_in_prefix": "ad6157926c1622ba4e1d03d478f1541368524bfc46f51e42fe0d945f7ef323e4", "size_in_bytes": 444379}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.ttf", "path_type": "hardlink", "sha256": "aa58f33f239a0fb02f5c7a6c45c043d7a9ac9a093335806694ecd6d4edc0d6a8", "sha256_in_prefix": "aa58f33f239a0fb02f5c7a6c45c043d7a9ac9a093335806694ecd6d4edc0d6a8", "size_in_bytes": 165548}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.woff", "path_type": "hardlink", "sha256": "ba0c59deb5450f5cb41b3f93609ee2d0d995415877ddfa223e8a8a7533474f07", "sha256_in_prefix": "ba0c59deb5450f5cb41b3f93609ee2d0d995415877ddfa223e8a8a7533474f07", "size_in_bytes": 98024}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/fontawesome-webfont.woff2", "path_type": "hardlink", "sha256": "2adefcbc041e7d18fcf2d417879dc5a09997aa64d675b7a3c4b6ce33da13f3fe", "sha256_in_prefix": "2adefcbc041e7d18fcf2d417879dc5a09997aa64d675b7a3c4b6ce33da13f3fe", "size_in_bytes": 77160}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-bold-italic.woff", "path_type": "hardlink", "sha256": "980c8592e5488df256192c999e92db8fd302db8cd8909b7fa266a684e37e45f8", "sha256_in_prefix": "980c8592e5488df256192c999e92db8fd302db8cd8909b7fa266a684e37e45f8", "size_in_bytes": 323344}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-bold-italic.woff2", "path_type": "hardlink", "sha256": "c0916a33340d063f7b05679e08031e729d1888444706f04804705da5966d895d", "sha256_in_prefix": "c0916a33340d063f7b05679e08031e729d1888444706f04804705da5966d895d", "size_in_bytes": 193308}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-bold.woff", "path_type": "hardlink", "sha256": "0e56b17d142eb366c8007031d14e34da48c70b4a9d9a0ca492e696a7bae45e1e", "sha256_in_prefix": "0e56b17d142eb366c8007031d14e34da48c70b4a9d9a0ca492e696a7bae45e1e", "size_in_bytes": 309728}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-bold.woff2", "path_type": "hardlink", "sha256": "ae88fc0d7a961832f809527d30bd3983a6866d42f66a56ade23f543681594db6", "sha256_in_prefix": "ae88fc0d7a961832f809527d30bd3983a6866d42f66a56ade23f543681594db6", "size_in_bytes": 184912}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-normal-italic.woff", "path_type": "hardlink", "sha256": "26318a1467a5e5caf10b04cfa942d079632560cd7a29cec565fd1dc9f7ec5081", "sha256_in_prefix": "26318a1467a5e5caf10b04cfa942d079632560cd7a29cec565fd1dc9f7ec5081", "size_in_bytes": 328412}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-normal-italic.woff2", "path_type": "hardlink", "sha256": "4465765f2f6eddcdad34ffd7cab559e56bc0e75e45e192f85e9562b0771481dc", "sha256_in_prefix": "4465765f2f6eddcdad34ffd7cab559e56bc0e75e45e192f85e9562b0771481dc", "size_in_bytes": 195704}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-normal.woff", "path_type": "hardlink", "sha256": "5b9025dda4d7688e3311b0c17eddc501133b807def33effaef6593843cf5416e", "sha256_in_prefix": "5b9025dda4d7688e3311b0c17eddc501133b807def33effaef6593843cf5416e", "size_in_bytes": 309192}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/fonts/lato-normal.woff2", "path_type": "hardlink", "sha256": "983b0caf336e8542214fc17019a4fc5e0360864b92806ca14d55c1fc1c2c5a0f", "sha256_in_prefix": "983b0caf336e8542214fc17019a4fc5e0360864b92806ca14d55c1fc1c2c5a0f", "size_in_bytes": 182708}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/css/theme.css", "path_type": "hardlink", "sha256": "af78284d3c7a8ab0917208ddc2a71854d4b552802104a7146f8705dca8dd88d1", "sha256_in_prefix": "af78284d3c7a8ab0917208ddc2a71854d4b552802104a7146f8705dca8dd88d1", "size_in_bytes": 129674}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "path_type": "hardlink", "sha256": "35ef98809846a30b25ae6f30881f451a1d4ae9fabdc409eed05d502b724d33c6", "sha256_in_prefix": "35ef98809846a30b25ae6f30881f451a1d4ae9fabdc409eed05d502b724d33c6", "size_in_bytes": 48386}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/design-tabs.js", "path_type": "hardlink", "sha256": "00a9989c6d54254f32a37e50c7bbc5dab737674231aa2553421f4939624b23e3", "sha256_in_prefix": "00a9989c6d54254f32a37e50c7bbc5dab737674231aa2553421f4939624b23e3", "size_in_bytes": 770}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/doctools.js", "path_type": "hardlink", "sha256": "b5cad4208b5895e6182a3d6ba2a28c38ba4c3ed7ddff4635839aa430eee59614", "sha256_in_prefix": "b5cad4208b5895e6182a3d6ba2a28c38ba4c3ed7ddff4635839aa430eee59614", "size_in_bytes": 10766}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/documentation_options.js", "path_type": "hardlink", "sha256": "528a489fb05ef774fc1452414dd2714ec9263deeb102d838baf1c1a6f79224f8", "sha256_in_prefix": "528a489fb05ef774fc1452414dd2714ec9263deeb102d838baf1c1a6f79224f8", "size_in_bytes": 422}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/favicon.ico", "path_type": "hardlink", "sha256": "2e4c2d0a267ad715477ab9b3ba55358d6af5afbdee9198ffd76111bb08972575", "sha256_in_prefix": "2e4c2d0a267ad715477ab9b3ba55358d6af5afbdee9198ffd76111bb08972575", "size_in_bytes": 15406}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/file.png", "path_type": "hardlink", "sha256": "5c4bc9a16aebf38c4b950f59b8e501ca36495328cb9eb622218bce9064a35e3e", "sha256_in_prefix": "5c4bc9a16aebf38c4b950f59b8e501ca36495328cb9eb622218bce9064a35e3e", "size_in_bytes": 286}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/jquery-3.5.1.js", "path_type": "hardlink", "sha256": "416a3b2c3bf16d64f6b5b6d0f7b079df2267614dd6847fc2f3271b4409233c37", "sha256_in_prefix": "416a3b2c3bf16d64f6b5b6d0f7b079df2267614dd6847fc2f3271b4409233c37", "size_in_bytes": 287630}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/jquery.js", "path_type": "hardlink", "sha256": "f7f6a5894f1d19ddad6fa392b2ece2c5e578cbf7da4ea805b6885eb6985b6e3d", "sha256_in_prefix": "f7f6a5894f1d19ddad6fa392b2ece2c5e578cbf7da4ea805b6885eb6985b6e3d", "size_in_bytes": 89476}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/js/badge_only.js", "path_type": "hardlink", "sha256": "f0a4808d04c4d55378751ac096a8376b64b1a704c82584b0ee590212cf413013", "sha256_in_prefix": "f0a4808d04c4d55378751ac096a8376b64b1a704c82584b0ee590212cf413013", "size_in_bytes": 934}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/js/html5shiv-printshiv.min.js", "path_type": "hardlink", "sha256": "b42a7e949a6e21d66b30fbbb4a22deafd9e0ccabc04f0fa2907fc6252fdf165f", "sha256_in_prefix": "b42a7e949a6e21d66b30fbbb4a22deafd9e0ccabc04f0fa2907fc6252fdf165f", "size_in_bytes": 4370}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/js/html5shiv.min.js", "path_type": "hardlink", "sha256": "f6e0283561ddb33b140e14977ffad57163aa28f7e2e7ff15e51e1475b6657b60", "sha256_in_prefix": "f6e0283561ddb33b140e14977ffad57163aa28f7e2e7ff15e51e1475b6657b60", "size_in_bytes": 2734}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/js/theme.js", "path_type": "hardlink", "sha256": "536ad2d746e944c5570cc15badaeccc3c0582a1b66e45511fe4edce32b6da510", "sha256_in_prefix": "536ad2d746e944c5570cc15badaeccc3c0582a1b66e45511fe4edce32b6da510", "size_in_bytes": 5023}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/language_data.js", "path_type": "hardlink", "sha256": "661af9e0ff14a7031d97977920de438313c4649f7fa9998702c9dba550dd4f59", "sha256_in_prefix": "661af9e0ff14a7031d97977920de438313c4649f7fa9998702c9dba550dd4f59", "size_in_bytes": 325}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/lunr.min.js", "path_type": "hardlink", "sha256": "0c50d9002b85780a842afffb567bb54ede402dae7c6dc5997a018614d8044fc8", "sha256_in_prefix": "0c50d9002b85780a842afffb567bb54ede402dae7c6dc5997a018614d8044fc8", "size_in_bytes": 29510}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/lunr_search.js", "path_type": "hardlink", "sha256": "3dccf11ee1a394d2581fbd87ec66c0f6d74be6a3ca302d6d446d29526a8d0395", "sha256_in_prefix": "3dccf11ee1a394d2581fbd87ec66c0f6d74be6a3ca302d6d446d29526a8d0395", "size_in_bytes": 6431}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/main_ov_logo_rect.png", "path_type": "hardlink", "sha256": "b15c15d30ac536c3fc4401c8d10abb699dc5b421d68ef6b394dfb4c617933dda", "sha256_in_prefix": "b15c15d30ac536c3fc4401c8d10abb699dc5b421d68ef6b394dfb4c617933dda", "size_in_bytes": 57423}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/main_ov_logo_square.png", "path_type": "hardlink", "sha256": "06b7ba35792a640bf5725c179b84750d5ab670a88ccc94a8b4360997c08c19b8", "sha256_in_prefix": "06b7ba35792a640bf5725c179b84750d5ab670a88ccc94a8b4360997c08c19b8", "size_in_bytes": 195331}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/mermaid-init.js", "path_type": "hardlink", "sha256": "965280720746f4b5e8053dd059e114dacf87afe6ee4ff4214f2dbf9fb472f75c", "sha256_in_prefix": "965280720746f4b5e8053dd059e114dacf87afe6ee4ff4214f2dbf9fb472f75c", "size_in_bytes": 1293}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/minus.png", "path_type": "hardlink", "sha256": "47e7fc50db3699f1ca41ce9a2ffa202c00c5d1d5180c55f62ba859b1bd6cc008", "sha256_in_prefix": "47e7fc50db3699f1ca41ce9a2ffa202c00c5d1d5180c55f62ba859b1bd6cc008", "size_in_bytes": 90}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/nsight-compute.ico", "path_type": "hardlink", "sha256": "f92fccc470e532adf31761d920ffa23da4a3dc52058d8f984157d069bd620e37", "sha256_in_prefix": "f92fccc470e532adf31761d920ffa23da4a3dc52058d8f984157d069bd620e37", "size_in_bytes": 256267}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/nsight-compute.png", "path_type": "hardlink", "sha256": "de827e5c16259c7a271db1058d626e5ae18cd39c3cf55a00c77b1b9e9d8cd28e", "sha256_in_prefix": "de827e5c16259c7a271db1058d626e5ae18cd39c3cf55a00c77b1b9e9d8cd28e", "size_in_bytes": 42166}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/omni-style-dark.css", "path_type": "hardlink", "sha256": "609a8dae3687e60dc742381d1a5517bd4fb3ebb551cdf32666a04efd55bd3813", "sha256_in_prefix": "609a8dae3687e60dc742381d1a5517bd4fb3ebb551cdf32666a04efd55bd3813", "size_in_bytes": 13790}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/omni-style.css", "path_type": "hardlink", "sha256": "52d65531aa8374b98926bdf0a276d12ee3b931af3a75f7c9fcf272a90ed4d1f0", "sha256_in_prefix": "52d65531aa8374b98926bdf0a276d12ee3b931af3a75f7c9fcf272a90ed4d1f0", "size_in_bytes": 52062}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/plus.png", "path_type": "hardlink", "sha256": "54115199b96a130cba02147c47c0deb43dcc9b9f08b5162bba8642b34980ac63", "sha256_in_prefix": "54115199b96a130cba02147c47c0deb43dcc9b9f08b5162bba8642b34980ac63", "size_in_bytes": 90}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/pygments.css", "path_type": "hardlink", "sha256": "f82f422053b4413684181f281e3cfcc2e84bea525d66feb8116f9dbe8674fcc2", "sha256_in_prefix": "f82f422053b4413684181f281e3cfcc2e84bea525d66feb8116f9dbe8674fcc2", "size_in_bytes": 4819}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/searchtools.js", "path_type": "hardlink", "sha256": "d6b5ee21edd7b46c029c5111326719dcec5c5f52368704a93b2d6485cb22414c", "sha256_in_prefix": "d6b5ee21edd7b46c029c5111326719dcec5c5f52368704a93b2d6485cb22414c", "size_in_bytes": 16634}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/social-media.js", "path_type": "hardlink", "sha256": "e49a6d45629abf7c28ff76c9897de383a61372dc18fa344a048d2061c3024778", "sha256_in_prefix": "e49a6d45629abf7c28ff76c9897de383a61372dc18fa344a048d2061c3024778", "size_in_bytes": 2595}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/theme-setter.js", "path_type": "hardlink", "sha256": "d6feb784f694a13844bef039ad96fe3854ba23cea8f85f6313733af104d6332a", "sha256_in_prefix": "d6feb784f694a13844bef039ad96fe3854ba23cea8f85f6313733af104d6332a", "size_in_bytes": 1713}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/theme-switcher-general.css", "path_type": "hardlink", "sha256": "cfaf6ac2d70d331348a3f0652ab7b25a3689c003c040e4b1ec90be2702dc2c3a", "sha256_in_prefix": "cfaf6ac2d70d331348a3f0652ab7b25a3689c003c040e4b1ec90be2702dc2c3a", "size_in_bytes": 900}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/twemoji.css", "path_type": "hardlink", "sha256": "79f024cf4b763993639a7db8d2951b43e6a61619a50bc4fa92e3d3e20ccf8363", "sha256_in_prefix": "79f024cf4b763993639a7db8d2951b43e6a61619a50bc4fa92e3d3e20ccf8363", "size_in_bytes": 103}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/twemoji.js", "path_type": "hardlink", "sha256": "4c98db8e2416f696e9805f90294c56fae45c71f1cae843c25d69c28087ad691b", "sha256_in_prefix": "4c98db8e2416f696e9805f90294c56fae45c71f1cae843c25d69c28087ad691b", "size_in_bytes": 332}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/underscore-1.13.1.js", "path_type": "hardlink", "sha256": "cc10f799cd0f6b65f95c4012445497e5ba3cb9f51964a9468940b27bde98b487", "sha256_in_prefix": "cc10f799cd0f6b65f95c4012445497e5ba3cb9f51964a9468940b27bde98b487", "size_in_bytes": 68420}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/underscore.js", "path_type": "hardlink", "sha256": "218fb1c1fc72e9af6b866f430be2a67fa376392b4db2f4dbf32772671b6ae55c", "sha256_in_prefix": "218fb1c1fc72e9af6b866f430be2a67fa376392b4db2f4dbf32772671b6ae55c", "size_in_bytes": 19530}, {"_path": "Library/nsight-compute/2025.1.1/docs/_static/version.js", "path_type": "hardlink", "sha256": "719f36d8c2326d7d7e169ffed4bbdb7541b3bf2475b952235fbbe89507b4c401", "sha256_in_prefix": "719f36d8c2326d7d7e169ffed4bbdb7541b3bf2475b952235fbbe89507b4c401", "size_in_bytes": 7384}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IAction.html", "path_type": "hardlink", "sha256": "9988a9b747732122ef000d74598ab63c0e9e0299608d489928fdaa48d1f9ced2", "sha256_in_prefix": "9988a9b747732122ef000d74598ab63c0e9e0299608d489928fdaa48d1f9ced2", "size_in_bytes": 46924}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IBaseContext.html", "path_type": "hardlink", "sha256": "59577254fe019cb990d85e0595a46b83906c36c70734cb4da67ca02cadb76350", "sha256_in_prefix": "59577254fe019cb990d85e0595a46b83906c36c70734cb4da67ca02cadb76350", "size_in_bytes": 14098}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IContext.html", "path_type": "hardlink", "sha256": "877905193d7420b85036f652b363877cd35317108c0d7ba54b1599f27af8d19a", "sha256_in_prefix": "877905193d7420b85036f652b363877cd35317108c0d7ba54b1599f27af8d19a", "size_in_bytes": 19880}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IController.html", "path_type": "hardlink", "sha256": "0b86ef6f2d4088b491daab2c95e59bbf4c69673155e48a187a27774f6fca647a", "sha256_in_prefix": "0b86ef6f2d4088b491daab2c95e59bbf4c69673155e48a187a27774f6fca647a", "size_in_bytes": 14233}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IEvaluator.html", "path_type": "hardlink", "sha256": "9aace2043d3cc53e1dcac1a450188e432febdd9c895131c215eb88f912fff2bf", "sha256_in_prefix": "9aace2043d3cc53e1dcac1a450188e432febdd9c895131c215eb88f912fff2bf", "size_in_bytes": 17649}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IFrontend.html", "path_type": "hardlink", "sha256": "1a496970336064c774f7cf78f7d82bb56fa9e47114a68fd950a36833087ce81e", "sha256_in_prefix": "1a496970336064c774f7cf78f7d82bb56fa9e47114a68fd950a36833087ce81e", "size_in_bytes": 52041}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IMessageVault.html", "path_type": "hardlink", "sha256": "1afeb959ce2b6ba21cf81ec2f46c325fa2b6f5a592d5bfb5b52ccfff996c6e8d", "sha256_in_prefix": "1afeb959ce2b6ba21cf81ec2f46c325fa2b6f5a592d5bfb5b52ccfff996c6e8d", "size_in_bytes": 17769}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IMetric.html", "path_type": "hardlink", "sha256": "618db0c84e57d4baf7e7919fd2db14ae961b0c2896755e5ba87bad958250bfdd", "sha256_in_prefix": "618db0c84e57d4baf7e7919fd2db14ae961b0c2896755e5ba87bad958250bfdd", "size_in_bytes": 69824}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1INvtxDomainInfo.html", "path_type": "hardlink", "sha256": "77552f5e65b149903068041f0312d404aca9954d9c1c8d1eb6dc724561dcf2d2", "sha256_in_prefix": "77552f5e65b149903068041f0312d404aca9954d9c1c8d1eb6dc724561dcf2d2", "size_in_bytes": 19118}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1INvtxRange.html", "path_type": "hardlink", "sha256": "e2b08131691ccc8ae64ba45e8bc1eee60ac282275ded2bc96fc4abb87a69360a", "sha256_in_prefix": "e2b08131691ccc8ae64ba45e8bc1eee60ac282275ded2bc96fc4abb87a69360a", "size_in_bytes": 31817}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1INvtxState.html", "path_type": "hardlink", "sha256": "c6ed1ab5a7ad0db874fb39bfb6385acdab87ae72766c5d516c55bc91fc87d2bd", "sha256_in_prefix": "c6ed1ab5a7ad0db874fb39bfb6385acdab87ae72766c5d516c55bc91fc87d2bd", "size_in_bytes": 14338}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1IRange.html", "path_type": "hardlink", "sha256": "7c2928ecc39784b4f5d7a58f16721f45cbfe869637284888accc70028ec55163", "sha256_in_prefix": "7c2928ecc39784b4f5d7a58f16721f45cbfe869637284888accc70028ec55163", "size_in_bytes": 17109}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/classNV_1_1Rules_1_1ISourceInfo.html", "path_type": "hardlink", "sha256": "f8b909fa127d8c3f87d3ac33c0c8fc2508b59e3468c912dea9f941ffabae2ddd", "sha256_in_prefix": "f8b909fa127d8c3f87d3ac33c0c8fc2508b59e3468c912dea9f941ffabae2ddd", "size_in_bytes": 13291}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/data-structures.html", "path_type": "hardlink", "sha256": "61aacb90d9de9ea94808d147ed353cc7189777bb7aadadd9e32dae48b8462ae0", "sha256_in_prefix": "61aacb90d9de9ea94808d147ed353cc7189777bb7aadadd9e32dae48b8462ae0", "size_in_bytes": 9969}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/group__NVRULES__HW.html", "path_type": "hardlink", "sha256": "b9002b264cffdf3069a5cfcd9cbb834fefef30bdf54f97a60e5239ed2d87212e", "sha256_in_prefix": "b9002b264cffdf3069a5cfcd9cbb834fefef30bdf54f97a60e5239ed2d87212e", "size_in_bytes": 9367}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/group__NVRULES__LW.html", "path_type": "hardlink", "sha256": "71932f8e4e3b4f39c78321450ac48ffe07812d965081d3f7769c2fb4212fb76f", "sha256_in_prefix": "71932f8e4e3b4f39c78321450ac48ffe07812d965081d3f7769c2fb4212fb76f", "size_in_bytes": 9581}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/group__NVRULES__NM.html", "path_type": "hardlink", "sha256": "0a86477ed9c6697329f3bcbb99213aa5770af160ce27677e4f7830cc2428a9a7", "sha256_in_prefix": "0a86477ed9c6697329f3bcbb99213aa5770af160ce27677e4f7830cc2428a9a7", "size_in_bytes": 18948}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/modules.html", "path_type": "hardlink", "sha256": "ec725ae705036fb33b6ef5cf375f5ca3adfc9d27619b1c653bd7228c990e5134", "sha256_in_prefix": "ec725ae705036fb33b6ef5cf375f5ca3adfc9d27619b1c653bd7228c990e5134", "size_in_bytes": 8465}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/namespaceNV.html", "path_type": "hardlink", "sha256": "2cb3fb8d4cb31d37455ffe73b2fe9aa3df462c7e3dba1912e53e4b4b1e5525d1", "sha256_in_prefix": "2cb3fb8d4cb31d37455ffe73b2fe9aa3df462c7e3dba1912e53e4b4b1e5525d1", "size_in_bytes": 8282}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/namespaceNV_1_1Rules.html", "path_type": "hardlink", "sha256": "2f1f54f739baf2563e7f8ba7e791fe13ba34d275fd35f65aff04dac059ada427", "sha256_in_prefix": "2f1f54f739baf2563e7f8ba7e791fe13ba34d275fd35f65aff04dac059ada427", "size_in_bytes": 16820}, {"_path": "Library/nsight-compute/2025.1.1/docs/api/namespaces.html", "path_type": "hardlink", "sha256": "6c9c107ba437936dc4f231d41453ae6ba2cef115f3b915cf3353c250efddb883", "sha256_in_prefix": "6c9c107ba437936dc4f231d41453ae6ba2cef115f3b915cf3353c250efddb883", "size_in_bytes": 7699}, {"_path": "Library/nsight-compute/2025.1.1/docs/genindex.html", "path_type": "hardlink", "sha256": "a07e55727d31885d71cd252243b47dcdda74f67e6d5cb08b544fc6bc66666c88", "sha256_in_prefix": "a07e55727d31885d71cd252243b47dcdda74f67e6d5cb08b544fc6bc66666c88", "size_in_bytes": 38724}, {"_path": "Library/nsight-compute/2025.1.1/docs/index.html", "path_type": "hardlink", "sha256": "90bb600ddda4a4d82fae3ec6de4d73902bcb94992d8e83a8772ef78ccd8e7215", "sha256_in_prefix": "90bb600ddda4a4d82fae3ec6de4d73902bcb94992d8e83a8772ef78ccd8e7215", "size_in_bytes": 10098}, {"_path": "Library/nsight-compute/2025.1.1/docs/objects.inv", "path_type": "hardlink", "sha256": "d3720d94265f9d1749df1a3f013fd41c27f11645e57976a29046d7da2bf7bda3", "sha256_in_prefix": "d3720d94265f9d1749df1a3f013fd41c27f11645e57976a29046d7da2bf7bda3", "size_in_bytes": 13082}, {"_path": "Library/nsight-compute/2025.1.1/docs/project.json", "path_type": "hardlink", "sha256": "8a90aa88319a4aaa8f68d8ae68ba4dd4ca083c55fec28499c800b6f8ce8e398c", "sha256_in_prefix": "8a90aa88319a4aaa8f68d8ae68ba4dd4ca083c55fec28499c800b6f8ce8e398c", "size_in_bytes": 44}, {"_path": "Library/nsight-compute/2025.1.1/docs/search.html", "path_type": "hardlink", "sha256": "b12a2940091cc373219dd2f93d3c93805b38d238f5052a43d0ded2ace0619ae4", "sha256_in_prefix": "b12a2940091cc373219dd2f93d3c93805b38d238f5052a43d0ded2ace0619ae4", "size_in_bytes": 6523}, {"_path": "Library/nsight-compute/2025.1.1/docs/searchindex.js", "path_type": "hardlink", "sha256": "fefaed4226dba368245eafcbfacb0145000fdafd41ae5c517743625526ca4006", "sha256_in_prefix": "fefaed4226dba368245eafcbfacb0145000fdafd41ae5c517743625526ca4006", "size_in_bytes": 959310}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/CpuStacktrace.proto", "path_type": "hardlink", "sha256": "bbc5cb0fc9a6579683a02f1a05e03e5d0f09f82b8ecd00e815de1168c0051f10", "sha256_in_prefix": "bbc5cb0fc9a6579683a02f1a05e03e5d0f09f82b8ecd00e815de1168c0051f10", "size_in_bytes": 2701}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/Nvtx.proto", "path_type": "hardlink", "sha256": "2b27fecffd5b60f63b380cf26bd004c182521773c707593bd3d1360f2417622a", "sha256_in_prefix": "2b27fecffd5b60f63b380cf26bd004c182521773c707593bd3d1360f2417622a", "size_in_bytes": 5368}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/NvtxCategories.proto", "path_type": "hardlink", "sha256": "78d2812b6be8919bb31b9eba66a643fb13284f2d94798f1b6ed15dedaad200f6", "sha256_in_prefix": "78d2812b6be8919bb31b9eba66a643fb13284f2d94798f1b6ed15dedaad200f6", "size_in_bytes": 324}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerMetricOptions.proto", "path_type": "hardlink", "sha256": "106a000541aa4cb9d27060eff579997a1ff867750f31061ed0af6cc79ce3e56a", "sha256_in_prefix": "106a000541aa4cb9d27060eff579997a1ff867750f31061ed0af6cc79ce3e56a", "size_in_bytes": 1973}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerReport.proto", "path_type": "hardlink", "sha256": "c5469ca9313777dd5aabe239eab1884620c29d0f5d8285b774fa74f0703d6ff5", "sha256_in_prefix": "c5469ca9313777dd5aabe239eab1884620c29d0f5d8285b774fa74f0703d6ff5", "size_in_bytes": 20049}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerReportCommon.proto", "path_type": "hardlink", "sha256": "966cf25e226ef38b8c7338912d0c36c3dc70d6c68edf983dde505e2d44a69649", "sha256_in_prefix": "966cf25e226ef38b8c7338912d0c36c3dc70d6c68edf983dde505e2d44a69649", "size_in_bytes": 2270}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerResultsCommon.proto", "path_type": "hardlink", "sha256": "d30d71566a5b3a94c71588a85fbb4b40eeb321d72ea2fc6e4745c5c932053016", "sha256_in_prefix": "d30d71566a5b3a94c71588a85fbb4b40eeb321d72ea2fc6e4745c5c932053016", "size_in_bytes": 1021}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerSection.proto", "path_type": "hardlink", "sha256": "b294e8d33121f52a78818429c08bb1564148114d6a9d1aea64c238f508802591", "sha256_in_prefix": "b294e8d33121f52a78818429c08bb1564148114d6a9d1aea64c238f508802591", "size_in_bytes": 25588}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/ProfilerStringTable.proto", "path_type": "hardlink", "sha256": "11cc7b1b517121f06bd9260141d11842c487cac1abc0b68396a7cf6a9df29dbb", "sha256_in_prefix": "11cc7b1b517121f06bd9260141d11842c487cac1abc0b68396a7cf6a9df29dbb", "size_in_bytes": 209}, {"_path": "Library/nsight-compute/2025.1.1/extras/FileFormat/RuleResults.proto", "path_type": "hardlink", "sha256": "06b93f2628e49e678958bd95762d6200e0b30f8662c076b7dddea633e1fe0ed4", "sha256_in_prefix": "06b93f2628e49e678958bd95762d6200e0b30f8662c076b7dddea633e1fe0ed4", "size_in_bytes": 8456}, {"_path": "Library/nsight-compute/2025.1.1/extras/RuleTemplates/AdvancedRuleTemplate.py", "path_type": "hardlink", "sha256": "c55c0b66113806c43f38e77a1afab0263890f2e110ef417ce1792a98f8c01ffb", "sha256_in_prefix": "c55c0b66113806c43f38e77a1afab0263890f2e110ef417ce1792a98f8c01ffb", "size_in_bytes": 1135}, {"_path": "Library/nsight-compute/2025.1.1/extras/RuleTemplates/BasicKernelInfo.py", "path_type": "hardlink", "sha256": "80c408eb0dc2f7a664aef2874e6c145992bde63c6f79bed167f8b28277cd35c8", "sha256_in_prefix": "80c408eb0dc2f7a664aef2874e6c145992bde63c6f79bed167f8b28277cd35c8", "size_in_bytes": 1473}, {"_path": "Library/nsight-compute/2025.1.1/extras/RuleTemplates/BasicRuleTemplate.py", "path_type": "hardlink", "sha256": "accc34845d56a50c54bb1ab1c16e78cfbbdeb3872e9512c4a49d4e66cceaf3e8", "sha256_in_prefix": "accc34845d56a50c54bb1ab1c16e78cfbbdeb3872e9512c4a49d4e66cceaf3e8", "size_in_bytes": 2367}, {"_path": "Library/nsight-compute/2025.1.1/extras/RuleTemplates/RuleTemplate.section", "path_type": "hardlink", "sha256": "d4dbad7903fcf1949d7dad1b1a58f373a62efb3a893b63e715c526dda7dfd762", "sha256_in_prefix": "d4dbad7903fcf1949d7dad1b1a58f373a62efb3a893b63e715c526dda7dfd762", "size_in_bytes": 432}, {"_path": "Library/nsight-compute/2025.1.1/extras/RuleTemplates/RuleTemplate2_table.chart", "path_type": "hardlink", "sha256": "f54a591bfdb4156ca5ddaba354b68195992eb48120c5795e60da9ab033712605", "sha256_in_prefix": "f54a591bfdb4156ca5ddaba354b68195992eb48120c5795e60da9ab033712605", "size_in_bytes": 212}, {"_path": "Library/nsight-compute/2025.1.1/extras/RuleTemplates/RuleTemplate_bar.chart", "path_type": "hardlink", "sha256": "b93a6b97ac5c1c3404e90ec01f9c73e30ecdd4f4ee8cb82bf05175d9426d3be8", "sha256_in_prefix": "b93a6b97ac5c1c3404e90ec01f9c73e30ecdd4f4ee8cb82bf05175d9426d3be8", "size_in_bytes": 251}, {"_path": "Library/nsight-compute/2025.1.1/extras/RuleTemplates/RuleTemplate_table.chart", "path_type": "hardlink", "sha256": "f2aff5fdea87120877bc9adc6c20adbb040a6abeb5d812781373dc27cdbe3cfb", "sha256_in_prefix": "f2aff5fdea87120877bc9adc6c20adbb040a6abeb5d812781373dc27cdbe3cfb", "size_in_bytes": 227}, {"_path": "Library/nsight-compute/2025.1.1/extras/RuleTemplates/SpeedupWithFocusMetrics.py", "path_type": "hardlink", "sha256": "33faf81ef0e863788e97f74197e8e2ab15e6c5ca6965cb874d52c703737837f2", "sha256_in_prefix": "33faf81ef0e863788e97f74197e8e2ab15e6c5ca6965cb874d52c703737837f2", "size_in_bytes": 2563}, {"_path": "Library/nsight-compute/2025.1.1/extras/python/_ncu_report.pyd", "path_type": "hardlink", "sha256": "eec884760e9f4cf67314146e1cd7c4b503ddfbf6576a0e03e30c862afdf0bdc0", "sha256_in_prefix": "eec884760e9f4cf67314146e1cd7c4b503ddfbf6576a0e03e30c862afdf0bdc0", "size_in_bytes": 7300608}, {"_path": "Library/nsight-compute/2025.1.1/extras/python/ncu_report.py", "path_type": "hardlink", "sha256": "4a6d4f8b67a37588a4b442efb780feda93fbd8c211b393cc48ecbd3e4733d38f", "sha256_in_prefix": "4a6d4f8b67a37588a4b442efb780feda93fbd8c211b393cc48ecbd3e4733d38f", "size_in_bytes": 97004}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/README.TXT", "path_type": "hardlink", "sha256": "93e1ab29b94200d4b92a0c3eef3b705b935fa0e2563a2bd64632e3fda114523c", "sha256_in_prefix": "93e1ab29b94200d4b92a0c3eef3b705b935fa0e2563a2bd64632e3fda114523c", "size_in_bytes": 2433}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/instructionMix.cu", "path_type": "hardlink", "sha256": "956d9f32899ebc6ba336651671e38d740546e996a5ba8e09af28365bc973b79c", "sha256_in_prefix": "956d9f32899ebc6ba336651671e38d740546e996a5ba8e09af28365bc973b79c", "size_in_bytes": 7737}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/instructionMix.pdf", "path_type": "hardlink", "sha256": "e30e652a983018782d9830916e53699c1c7d2a2dabb76e34841c07c87ca10a35", "sha256_in_prefix": "e30e652a983018782d9830916e53699c1c7d2a2dabb76e34841c07c87ca10a35", "size_in_bytes": 3590960}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/sobelDouble.ncu-rep", "path_type": "hardlink", "sha256": "7d68fa9592424291373dff04d8aee1f321dec056981574a2d475d01fbe3723d1", "sha256_in_prefix": "7d68fa9592424291373dff04d8aee1f321dec056981574a2d475d01fbe3723d1", "size_in_bytes": 666104}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/instructionMix/sobelFloat.ncu-rep", "path_type": "hardlink", "sha256": "e9f06ae605bc54157c55c5d48a9f3981870eb44ab0dbff297d04ffb2bcaddea0", "sha256_in_prefix": "e9f06ae605bc54157c55c5d48a9f3981870eb44ab0dbff297d04ffb2bcaddea0", "size_in_bytes": 371507}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/interKernelCommunication/README.TXT", "path_type": "hardlink", "sha256": "734dddaf9b78dcb4d185068c95dbd1abdeb163c18fb3c9a28b2175dd279f6a70", "sha256_in_prefix": "734dddaf9b78dcb4d185068c95dbd1abdeb163c18fb3c9a28b2175dd279f6a70", "size_in_bytes": 4787}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/interKernelCommunication/interKernelCommunication.cu", "path_type": "hardlink", "sha256": "3538719f5adae0b6656e458391e2e08ccc410b4a0c5e1439394c3f01ecea7852", "sha256_in_prefix": "3538719f5adae0b6656e458391e2e08ccc410b4a0c5e1439394c3f01ecea7852", "size_in_bytes": 14254}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/README.TXT", "path_type": "hardlink", "sha256": "34db81af4c0e6ad54d0ef45030edeeef5542c0dfe5fc1071001b7f271174c90e", "sha256_in_prefix": "34db81af4c0e6ad54d0ef45030edeeef5542c0dfe5fc1071001b7f271174c90e", "size_in_bytes": 2702}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/sharedBankConflicts.cu", "path_type": "hardlink", "sha256": "1295906108d4bd24ab86a5134308558b7fe2645f0aab0a25c3986527706a62cc", "sha256_in_prefix": "1295906108d4bd24ab86a5134308558b7fe2645f0aab0a25c3986527706a62cc", "size_in_bytes": 10229}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/sharedBankConflicts.pdf", "path_type": "hardlink", "sha256": "e7fcf78ef118e46546d8bcf15ac6f7ae89c31b612e647383e865ae8cec7ee631", "sha256_in_prefix": "e7fcf78ef118e46546d8bcf15ac6f7ae89c31b612e647383e865ae8cec7ee631", "size_in_bytes": 3866999}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/transposeCoalesced.ncu-rep", "path_type": "hardlink", "sha256": "c5a0c08e60abde0e20376829a181bdd4a878bcb778f51d05bd8e8fb3a880be00", "sha256_in_prefix": "c5a0c08e60abde0e20376829a181bdd4a878bcb778f51d05bd8e8fb3a880be00", "size_in_bytes": 568389}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/sharedBankConflicts/transposeNoBankConflicts.ncu-rep", "path_type": "hardlink", "sha256": "544b42110730bd2adf2325eeea44a69be6455767443fe1d7a1572bbd7183bb29", "sha256_in_prefix": "544b42110730bd2adf2325eeea44a69be6455767443fe1d7a1572bbd7183bb29", "size_in_bytes": 466431}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/README.TXT", "path_type": "hardlink", "sha256": "4eae3fccfa9c11b384f7911381bd1967cf59ad4e6a7623092d7705a1061a448f", "sha256_in_prefix": "4eae3fccfa9c11b384f7911381bd1967cf59ad4e6a7623092d7705a1061a448f", "size_in_bytes": 2092}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/addConstDouble.ncu-rep", "path_type": "hardlink", "sha256": "3afffc2e1a1482477ad784c83a1243ccec5ce63c1bb278e7315b698f962fcfcd", "sha256_in_prefix": "3afffc2e1a1482477ad784c83a1243ccec5ce63c1bb278e7315b698f962fcfcd", "size_in_bytes": 277780}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/addConstDouble3.ncu-rep", "path_type": "hardlink", "sha256": "f9ddc7def4da494eebaf0bef7df8a20a0ae14026c62dc3ff4c116fbd8fea521f", "sha256_in_prefix": "f9ddc7def4da494eebaf0bef7df8a20a0ae14026c62dc3ff4c116fbd8fea521f", "size_in_bytes": 285545}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/uncoalescedGlobalAccesses.cu", "path_type": "hardlink", "sha256": "5f314dea5facc9f4b4ee6147033170b12209fe93dfea2aa6e850e57c58960330", "sha256_in_prefix": "5f314dea5facc9f4b4ee6147033170b12209fe93dfea2aa6e850e57c58960330", "size_in_bytes": 6616}, {"_path": "Library/nsight-compute/2025.1.1/extras/samples/uncoalescedGlobalAccesses/uncoalescedGlobalAccesses.pdf", "path_type": "hardlink", "sha256": "4a03a2360d15b54b0268baedd9accf0d899b8c3edb101404510e639bb2dad77c", "sha256_in_prefix": "4a03a2360d15b54b0268baedd9accf0d899b8c3edb101404510e639bb2dad77c", "size_in_bytes": 4300145}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/CudaGpuInfoDumper.exe", "path_type": "hardlink", "sha256": "f20cfc8c7e8e2a91cb3f39fa9a173f9c37c7730abbf4b56c10c7c4159d48f713", "sha256_in_prefix": "f20cfc8c7e8e2a91cb3f39fa9a173f9c37c7730abbf4b56c10c7c4159d48f713", "size_in_bytes": 745560}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ad10x-gfxt.config", "path_type": "hardlink", "sha256": "44070166380b2fa6f98d71f37aa85a8de21eb537497269e50191d59f114da126", "sha256_in_prefix": "44070166380b2fa6f98d71f37aa85a8de21eb537497269e50191d59f114da126", "size_in_bytes": 27220}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ad10x.config", "path_type": "hardlink", "sha256": "f2f35c098167d66e5e1da9f4677348a6245cdb8e037846f6f57e942147aaa773", "sha256_in_prefix": "f2f35c098167d66e5e1da9f4677348a6245cdb8e037846f6f57e942147aaa773", "size_in_bytes": 9420}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga100.config", "path_type": "hardlink", "sha256": "2f8542f60353fdf6729c17e1576f58f3220e061218223bc97b65c1da8abe539d", "sha256_in_prefix": "2f8542f60353fdf6729c17e1576f58f3220e061218223bc97b65c1da8abe539d", "size_in_bytes": 9811}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga10b.config", "path_type": "hardlink", "sha256": "292e3bfdb71326f280e680d4595881f5d63cabc18c6f117d58194b2217cac921", "sha256_in_prefix": "292e3bfdb71326f280e680d4595881f5d63cabc18c6f117d58194b2217cac921", "size_in_bytes": 6668}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga10x-gfxact.config", "path_type": "hardlink", "sha256": "08afa073be06e049958c9e6709b884a8a6c680a94646e08b5d5aed9fb2e28cca", "sha256_in_prefix": "08afa073be06e049958c9e6709b884a8a6c680a94646e08b5d5aed9fb2e28cca", "size_in_bytes": 25792}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga10x-gfxt.config", "path_type": "hardlink", "sha256": "ee21a8f0f059374da721b37c7552937a6c829fef580f67a712d77fef3b381b50", "sha256_in_prefix": "ee21a8f0f059374da721b37c7552937a6c829fef580f67a712d77fef3b381b50", "size_in_bytes": 26801}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/ga10x.config", "path_type": "hardlink", "sha256": "f75302d161458159feb5f630145b3139edcd2be9b83dd0b3d8395f75e4588098", "sha256_in_prefix": "f75302d161458159feb5f630145b3139edcd2be9b83dd0b3d8395f75e4588098", "size_in_bytes": 12017}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/gb10x.config", "path_type": "hardlink", "sha256": "aa70fc54185b45017c1a75f0f806db145523654db7140a1b94b53b969ecc41f0", "sha256_in_prefix": "aa70fc54185b45017c1a75f0f806db145523654db7140a1b94b53b969ecc41f0", "size_in_bytes": 8533}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/gb20x.config", "path_type": "hardlink", "sha256": "8df90523bc7788c84d13f56bfc310d07472a6be20ac57e7e8fd975a1caba86bf", "sha256_in_prefix": "8df90523bc7788c84d13f56bfc310d07472a6be20ac57e7e8fd975a1caba86bf", "size_in_bytes": 6872}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/gh100-ct.config", "path_type": "hardlink", "sha256": "70efcda23ef4b50335a828e8c7706f930209a062cee768bfdfb79c0c7a03c761", "sha256_in_prefix": "70efcda23ef4b50335a828e8c7706f930209a062cee768bfdfb79c0c7a03c761", "size_in_bytes": 30215}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/gh100.config", "path_type": "hardlink", "sha256": "7d5f92b9fe391265d010c016cbcd40e5228332ea13614059cb535af460740f54", "sha256_in_prefix": "7d5f92b9fe391265d010c016cbcd40e5228332ea13614059cb535af460740f54", "size_in_bytes": 10596}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/index.config", "path_type": "hardlink", "sha256": "351935b5f18ed42dfacf26f38210aaba7b648fd3d280e6b127fc4aab733468ed", "sha256_in_prefix": "351935b5f18ed42dfacf26f38210aaba7b648fd3d280e6b127fc4aab733468ed", "size_in_bytes": 123}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/tu10x-gfxt.config", "path_type": "hardlink", "sha256": "9872888f2507d129fec04e9213ef1680ece5c7d5ed6ff2d23b60d7db0ee24741", "sha256_in_prefix": "9872888f2507d129fec04e9213ef1680ece5c7d5ed6ff2d23b60d7db0ee24741", "size_in_bytes": 18502}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/tu10x.config", "path_type": "hardlink", "sha256": "a2101119a60f4c8b88b22b55ab2e28a338061fa436f259951f7027f455ffbcf3", "sha256_in_prefix": "a2101119a60f4c8b88b22b55ab2e28a338061fa436f259951f7027f455ffbcf3", "size_in_bytes": 11652}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/GpuMetrics/tu11x.config", "path_type": "hardlink", "sha256": "63d2e31540636d21a791aa3f58f870f6bfe0cc7e6090ac886166dcd3ff5c7dc1", "sha256_in_prefix": "63d2e31540636d21a791aa3f58f870f6bfe0cc7e6090ac886166dcd3ff5c7dc1", "size_in_bytes": 8246}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/NsysVsIntegration.xml", "path_type": "hardlink", "sha256": "eba6c49ed47b80c9acf0c63a6234f1c0152421fc826b9c3db12a0f0df1dc1ee9", "sha256_in_prefix": "eba6c49ed47b80c9acf0c63a6234f1c0152421fc826b9c3db12a0f0df1dc1ee9", "size_in_bytes": 1112}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/PythonFunctionsTrace/annotations.json", "path_type": "hardlink", "sha256": "3bbb714b310c8bb30c776daedbb73588be702e5f0605db47d99526b9e220dce9", "sha256_in_prefix": "3bbb714b310c8bb30c776daedbb73588be702e5f0605db47d99526b9e220dce9", "size_in_bytes": 1128}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjection64.dll", "path_type": "hardlink", "sha256": "4e74980a06dbbc1e15d1ae4fe7c5b954947e2dc247222dc5379e5e4bb1eac9c8", "sha256_in_prefix": "4e74980a06dbbc1e15d1ae4fe7c5b954947e2dc247222dc5379e5e4bb1eac9c8", "size_in_bytes": 11885640}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionHelper64.dll", "path_type": "hardlink", "sha256": "fa91d4f9e1a4e4d0419349ff1dae1cd775a032578ca929877049559c72714a74", "sha256_in_prefix": "fa91d4f9e1a4e4d0419349ff1dae1cd775a032578ca929877049559c72714a74", "size_in_bytes": 917560}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionPythonBacktrace64.dll", "path_type": "hardlink", "sha256": "848981196192f130d2d6075c8b1ba27f18d503583cc200d4bb421f4897654918", "sha256_in_prefix": "848981196192f130d2d6075c8b1ba27f18d503583cc200d4bb421f4897654918", "size_in_bytes": 273976}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionPythonGilTracing64.dll", "path_type": "hardlink", "sha256": "9e5390d24dc552f37fccd6d0d8e96d0b5de5fc40b2321946f5b12cfe1afee6b5", "sha256_in_prefix": "9e5390d24dc552f37fccd6d0d8e96d0b5de5fc40b2321946f5b12cfe1afee6b5", "size_in_bytes": 226872}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionPythonNvtxAnnotations64.dll", "path_type": "hardlink", "sha256": "80d4c9f30ff902ff4a7cadfcf00d89b564c06f747fd22cdecab27f76479f7e93", "sha256_in_prefix": "80d4c9f30ff902ff4a7cadfcf00d89b564c06f747fd22cdecab27f76479f7e93", "size_in_bytes": 386104}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/ToolsInjectionWindowsHook64.dll", "path_type": "hardlink", "sha256": "05d071945ee207df5338fe2e9d409061003c9862bac55ef6e3f6c76b7fc6a727", "sha256_in_prefix": "05d071945ee207df5338fe2e9d409061003c9862bac55ef6e3f6c76b7fc6a727", "size_in_bytes": 154696}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/atlas.dll", "path_type": "hardlink", "sha256": "d366dd2bb7d9310efafa8e2f7b661b6763d292c4a411386c100bf5c538957ccb", "sha256_in_prefix": "d366dd2bb7d9310efafa8e2f7b661b6763d292c4a411386c100bf5c538957ccb", "size_in_bytes": 2931792}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/bifrost.dll", "path_type": "hardlink", "sha256": "ed22130dcf79ab792cc5594ddaf7ebdc67e6a423c1ed8363fa146696b3d02fb3", "sha256_in_prefix": "ed22130dcf79ab792cc5594ddaf7ebdc67e6a423c1ed8363fa146696b3d02fb3", "size_in_bytes": 3080760}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/bifrost_loader.2.dll", "path_type": "hardlink", "sha256": "b02f34f0c9318bbd4022fe730ec14b0c68ef51ef3224ebdf1a1c343738c2596a", "sha256_in_prefix": "b02f34f0c9318bbd4022fe730ec14b0c68ef51ef3224ebdf1a1c343738c2596a", "size_in_bytes": 2326608}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/bifrost_plugin.2.dll", "path_type": "hardlink", "sha256": "b565cca6fa13f56bb765405c8f5e8d51cac457ea62c11d823e58cf89132c0627", "sha256_in_prefix": "b565cca6fa13f56bb765405c8f5e8d51cac457ea62c11d823e58cf89132c0627", "size_in_bytes": 2001976}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/config.ini", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_100.dll", "path_type": "hardlink", "sha256": "35c30c757d1fe2ede4932a2e668f813386aad93e672b1f494b3d32ae2182e792", "sha256_in_prefix": "35c30c757d1fe2ede4932a2e668f813386aad93e672b1f494b3d32ae2182e792", "size_in_bytes": 3666488}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_101.dll", "path_type": "hardlink", "sha256": "ce080b928e545f8d2771d7b09b3e088855adba87cf9426c89e2ba225d0ba3ca3", "sha256_in_prefix": "ce080b928e545f8d2771d7b09b3e088855adba87cf9426c89e2ba225d0ba3ca3", "size_in_bytes": 3673656}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_102.dll", "path_type": "hardlink", "sha256": "c4f92bd04bfcc6c22adf7801a04a31c2b882b143b9894247160a533abea8c25e", "sha256_in_prefix": "c4f92bd04bfcc6c22adf7801a04a31c2b882b143b9894247160a533abea8c25e", "size_in_bytes": 3539000}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_110.dll", "path_type": "hardlink", "sha256": "fe7ff3f9290b1141bb9a77279f2b2d48b0b60d6193a7b6248d705aa420ea2211", "sha256_in_prefix": "fe7ff3f9290b1141bb9a77279f2b2d48b0b60d6193a7b6248d705aa420ea2211", "size_in_bytes": 3749456}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_111.dll", "path_type": "hardlink", "sha256": "cfaa2519bb92058b98f2b800f9abc03f4c6013ed7aaf2e162b410e12d110110b", "sha256_in_prefix": "cfaa2519bb92058b98f2b800f9abc03f4c6013ed7aaf2e162b410e12d110110b", "size_in_bytes": 3756624}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_112.dll", "path_type": "hardlink", "sha256": "f5550aa743879d47874717ea5783000dfc267dfaef6845c0c9b6561c9b9e5f87", "sha256_in_prefix": "f5550aa743879d47874717ea5783000dfc267dfaef6845c0c9b6561c9b9e5f87", "size_in_bytes": 3792440}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_113.dll", "path_type": "hardlink", "sha256": "149726bc5e7154808fa04d2a42a403a7ca2ac77806338af5b9d80ae1cdfa8d07", "sha256_in_prefix": "149726bc5e7154808fa04d2a42a403a7ca2ac77806338af5b9d80ae1cdfa8d07", "size_in_bytes": 3859000}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_114.dll", "path_type": "hardlink", "sha256": "48e9dcf05333c688f828814dc565ac548e19c33a352705df40a495be59171d50", "sha256_in_prefix": "48e9dcf05333c688f828814dc565ac548e19c33a352705df40a495be59171d50", "size_in_bytes": 4116024}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_115.dll", "path_type": "hardlink", "sha256": "6bc5aaab81259aa712c1c1e25ecfd1d24bf26f8ca5f98605ea70da2cdd94c825", "sha256_in_prefix": "6bc5aaab81259aa712c1c1e25ecfd1d24bf26f8ca5f98605ea70da2cdd94c825", "size_in_bytes": 4120144}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_116.dll", "path_type": "hardlink", "sha256": "35cd2c3976562f9ceb42951416334b489aeea0f4d59271a6e18df551bd478107", "sha256_in_prefix": "35cd2c3976562f9ceb42951416334b489aeea0f4d59271a6e18df551bd478107", "size_in_bytes": 4119608}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_117.dll", "path_type": "hardlink", "sha256": "1ec66000716e8896140f6824c85ef787621bb5aa0cd9bca92a3daedb87a46872", "sha256_in_prefix": "1ec66000716e8896140f6824c85ef787621bb5aa0cd9bca92a3daedb87a46872", "size_in_bytes": 4179024}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_118.dll", "path_type": "hardlink", "sha256": "56d7024991c89c6a7b7118f356b00767dd3a9c7340efbb82538c1cdda79dd6c7", "sha256_in_prefix": "56d7024991c89c6a7b7118f356b00767dd3a9c7340efbb82538c1cdda79dd6c7", "size_in_bytes": 4301880}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/cupti64_128.dll", "path_type": "hardlink", "sha256": "5dcaf3c63ba14dc22362a889313c65a558a13d55037e80abbada7049bbd13c60", "sha256_in_prefix": "5dcaf3c63ba14dc22362a889313c65a558a13d55037e80abbada7049bbd13c60", "size_in_bytes": 3769400}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/etw_providers_template.json", "path_type": "hardlink", "sha256": "27c19d05d829052b48ad29f985d7fc8bdb8b85330b6eeca9121c16e3357f776e", "sha256_in_prefix": "27c19d05d829052b48ad29f985d7fc8bdb8b85330b6eeca9121c16e3357f776e", "size_in_bytes": 2645}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/exporter/export_schema_version_notes.txt", "path_type": "hardlink", "sha256": "23c62aa103cfcea7de27089def5f9ad729eccb54133c4a2c15a9ea87f13025e8", "sha256_in_prefix": "23c62aa103cfcea7de27089def5f9ad729eccb54133c4a2c15a9ea87f13025e8", "size_in_bytes": 9692}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nsight-sys-service.exe", "path_type": "hardlink", "sha256": "d06f1296334dad2185834a122602c0fcba3c4e12a09ceacbcc31656019c22ca8", "sha256_in_prefix": "d06f1296334dad2185834a122602c0fcba3c4e12a09ceacbcc31656019c22ca8", "size_in_bytes": 136248}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nsys.exe", "path_type": "hardlink", "sha256": "0a0dc298dbdde5c383ee16e5a51b0320d644215f7810fbca0166d8fb5ee6c30c", "sha256_in_prefix": "0a0dc298dbdde5c383ee16e5a51b0320d644215f7810fbca0166d8fb5ee6c30c", "size_in_bytes": 46246464}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvlog.config.template", "path_type": "hardlink", "sha256": "c69312187c4d0fb19e930d86baa030e07d1e7f0b46d730e6aa609fb542b158a7", "sha256_in_prefix": "c69312187c4d0fb19e930d86baa030e07d1e7f0b46d730e6aa609fb542b158a7", "size_in_bytes": 648}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvperf_grfx_host.dll", "path_type": "hardlink", "sha256": "b139ff9c5168b485d60e32413dc30251c17b2788b8b5d83fe585baec17be590d", "sha256_in_prefix": "b139ff9c5168b485d60e32413dc30251c17b2788b8b5d83fe585baec17be590d", "size_in_bytes": 21626960}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvsym.dll", "path_type": "hardlink", "sha256": "cafb3d97743b7ffe43fcd98bd770457b039d5b2102efd8f072462e8ebce1d831", "sha256_in_prefix": "cafb3d97743b7ffe43fcd98bd770457b039d5b2102efd8f072462e8ebce1d831", "size_in_bytes": 4415032}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExt.h", "path_type": "hardlink", "sha256": "9efbf763f4573b16224fe6a1391a21c775a9546d0b2021c9c56834193207d7b8", "sha256_in_prefix": "9efbf763f4573b16224fe6a1391a21c775a9546d0b2021c9c56834193207d7b8", "size_in_bytes": 52130}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtCuda.h", "path_type": "hardlink", "sha256": "ad8f066ef118c7efaa7398f40cd3bdf00b4d84b6b056d41cdd832d0cc6dfb83e", "sha256_in_prefix": "ad8f066ef118c7efaa7398f40cd3bdf00b4d84b6b056d41cdd832d0cc6dfb83e", "size_in_bytes": 4758}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtCudaRt.h", "path_type": "hardlink", "sha256": "f5ff06c50ed8da0ac1adc10e87737159890ed82dfa30cba6f83922912ccef19d", "sha256_in_prefix": "f5ff06c50ed8da0ac1adc10e87737159890ed82dfa30cba6f83922912ccef19d", "size_in_bytes": 3923}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtOpenCL.h", "path_type": "hardlink", "sha256": "6f8cca615c0359d25bf12bdce2d95e8d3a949085b3989dd76d7b1ff5adbe29c4", "sha256_in_prefix": "6f8cca615c0359d25bf12bdce2d95e8d3a949085b3989dd76d7b1ff5adbe29c4", "size_in_bytes": 7167}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtSync.h", "path_type": "hardlink", "sha256": "c15237974bf60e746e38618e16ff84d7f05c8eaba36e4b6c488abb2b0892bee8", "sha256_in_prefix": "c15237974bf60e746e38618e16ff84d7f05c8eaba36e4b6c488abb2b0892bee8", "size_in_bytes": 13551}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtx3.hpp", "path_type": "hardlink", "sha256": "9ab1daeef16ef8eb49c6b1d8be0c37dde1b9dd9083d6f6d168035aba70e719c6", "sha256_in_prefix": "9ab1daeef16ef8eb49c6b1d8be0c37dde1b9dd9083d6f6d168035aba70e719c6", "size_in_bytes": 103412}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImpl.h", "path_type": "hardlink", "sha256": "bbf8525ae2a64ca4f8dc3c3dd2debbe682ddff7b929ce0129795c56ecb9c4b7e", "sha256_in_prefix": "bbf8525ae2a64ca4f8dc3c3dd2debbe682ddff7b929ce0129795c56ecb9c4b7e", "size_in_bytes": 22104}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCore.h", "path_type": "hardlink", "sha256": "d525f715d9cfa83455af56faf7665fb800122665883818130dd2c0380b89f282", "sha256_in_prefix": "d525f715d9cfa83455af56faf7665fb800122665883818130dd2c0380b89f282", "size_in_bytes": 10318}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCudaRt_v3.h", "path_type": "hardlink", "sha256": "126b40c3eb43042702d11e753b20318528f707772c391f184ba8569e467f8277", "sha256_in_prefix": "126b40c3eb43042702d11e753b20318528f707772c391f184ba8569e467f8277", "size_in_bytes": 3185}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCuda_v3.h", "path_type": "hardlink", "sha256": "dfba45b8d8e231f9c6eee0827a59980e049ea21f1919a550a6ca1e1f0ec2bbee", "sha256_in_prefix": "dfba45b8d8e231f9c6eee0827a59980e049ea21f1919a550a6ca1e1f0ec2bbee", "size_in_bytes": 3985}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplOpenCL_v3.h", "path_type": "hardlink", "sha256": "3cc35f21fc467ad63aed9abd8d34b888490f0dd44917f0032fe7f9dcfacee34d", "sha256_in_prefix": "3cc35f21fc467ad63aed9abd8d34b888490f0dd44917f0032fe7f9dcfacee34d", "size_in_bytes": 6769}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplSync_v3.h", "path_type": "hardlink", "sha256": "a2a8df970034b58ac263d0d7bc0244d31e113bb6178f257fa0584c6dbd278a6f", "sha256_in_prefix": "a2a8df970034b58ac263d0d7bc0244d31e113bb6178f257fa0584c6dbd278a6f", "size_in_bytes": 3438}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInit.h", "path_type": "hardlink", "sha256": "0352554912f21413c6a748bef4e2dee37336498fe6827d6da40e183a78346f15", "sha256_in_prefix": "0352554912f21413c6a748bef4e2dee37336498fe6827d6da40e183a78346f15", "size_in_bytes": 13384}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInitDecls.h", "path_type": "hardlink", "sha256": "d9fd0ce350f2a7d4a58cf6e4c4773456b259b9c78f580ddd1643df798f7730b5", "sha256_in_prefix": "d9fd0ce350f2a7d4a58cf6e4c4773456b259b9c78f580ddd1643df798f7730b5", "size_in_bytes": 9697}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInitDefs.h", "path_type": "hardlink", "sha256": "e8d0fad60f7c858e2ef0685a3ea6fdc8187a9f5f8162a13e934a20103fccd759", "sha256_in_prefix": "e8d0fad60f7c858e2ef0685a3ea6fdc8187a9f5f8162a13e934a20103fccd759", "size_in_bytes": 36260}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxLinkOnce.h", "path_type": "hardlink", "sha256": "e1d11108ca20e34e3d4638515a1a30351c532efa637cf4640b886c619c12a305", "sha256_in_prefix": "e1d11108ca20e34e3d4638515a1a30351c532efa637cf4640b886c619c12a305", "size_in_bytes": 4201}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxTypes.h", "path_type": "hardlink", "sha256": "21e7d502e952b392ad059a4763a6d3f45ebe1f5e4f5a514c544abe9a81df2089", "sha256_in_prefix": "21e7d502e952b392ad059a4763a6d3f45ebe1f5e4f5a514c544abe9a81df2089", "size_in_bytes": 16069}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/odin.dll", "path_type": "hardlink", "sha256": "6962facb695a9c2cd913c315676a87e80580159ed477434fdc55ec9fc2d16c87", "sha256_in_prefix": "6962facb695a9c2cd913c315676a87e80580159ed477434fdc55ec9fc2d16c87", "size_in_bytes": 1347152}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/openxr-layers/XR_APILAYER_NV_nsight-sys_windows.json", "path_type": "hardlink", "sha256": "24ab58119865f13aa0a36e578cb2dce1be0acf1faf09a525718baf77e5265413", "sha256_in_prefix": "24ab58119865f13aa0a36e578cb2dce1be0acf1faf09a525718baf77e5265413", "size_in_bytes": 888}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/bin/python.exe", "path_type": "hardlink", "sha256": "887eaea81513c94dd45a55850f5c390d2af0ad3e5073412f355ea33b633ac5b0", "sha256_in_prefix": "887eaea81513c94dd45a55850f5c390d2af0ad3e5073412f355ea33b633ac5b0", "size_in_bytes": 8753664}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/lib/gpustats.py", "path_type": "hardlink", "sha256": "a3b8b83c253ec1799aa5c7ea684bde0f113d9a52c443c85dbc44d6186b398f25", "sha256_in_prefix": "a3b8b83c253ec1799aa5c7ea684bde0f113d9a52c443c85dbc44d6186b398f25", "size_in_bytes": 8987}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/lib/kernel_helper.py", "path_type": "hardlink", "sha256": "fd1749b894eb3619a51bcd9ca42055d436716a23f6d22896ce56cb2cdd8466ca", "sha256_in_prefix": "fd1749b894eb3619a51bcd9ca42055d436716a23f6d22896ce56cb2cdd8466ca", "size_in_bytes": 6117}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/lib/nsysstats.py", "path_type": "hardlink", "sha256": "5d208e31d27ad3875bbad9b837f3643762521b0abdd577faa7d8cf9bc50638e5", "sha256_in_prefix": "5d208e31d27ad3875bbad9b837f3643762521b0abdd577faa7d8cf9bc50638e5", "size_in_bytes": 21892}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/Dockerfile", "path_type": "hardlink", "sha256": "f3e7504eca99bf8ffb3847ce7aeb8a990a50534c910134cc7740886e659d05a2", "sha256_in_prefix": "f3e7504eca99bf8ffb3847ce7aeb8a990a50534c910134cc7740886e659d05a2", "size_in_bytes": 804}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/__init__.py", "path_type": "hardlink", "sha256": "b686ebaeb1e26c47847bb4e54524c8b61e5707e4874c8f74f295a0be43f7a406", "sha256_in_prefix": "b686ebaeb1e26c47847bb4e54524c8b61e5707e4874c8f74f295a0be43f7a406", "size_in_bytes": 2186}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/__main__.py", "path_type": "hardlink", "sha256": "fea485c42588e118e81d1707408dc1db6cb8bac5d7a919a3f30df626a1ec4829", "sha256_in_prefix": "fea485c42588e118e81d1707408dc1db6cb8bac5d7a919a3f30df626a1ec4829", "size_in_bytes": 4294}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/clean.py", "path_type": "hardlink", "sha256": "aeb91546c84ab0059c1fe9619a72a1c7f567d364a62941d19180d83a8cdc0cbc", "sha256_in_prefix": "aeb91546c84ab0059c1fe9619a72a1c7f567d364a62941d19180d83a8cdc0cbc", "size_in_bytes": 876}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/data_service.py", "path_type": "hardlink", "sha256": "b680b327895cd03bfc9a1d96200895978a490edc56fe99634d27d6cc09d603fc", "sha256_in_prefix": "b680b327895cd03bfc9a1d96200895978a490edc56fe99634d27d6cc09d603fc", "size_in_bytes": 14551}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/format.py", "path_type": "hardlink", "sha256": "7e2bdadcaab8f79b1b9d21091afe37f70ab6c4b1afb7019c67f7762e1a066472", "sha256_in_prefix": "7e2bdadcaab8f79b1b9d21091afe37f70ab6c4b1afb7019c67f7762e1a066472", "size_in_bytes": 940}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/install.py", "path_type": "hardlink", "sha256": "1c51206fd4af7f0ec85ba75082a050c8292af35daec08ddb96d8084673bf7d60", "sha256_in_prefix": "1c51206fd4af7f0ec85ba75082a050c8292af35daec08ddb96d8084673bf7d60", "size_in_bytes": 8557}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/__init__.py", "path_type": "hardlink", "sha256": "c24f1bc03bdbbd74a685476a91ae71496b12f3895e2781a76a2be99a9f94ca71", "sha256_in_prefix": "c24f1bc03bdbbd74a685476a91ae71496b12f3895e2781a76a2be99a9f94ca71", "size_in_bytes": 721}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/args.py", "path_type": "hardlink", "sha256": "41baf791981eaedbb0e9b519427e13176a4f55acdcb98318d258d991dc53648f", "sha256_in_prefix": "41baf791981eaedbb0e9b519427e13176a4f55acdcb98318d258d991dc53648f", "size_in_bytes": 17609}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/collective_loader.py", "path_type": "hardlink", "sha256": "7e36024e06ff4f7673558c1a7b07adc17cd5e0f57c9e9623db5e60e2e46c9a1f", "sha256_in_prefix": "7e36024e06ff4f7673558c1a7b07adc17cd5e0f57c9e9623db5e60e2e46c9a1f", "size_in_bytes": 3219}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/cuda.py", "path_type": "hardlink", "sha256": "e40896850b3b7b2ef9d19091c4497d5b486e0845b3513e637c16709d1ac8ac69", "sha256_in_prefix": "e40896850b3b7b2ef9d19091c4497d5b486e0845b3513e637c16709d1ac8ac69", "size_in_bytes": 6664}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/data_reader.py", "path_type": "hardlink", "sha256": "f253cf91839496af1ab8f5b0dd8a3cfcce5254431ea3950a089dce24ad24c8b5", "sha256_in_prefix": "f253cf91839496af1ab8f5b0dd8a3cfcce5254431ea3950a089dce24ad24c8b5", "size_in_bytes": 14901}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/data_utils.py", "path_type": "hardlink", "sha256": "9646c199b35234b696571cf653fbe64b4341d9f64f40bd2f70cd91758c143c9c", "sha256_in_prefix": "9646c199b35234b696571cf653fbe64b4341d9f64f40bd2f70cd91758c143c9c", "size_in_bytes": 13131}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/exceptions.py", "path_type": "hardlink", "sha256": "43f0e7a5ce9f44eb12efe02b82c09dc763fc2c21651c6ba58a8af2357e8a3075", "sha256_in_prefix": "43f0e7a5ce9f44eb12efe02b82c09dc763fc2c21651c6ba58a8af2357e8a3075", "size_in_bytes": 1086}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/export.py", "path_type": "hardlink", "sha256": "7537f758d6fb33ee7f134f6b6ea36a722779e61e53509c8d5b5ba9badfe0070a", "sha256_in_prefix": "7537f758d6fb33ee7f134f6b6ea36a722779e61e53509c8d5b5ba9badfe0070a", "size_in_bytes": 2909}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/gpu_metrics.py", "path_type": "hardlink", "sha256": "284194e878cb76a2a012c39f1eeb110e0398138ca8768e95842c415816d460cc", "sha256_in_prefix": "284194e878cb76a2a012c39f1eeb110e0398138ca8768e95842c415816d460cc", "size_in_bytes": 1710}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/heatmap.py", "path_type": "hardlink", "sha256": "674c2d1527a3c5f6ce4fee0a5d22f8e97a12f2587921e2a8aa498f5e17da4cad", "sha256_in_prefix": "674c2d1527a3c5f6ce4fee0a5d22f8e97a12f2587921e2a8aa498f5e17da4cad", "size_in_bytes": 4088}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/helpers.py", "path_type": "hardlink", "sha256": "75451388c64ac3f571b6eaaa4655bf29c4e2d7533dfd9465973bc3a11dd768f4", "sha256_in_prefix": "75451388c64ac3f571b6eaaa4655bf29c4e2d7533dfd9465973bc3a11dd768f4", "size_in_bytes": 2523}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/network.py", "path_type": "hardlink", "sha256": "e2fb9f078536201e21cbcd8c7bf24bd8e65cefa1a92b2806886f7547edd0598f", "sha256_in_prefix": "e2fb9f078536201e21cbcd8c7bf24bd8e65cefa1a92b2806886f7547edd0598f", "size_in_bytes": 4583}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_display.py", "path_type": "hardlink", "sha256": "e7d48b738f3c4291895db899f11ef4eaead4746f0a416017220e0e81595b22eb", "sha256_in_prefix": "e7d48b738f3c4291895db899f11ef4eaead4746f0a416017220e0e81595b22eb", "size_in_bytes": 12670}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_path.py", "path_type": "hardlink", "sha256": "3926fc2a2f0ca313ad2e9efa39e988264736c262d3091e6a6af9f99cca1e5bb3", "sha256_in_prefix": "3926fc2a2f0ca313ad2e9efa39e988264736c262d3091e6a6af9f99cca1e5bb3", "size_in_bytes": 2160}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_pres.py", "path_type": "hardlink", "sha256": "7755216991b1780347b47421547c565114cde056bc6e368514c67806d579b932", "sha256_in_prefix": "7755216991b1780347b47421547c565114cde056bc6e368514c67806d579b932", "size_in_bytes": 13368}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nvtx.py", "path_type": "hardlink", "sha256": "abde283e902769c4f5cc96f11c3f29bac0810fa2f6316329982a76c0e5b2b2e5", "sha256_in_prefix": "abde283e902769c4f5cc96f11c3f29bac0810fa2f6316329982a76c0e5b2b2e5", "size_in_bytes": 11170}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/overlap.py", "path_type": "hardlink", "sha256": "7980d37d3fa318a182d23f126e4997b994f4133b8815dd07dd85958228311ff2", "sha256_in_prefix": "7980d37d3fa318a182d23f126e4997b994f4133b8815dd07dd85958228311ff2", "size_in_bytes": 10404}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/pace.py", "path_type": "hardlink", "sha256": "7d7a3922c9042027d9c024515cd78b2e164a3515d36475f30266dd3b5bc19fab", "sha256_in_prefix": "7d7a3922c9042027d9c024515cd78b2e164a3515d36475f30266dd3b5bc19fab", "size_in_bytes": 3671}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/recipe.py", "path_type": "hardlink", "sha256": "2628c3a774463b29a01a2321c0d93953687b4336bd8d0ace5576ab3d73ad864a", "sha256_in_prefix": "2628c3a774463b29a01a2321c0d93953687b4336bd8d0ace5576ab3d73ad864a", "size_in_bytes": 17495}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/recipe_loader.py", "path_type": "hardlink", "sha256": "12a6ad74bc4b9542d6deeace1cc06a2141bd849d9665ddc6725f8f208b9a11b4", "sha256_in_prefix": "12a6ad74bc4b9542d6deeace1cc06a2141bd849d9665ddc6725f8f208b9a11b4", "size_in_bytes": 5856}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/summary.py", "path_type": "hardlink", "sha256": "b869039bc8523a351c385f00d357d6109a047fa8e35e45e4a3c662b1ce63d666", "sha256_in_prefix": "b869039bc8523a351c385f00d357d6109a047fa8e35e45e4a3c662b1ce63d666", "size_in_bytes": 4065}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/table_config.py", "path_type": "hardlink", "sha256": "da11e0ef0d405847909135ecd0c6a8f1f913563bcbd8f95525c2967151630024", "sha256_in_prefix": "da11e0ef0d405847909135ecd0c6a8f1f913563bcbd8f95525c2967151630024", "size_in_bytes": 18725}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/log.py", "path_type": "hardlink", "sha256": "0b87a52258a2f106ac5c197c2f7759816d95dafeb04a469086d6d1e3dce75393", "sha256_in_prefix": "0b87a52258a2f106ac5c197c2f7759816d95dafeb04a469086d6d1e3dce75393", "size_in_bytes": 1741}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/nsys_constants.py", "path_type": "hardlink", "sha256": "16f76698db439d5611be111f3784da52960b09e9f1ae840fa4429eb2e557703f", "sha256_in_prefix": "16f76698db439d5611be111f3784da52960b09e9f1ae840fa4429eb2e557703f", "size_in_bytes": 2123}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/pyproject.toml", "path_type": "hardlink", "sha256": "ea8ee6c67bb83923344c7b4788dabb53bd1362c6e564badb8beb1be7880105ae", "sha256_in_prefix": "ea8ee6c67bb83923344c7b4788dabb53bd1362c6e564badb8beb1be7880105ae", "size_in_bytes": 142}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/cuda_api_sum.py", "path_type": "hardlink", "sha256": "77bd57f51d0b32e2c22ddfa4ad6d85abc828e4a1d02e9916be6016660e86a125", "sha256_in_prefix": "77bd57f51d0b32e2c22ddfa4ad6d85abc828e4a1d02e9916be6016660e86a125", "size_in_bytes": 4534}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/metadata.json", "path_type": "hardlink", "sha256": "898babb2824a68c5facde8245c7cf173196bca72d8c3a60deac3d797720ee119", "sha256_in_prefix": "898babb2824a68c5facde8245c7cf173196bca72d8c3a60deac3d797720ee119", "size_in_bytes": 207}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/stats.ipynb", "path_type": "hardlink", "sha256": "e2dd6c2446ea6a3f50172339f91079a7d6f253d15ac508b6e32ac4bf75968b9e", "sha256_in_prefix": "e2dd6c2446ea6a3f50172339f91079a7d6f253d15ac508b6e32ac4bf75968b9e", "size_in_bytes": 4804}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/analysis.ipynb", "path_type": "hardlink", "sha256": "259143051d6726a2d4d7bd61cc1800ba00f7ff253b84493ebb6443c07355f869", "sha256_in_prefix": "259143051d6726a2d4d7bd61cc1800ba00f7ff253b84493ebb6443c07355f869", "size_in_bytes": 2671}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/cuda_api_sync.py", "path_type": "hardlink", "sha256": "acaeac0a23164a891ed2577bfb9fd2c5f6aff7eabb7aec217ddd31dc4bb76038", "sha256_in_prefix": "acaeac0a23164a891ed2577bfb9fd2c5f6aff7eabb7aec217ddd31dc4bb76038", "size_in_bytes": 3590}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/metadata.json", "path_type": "hardlink", "sha256": "1c13b833f95b8268d1ac57b919a9f1892b7ab3ef7a00ea17ec1f30758de27d70", "sha256_in_prefix": "1c13b833f95b8268d1ac57b919a9f1892b7ab3ef7a00ea17ec1f30758de27d70", "size_in_bytes": 346}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_hist/cuda_gpu_kern_hist.py", "path_type": "hardlink", "sha256": "06353b2bdea3165f09ebc4b77c9645e950e6190a81d7bf6a1ba8cfcc02ad0725", "sha256_in_prefix": "06353b2bdea3165f09ebc4b77c9645e950e6190a81d7bf6a1ba8cfcc02ad0725", "size_in_bytes": 3995}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_hist/histogram.ipynb", "path_type": "hardlink", "sha256": "ea83ed2f3895db35dc4ad73b2ca6faebcaab09f6a8e02d303b5a40bdfa731c68", "sha256_in_prefix": "ea83ed2f3895db35dc4ad73b2ca6faebcaab09f6a8e02d303b5a40bdfa731c68", "size_in_bytes": 2681}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_hist/metadata.json", "path_type": "hardlink", "sha256": "f9d113e15f3abab86b6230851add2ebb9cb20550f31425e7dd9e487a84736bd4", "sha256_in_prefix": "f9d113e15f3abab86b6230851add2ebb9cb20550f31425e7dd9e487a84736bd4", "size_in_bytes": 287}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/cuda_gpu_kern_pace.py", "path_type": "hardlink", "sha256": "41dd4a81d07424445dcf58773f03c744f76a93b2b07b85882b5f40cafef4d4a7", "sha256_in_prefix": "41dd4a81d07424445dcf58773f03c744f76a93b2b07b85882b5f40cafef4d4a7", "size_in_bytes": 4716}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/metadata.json", "path_type": "hardlink", "sha256": "1b1568daeaf10154ab641f0b8ed69b3cf0c447bbca4d4e271e1135e13fb47b77", "sha256_in_prefix": "1b1568daeaf10154ab641f0b8ed69b3cf0c447bbca4d4e271e1135e13fb47b77", "size_in_bytes": 258}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/pace.ipynb", "path_type": "hardlink", "sha256": "ba043569985a78c34bd2dfa6b1216932e3a99e77d8d6b242c04978b52cefe37a", "sha256_in_prefix": "ba043569985a78c34bd2dfa6b1216932e3a99e77d8d6b242c04978b52cefe37a", "size_in_bytes": 11467}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/cuda_gpu_kern_sum.py", "path_type": "hardlink", "sha256": "d9752471636ddc9ba1b1e60d0401e7acf5548c044be12703846ec0c4dda9939d", "sha256_in_prefix": "d9752471636ddc9ba1b1e60d0401e7acf5548c044be12703846ec0c4dda9939d", "size_in_bytes": 5252}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/metadata.json", "path_type": "hardlink", "sha256": "7b9bf96da15cb7bb9ee3342d9da49d62b4098557d8d88aef0f54f126e5867532", "sha256_in_prefix": "7b9bf96da15cb7bb9ee3342d9da49d62b4098557d8d88aef0f54f126e5867532", "size_in_bytes": 213}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/stats.ipynb", "path_type": "hardlink", "sha256": "a2ef7adb341d8af5ff15f315ca1ae90894a20a696d598edc6d27a2ac9c8ecb47", "sha256_in_prefix": "a2ef7adb341d8af5ff15f315ca1ae90894a20a696d598edc6d27a2ac9c8ecb47", "size_in_bytes": 4893}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/cuda_gpu_mem_size_sum.py", "path_type": "hardlink", "sha256": "06074c7bf168ac9fc788d668b9fb1e80ffde7a3042f682f252415652884691a8", "sha256_in_prefix": "06074c7bf168ac9fc788d668b9fb1e80ffde7a3042f682f252415652884691a8", "size_in_bytes": 5020}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/metadata.json", "path_type": "hardlink", "sha256": "0187893c2a456e6c8d2eddbdf4c19fdc04ac19eb34c98ad827189847ba4f2777", "sha256_in_prefix": "0187893c2a456e6c8d2eddbdf4c19fdc04ac19eb34c98ad827189847ba4f2777", "size_in_bytes": 279}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/stats.ipynb", "path_type": "hardlink", "sha256": "445bc2ebf6dc336c9f1549cd14f1ce0b7e528872e7c90368cac08c73b3fddcf1", "sha256_in_prefix": "445bc2ebf6dc336c9f1549cd14f1ce0b7e528872e7c90368cac08c73b3fddcf1", "size_in_bytes": 4714}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/cuda_gpu_mem_time_sum.py", "path_type": "hardlink", "sha256": "bd44e24ffa0b00d82b0490dc72f5df1d91dfb684f7c1c7db8ede8ab77f28ac31", "sha256_in_prefix": "bd44e24ffa0b00d82b0490dc72f5df1d91dfb684f7c1c7db8ede8ab77f28ac31", "size_in_bytes": 5543}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/metadata.json", "path_type": "hardlink", "sha256": "038b00d2177a594a154559a70e4734931b20cc56b23bf376eefde6d1914f9da4", "sha256_in_prefix": "038b00d2177a594a154559a70e4734931b20cc56b23bf376eefde6d1914f9da4", "size_in_bytes": 267}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/stats.ipynb", "path_type": "hardlink", "sha256": "16c76930a84442095a34440ccfa324c26c6e038dee68bebfc94215e8a7659855", "sha256_in_prefix": "16c76930a84442095a34440ccfa324c26c6e038dee68bebfc94215e8a7659855", "size_in_bytes": 4945}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/cuda_gpu_time_util_map.py", "path_type": "hardlink", "sha256": "2f2af58bc74b45d6924ed453f381ba399dc866422c58c7139b4f921fb5a2a7f2", "sha256_in_prefix": "2f2af58bc74b45d6924ed453f381ba399dc866422c58c7139b4f921fb5a2a7f2", "size_in_bytes": 5514}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "9c31d988f19492a9a9576ef05bd41b04653f7dfdf8c364b8c4e91406f9480493", "sha256_in_prefix": "9c31d988f19492a9a9576ef05bd41b04653f7dfdf8c364b8c4e91406f9480493", "size_in_bytes": 3210}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/metadata.json", "path_type": "hardlink", "sha256": "82725e6d776e4b9170d8ddec994be87d514b938d72daf7acf82cd629005bfb2e", "sha256_in_prefix": "82725e6d776e4b9170d8ddec994be87d514b938d72daf7acf82cd629005bfb2e", "size_in_bytes": 568}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/analysis.ipynb", "path_type": "hardlink", "sha256": "eee60cfb48032fa1a8f00766ec012894330a9ed5b52b51a50845bfee5c84ee24", "sha256_in_prefix": "eee60cfb48032fa1a8f00766ec012894330a9ed5b52b51a50845bfee5c84ee24", "size_in_bytes": 2517}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/cuda_memcpy_async.py", "path_type": "hardlink", "sha256": "acd6819347846ae0bdc97755cc59433881c55195929906c969881ec7cc48028a", "sha256_in_prefix": "acd6819347846ae0bdc97755cc59433881c55195929906c969881ec7cc48028a", "size_in_bytes": 3626}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/metadata.json", "path_type": "hardlink", "sha256": "a8dbbbcc9f69199a41b07c8b0a6c793bc4fd7a7d31b16142faee8f17627ffc98", "sha256_in_prefix": "a8dbbbcc9f69199a41b07c8b0a6c793bc4fd7a7d31b16142faee8f17627ffc98", "size_in_bytes": 276}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/analysis.ipynb", "path_type": "hardlink", "sha256": "53396972d4c83ca37ebc0b150cd292135a25da39ce24491a4a8dc1de4e641dd7", "sha256_in_prefix": "53396972d4c83ca37ebc0b150cd292135a25da39ce24491a4a8dc1de4e641dd7", "size_in_bytes": 2541}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/cuda_memcpy_sync.py", "path_type": "hardlink", "sha256": "69a0a79abfb4b15b65fa8c26b8bb115914d0d040184d444601698729553ee619", "sha256_in_prefix": "69a0a79abfb4b15b65fa8c26b8bb115914d0d040184d444601698729553ee619", "size_in_bytes": 3599}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/metadata.json", "path_type": "hardlink", "sha256": "9c4a1890fd46c952e33a09b32cb1e112b59fe6f3598a07c1c19cee0e064e409c", "sha256_in_prefix": "9c4a1890fd46c952e33a09b32cb1e112b59fe6f3598a07c1c19cee0e064e409c", "size_in_bytes": 369}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/analysis.ipynb", "path_type": "hardlink", "sha256": "214995305d19b27f1b71a7fe9fe757c5089c1a454f4141d3fd9fb8cad2ab47f9", "sha256_in_prefix": "214995305d19b27f1b71a7fe9fe757c5089c1a454f4141d3fd9fb8cad2ab47f9", "size_in_bytes": 2642}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/cuda_memset_sync.py", "path_type": "hardlink", "sha256": "be72dae19a8493016125cebe44761d1e759394b589abb0a74cf3838fc343762e", "sha256_in_prefix": "be72dae19a8493016125cebe44761d1e759394b589abb0a74cf3838fc343762e", "size_in_bytes": 3599}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/metadata.json", "path_type": "hardlink", "sha256": "2fae3bf5f6a082279a6f4e0de9d7888b29151e277cdf471dedbde2b6f944b80a", "sha256_in_prefix": "2fae3bf5f6a082279a6f4e0de9d7888b29151e277cdf471dedbde2b6f944b80a", "size_in_bytes": 250}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/diff.ipynb", "path_type": "hardlink", "sha256": "488cd4a65503584c2b709f116b91bc9e98c4f3c6a4ffc6a5a9085072fb295771", "sha256_in_prefix": "488cd4a65503584c2b709f116b91bc9e98c4f3c6a4ffc6a5a9085072fb295771", "size_in_bytes": 4193}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/diff.py", "path_type": "hardlink", "sha256": "c17a7a1adc91dce24eab5cb7cfb657b18633f6b5816424137812b84153bce475", "sha256_in_prefix": "c17a7a1adc91dce24eab5cb7cfb657b18633f6b5816424137812b84153bce475", "size_in_bytes": 8301}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/metadata.json", "path_type": "hardlink", "sha256": "245a6f632df1bd2b44c36c91b5fe85a3a024f369c3c6b04b06555579f47965f6", "sha256_in_prefix": "245a6f632df1bd2b44c36c91b5fe85a3a024f369c3c6b04b06555579f47965f6", "size_in_bytes": 171}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/analysis.ipynb", "path_type": "hardlink", "sha256": "9862d8d6dab498a899f62dea6c8788d140e17626a22194404e4466f94c0d5143", "sha256_in_prefix": "9862d8d6dab498a899f62dea6c8788d140e17626a22194404e4466f94c0d5143", "size_in_bytes": 4382}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/dx12_mem_ops.py", "path_type": "hardlink", "sha256": "52898e7d0810b74757aca7ecb949932b2f374f94740b6ba05d75f642a662c9fd", "sha256_in_prefix": "52898e7d0810b74757aca7ecb949932b2f374f94740b6ba05d75f642a662c9fd", "size_in_bytes": 3587}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/metadata.json", "path_type": "hardlink", "sha256": "ec9028512e6a9e8ea1b4631ae610d9ae69f7d0b73adc0b0752dcab0152741df5", "sha256_in_prefix": "ec9028512e6a9e8ea1b4631ae610d9ae69f7d0b73adc0b0752dcab0152741df5", "size_in_bytes": 724}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/analysis.ipynb", "path_type": "hardlink", "sha256": "3ede767e68de758dbfcccc803672c4fe0fa3758d8cb1a746b2593cb6f6353f5f", "sha256_in_prefix": "3ede767e68de758dbfcccc803672c4fe0fa3758d8cb1a746b2593cb6f6353f5f", "size_in_bytes": 2672}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/gpu_gaps.py", "path_type": "hardlink", "sha256": "c94d691861d1cc1b7fdd01e36f091376ea40c142b54f69cc4ef4ce81a1183d95", "sha256_in_prefix": "c94d691861d1cc1b7fdd01e36f091376ea40c142b54f69cc4ef4ce81a1183d95", "size_in_bytes": 3796}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/metadata.json", "path_type": "hardlink", "sha256": "b6b0bba376db9e657e08c2d85b880b647c4000218694512dd7b1368076afeda2", "sha256_in_prefix": "b6b0bba376db9e657e08c2d85b880b647c4000218694512dd7b1368076afeda2", "size_in_bytes": 591}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/gpu_metric_util_map.py", "path_type": "hardlink", "sha256": "2d36eb008723a90f2f6b5fa7c7019be4698e7ce5f07cdcad9fe9a990cd712d96", "sha256_in_prefix": "2d36eb008723a90f2f6b5fa7c7019be4698e7ce5f07cdcad9fe9a990cd712d96", "size_in_bytes": 6401}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "61f1c926d1ad2d59f4b41498f42fb9ce7b8510bd5804ba933ed4c8da3ce37ad4", "sha256_in_prefix": "61f1c926d1ad2d59f4b41498f42fb9ce7b8510bd5804ba933ed4c8da3ce37ad4", "size_in_bytes": 3382}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/metadata.json", "path_type": "hardlink", "sha256": "c0ba18319b27b9b54841f9da09f998c3a9050942e1e000384e4f1473738f2476", "sha256_in_prefix": "c0ba18319b27b9b54841f9da09f998c3a9050942e1e000384e4f1473738f2476", "size_in_bytes": 246}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/analysis.ipynb", "path_type": "hardlink", "sha256": "74f409a0ed4f4ed916feac74dc036e5baa48a2ee7df2e71558496d1bdeea77a4", "sha256_in_prefix": "74f409a0ed4f4ed916feac74dc036e5baa48a2ee7df2e71558496d1bdeea77a4", "size_in_bytes": 3647}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/gpu_time_util.py", "path_type": "hardlink", "sha256": "a824e866cd7b72596442c827739117c003c666266caa5d97c8a0d22eb5466605", "sha256_in_prefix": "a824e866cd7b72596442c827739117c003c666266caa5d97c8a0d22eb5466605", "size_in_bytes": 4021}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/metadata.json", "path_type": "hardlink", "sha256": "f0d0bee12b848ae938094332f7542a8cc2d4ed3272957d9a24a4c7560428d949", "sha256_in_prefix": "f0d0bee12b848ae938094332f7542a8cc2d4ed3272957d9a24a4c7560428d949", "size_in_bytes": 1547}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "647840fcfbc1d58498c70391ed5572c8b656aa643ebdc949c2fb4b10ba387bc1", "sha256_in_prefix": "647840fcfbc1d58498c70391ed5572c8b656aa643ebdc949c2fb4b10ba387bc1", "size_in_bytes": 3215}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/metadata.json", "path_type": "hardlink", "sha256": "35ebc6d72103be1d9c4a0ea2f2a5e9a758c12b8c483ae367da46194922eb0974", "sha256_in_prefix": "35ebc6d72103be1d9c4a0ea2f2a5e9a758c12b8c483ae367da46194922eb0974", "size_in_bytes": 727}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/mpi_gpu_time_util_map.py", "path_type": "hardlink", "sha256": "5d258c77a7badd4a33378040ffde7796ecad0fb36cf6e85451f4555c764f975c", "sha256_in_prefix": "5d258c77a7badd4a33378040ffde7796ecad0fb36cf6e85451f4555c764f975c", "size_in_bytes": 6488}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/metadata.json", "path_type": "hardlink", "sha256": "517374fda80cbe46bc4f5acdaaeb0b3d43d6fdfa083447c7ebbf4a9f91dec761", "sha256_in_prefix": "517374fda80cbe46bc4f5acdaaeb0b3d43d6fdfa083447c7ebbf4a9f91dec761", "size_in_bytes": 192}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/mpi_sum.py", "path_type": "hardlink", "sha256": "a53b1ac1b6745d7d91f5f315a05221dc4bfd24b258e8b5102deffe814c8d330d", "sha256_in_prefix": "a53b1ac1b6745d7d91f5f315a05221dc4bfd24b258e8b5102deffe814c8d330d", "size_in_bytes": 4316}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/stats.ipynb", "path_type": "hardlink", "sha256": "c9460421c7025519a484c4295fee8db23fe6fb04593c44cb8b0be85224622991", "sha256_in_prefix": "c9460421c7025519a484c4295fee8db23fe6fb04593c44cb8b0be85224622991", "size_in_bytes": 5724}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_overlap_trace/metadata.json", "path_type": "hardlink", "sha256": "af06ac881ed41f4d0eeeeb8ff5d94783d3f4735846af4d07c5817b644500d547", "sha256_in_prefix": "af06ac881ed41f4d0eeeeb8ff5d94783d3f4735846af4d07c5817b644500d547", "size_in_bytes": 239}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_overlap_trace/nccl_gpu_overlap_trace.py", "path_type": "hardlink", "sha256": "5d24cc4b5eff7f2db45038974ab59f4557f832dbac46d612710cef0ff7fcb5a2", "sha256_in_prefix": "5d24cc4b5eff7f2db45038974ab59f4557f832dbac46d612710cef0ff7fcb5a2", "size_in_bytes": 7734}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_overlap_trace/trace.ipynb", "path_type": "hardlink", "sha256": "9d5949121937872acb690d9e9dc8674d675d87814c91d98b7e3484e8ad44a85e", "sha256_in_prefix": "9d5949121937872acb690d9e9dc8674d675d87814c91d98b7e3484e8ad44a85e", "size_in_bytes": 3005}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/metadata.json", "path_type": "hardlink", "sha256": "5b7d54aa297b06f26063bababc0e125d1d6456253f60e86087f47adf089a9df1", "sha256_in_prefix": "5b7d54aa297b06f26063bababc0e125d1d6456253f60e86087f47adf089a9df1", "size_in_bytes": 287}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/nccl_gpu_proj_sum.py", "path_type": "hardlink", "sha256": "87e187d3a6b3e95c0a24781aff42fde8763fedccd535d89c6e2c9e7d398f71b1", "sha256_in_prefix": "87e187d3a6b3e95c0a24781aff42fde8763fedccd535d89c6e2c9e7d398f71b1", "size_in_bytes": 4867}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/stats.ipynb", "path_type": "hardlink", "sha256": "0b254f7ef8d0b2655f1b2e9d22b196c9d90766569d4e5c9c910c2c803119194d", "sha256_in_prefix": "0b254f7ef8d0b2655f1b2e9d22b196c9d90766569d4e5c9c910c2c803119194d", "size_in_bytes": 6122}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "4d407f91f44a61d19809e8a26d9b89c4fe16f9633ac1e018994e25cce03b1ba0", "sha256_in_prefix": "4d407f91f44a61d19809e8a26d9b89c4fe16f9633ac1e018994e25cce03b1ba0", "size_in_bytes": 3285}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/metadata.json", "path_type": "hardlink", "sha256": "51cd1e4595246dcaf8962af932232bc5d9f6d935de1a162ed4ab9a99d7c1cc6b", "sha256_in_prefix": "51cd1e4595246dcaf8962af932232bc5d9f6d935de1a162ed4ab9a99d7c1cc6b", "size_in_bytes": 751}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/nccl_gpu_time_util_map.py", "path_type": "hardlink", "sha256": "5f73d6da027b46bd48d8a9c45a449d252139dd6b50da6c3bff18aacdb7537320", "sha256_in_prefix": "5f73d6da027b46bd48d8a9c45a449d252139dd6b50da6c3bff18aacdb7537320", "size_in_bytes": 6165}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/metadata.json", "path_type": "hardlink", "sha256": "7dd50142779110805b79534bd228bdeb83c2f23745935c01dac03538d967a497", "sha256_in_prefix": "7dd50142779110805b79534bd228bdeb83c2f23745935c01dac03538d967a497", "size_in_bytes": 195}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/nccl_sum.py", "path_type": "hardlink", "sha256": "ba3c9f276cc7ff6864891c70ec6e19384aa7b0423b769f9d94ced76e5143a892", "sha256_in_prefix": "ba3c9f276cc7ff6864891c70ec6e19384aa7b0423b769f9d94ced76e5143a892", "size_in_bytes": 4139}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/stats.ipynb", "path_type": "hardlink", "sha256": "6d00f6d922d5f3b033da1a75f4a1404d7bf2ad383053986655927a23a507bd35", "sha256_in_prefix": "6d00f6d922d5f3b033da1a75f4a1404d7bf2ad383053986655927a23a507bd35", "size_in_bytes": 6107}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_map_aws/heatmap.ipynb", "path_type": "hardlink", "sha256": "bd87de99fba5ca46f55ca51dac29241dd30833afe1c2ad1292c002c2983aa97e", "sha256_in_prefix": "bd87de99fba5ca46f55ca51dac29241dd30833afe1c2ad1292c002c2983aa97e", "size_in_bytes": 5826}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_map_aws/metadata.json", "path_type": "hardlink", "sha256": "b1127a94e07780b4de05ef17ea982906c493a03dc93231103a2c8b9ac3b35d1c", "sha256_in_prefix": "b1127a94e07780b4de05ef17ea982906c493a03dc93231103a2c8b9ac3b35d1c", "size_in_bytes": 177}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_map_aws/network_map_aws.py", "path_type": "hardlink", "sha256": "6716d88979a6726cbc5ec9cc5d694cf15826e3dce276ccef7b7a82d9a3ab5d81", "sha256_in_prefix": "6716d88979a6726cbc5ec9cc5d694cf15826e3dce276ccef7b7a82d9a3ab5d81", "size_in_bytes": 7430}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_sum/metadata.json", "path_type": "hardlink", "sha256": "c78faa43d84571ce306db1d684057f2190bc63f409dd18109ec288c75f003bc8", "sha256_in_prefix": "c78faa43d84571ce306db1d684057f2190bc63f409dd18109ec288c75f003bc8", "size_in_bytes": 209}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_sum/network_sum.py", "path_type": "hardlink", "sha256": "40794a15265e41f9774dd0286f3ff1a6d12ef326660eb88fbd4615a510fbb8b7", "sha256_in_prefix": "40794a15265e41f9774dd0286f3ff1a6d12ef326660eb88fbd4615a510fbb8b7", "size_in_bytes": 11043}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_sum/stats.ipynb", "path_type": "hardlink", "sha256": "7a2508a86a4b2e98c4fc6f53e56c0974550a5b77580b4911613e43594032535a", "sha256_in_prefix": "7a2508a86a4b2e98c4fc6f53e56c0974550a5b77580b4911613e43594032535a", "size_in_bytes": 5712}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_traffic_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "e25fe181cd855bedb80f64f4732975ff5a978d050c56872559a2aedb5aa8dcb3", "sha256_in_prefix": "e25fe181cd855bedb80f64f4732975ff5a978d050c56872559a2aedb5aa8dcb3", "size_in_bytes": 6241}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_traffic_map/metadata.json", "path_type": "hardlink", "sha256": "1e38aea669dfcc1b5ae871b98dc5ef79ff077d54757080cedb28fbe857471671", "sha256_in_prefix": "1e38aea669dfcc1b5ae871b98dc5ef79ff077d54757080cedb28fbe857471671", "size_in_bytes": 253}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/network_traffic_map/network_traffic_map.py", "path_type": "hardlink", "sha256": "7f49adfea8520e7bb5ab5dc3aadac1da8bbddf8700f8e2ff2d8306b1ce5ab947", "sha256_in_prefix": "7f49adfea8520e7bb5ab5dc3aadac1da8bbddf8700f8e2ff2d8306b1ce5ab947", "size_in_bytes": 14008}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvlink_sum/metadata.json", "path_type": "hardlink", "sha256": "757a8035bf64d0c1becc00938bbc919e2597331219f2e9834e8be9164c593bd0", "sha256_in_prefix": "757a8035bf64d0c1becc00938bbc919e2597331219f2e9834e8be9164c593bd0", "size_in_bytes": 201}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvlink_sum/nvlink_sum.py", "path_type": "hardlink", "sha256": "5bcfdaf06c3d816c00a5ad12be58f0cf5df77e7147655f2be3f7bacf11ed64d2", "sha256_in_prefix": "5bcfdaf06c3d816c00a5ad12be58f0cf5df77e7147655f2be3f7bacf11ed64d2", "size_in_bytes": 7123}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvlink_sum/stats.ipynb", "path_type": "hardlink", "sha256": "06d1245ad9614fcd8ae2f9fc93cdc111eec2b5b3f8d23e36f60aada8a0abb942", "sha256_in_prefix": "06d1245ad9614fcd8ae2f9fc93cdc111eec2b5b3f8d23e36f60aada8a0abb942", "size_in_bytes": 3871}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/metadata.json", "path_type": "hardlink", "sha256": "3873a590f182003f4bd0a64a1aaf88c727c98f32770a15782c8391b3c7e855a2", "sha256_in_prefix": "3873a590f182003f4bd0a64a1aaf88c727c98f32770a15782c8391b3c7e855a2", "size_in_bytes": 296}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/nvtx_gpu_proj_pace.py", "path_type": "hardlink", "sha256": "2d07925af0427b0cf3fc4691a5d4ff33f5251ae621f8f5c21bcf7aeaa2c04c57", "sha256_in_prefix": "2d07925af0427b0cf3fc4691a5d4ff33f5251ae621f8f5c21bcf7aeaa2c04c57", "size_in_bytes": 5193}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/pace.ipynb", "path_type": "hardlink", "sha256": "ff639797d7d8a6bf48a3a0d75028eabf180d61a1474af8c2b6799aa020812ce4", "sha256_in_prefix": "ff639797d7d8a6bf48a3a0d75028eabf180d61a1474af8c2b6799aa020812ce4", "size_in_bytes": 11512}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/metadata.json", "path_type": "hardlink", "sha256": "58eafdfbd5e4003ad260023998e1c4ac20afc75f5565577eb5dead84e8d9dfb0", "sha256_in_prefix": "58eafdfbd5e4003ad260023998e1c4ac20afc75f5565577eb5dead84e8d9dfb0", "size_in_bytes": 289}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/nvtx_gpu_proj_sum.py", "path_type": "hardlink", "sha256": "d43e6e7638ea2b4fc6a61bef1601c1bf15a82e3ea7d944193c9416e640af2419", "sha256_in_prefix": "d43e6e7638ea2b4fc6a61bef1601c1bf15a82e3ea7d944193c9416e640af2419", "size_in_bytes": 7137}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/stats.ipynb", "path_type": "hardlink", "sha256": "bdc01d1aaa9fadb8299f410690634dcc7d34fc8b4bb24da882c8721e63a8f4e3", "sha256_in_prefix": "bdc01d1aaa9fadb8299f410690634dcc7d34fc8b4bb24da882c8721e63a8f4e3", "size_in_bytes": 6520}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/topN.ipynb", "path_type": "hardlink", "sha256": "05bbc17e298adafdbd4f833b8f74d5199872c58b6ea996a8b522acefcce752ee", "sha256_in_prefix": "05bbc17e298adafdbd4f833b8f74d5199872c58b6ea996a8b522acefcce752ee", "size_in_bytes": 4342}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/metadata.json", "path_type": "hardlink", "sha256": "120d66a0302c21ada344ef16af3702dcccc9bb12385feb99b2cafe7c5e9e7622", "sha256_in_prefix": "120d66a0302c21ada344ef16af3702dcccc9bb12385feb99b2cafe7c5e9e7622", "size_in_bytes": 799}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/nvtx_gpu_proj_trace.py", "path_type": "hardlink", "sha256": "b8a883c4a82c2b60d78b2fbc7b27055e6cf4e7bd9c56b1f308f13338e6bf29b7", "sha256_in_prefix": "b8a883c4a82c2b60d78b2fbc7b27055e6cf4e7bd9c56b1f308f13338e6bf29b7", "size_in_bytes": 6618}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/trace.ipynb", "path_type": "hardlink", "sha256": "4e031dfc0f751cfd27b88323047e6c0de8fb828c9caee1e2ba5ddffea93a8f4e", "sha256_in_prefix": "4e031dfc0f751cfd27b88323047e6c0de8fb828c9caee1e2ba5ddffea93a8f4e", "size_in_bytes": 2876}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/metadata.json", "path_type": "hardlink", "sha256": "55c0e534c2d25fda04b317be19412f1d31e6fabca37e2dc74887e5908ca93786", "sha256_in_prefix": "55c0e534c2d25fda04b317be19412f1d31e6fabca37e2dc74887e5908ca93786", "size_in_bytes": 237}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/nvtx_pace.py", "path_type": "hardlink", "sha256": "353f2927befc7baa28e63c3abc4e8f660ae86681d470f10618a067764815bac1", "sha256_in_prefix": "353f2927befc7baa28e63c3abc4e8f660ae86681d470f10618a067764815bac1", "size_in_bytes": 4678}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/pace.ipynb", "path_type": "hardlink", "sha256": "ff639797d7d8a6bf48a3a0d75028eabf180d61a1474af8c2b6799aa020812ce4", "sha256_in_prefix": "ff639797d7d8a6bf48a3a0d75028eabf180d61a1474af8c2b6799aa020812ce4", "size_in_bytes": 11512}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/metadata.json", "path_type": "hardlink", "sha256": "705e0f05c4f75902cc7544ffc59042e1eb3659d71de2258235c0e970aa17dc1d", "sha256_in_prefix": "705e0f05c4f75902cc7544ffc59042e1eb3659d71de2258235c0e970aa17dc1d", "size_in_bytes": 253}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/nvtx_sum.py", "path_type": "hardlink", "sha256": "30ab04097d0306534eace1cda58c85ed1ccde5d013621d005ae83c5b3aae4d8c", "sha256_in_prefix": "30ab04097d0306534eace1cda58c85ed1ccde5d013621d005ae83c5b3aae4d8c", "size_in_bytes": 4390}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/stats.ipynb", "path_type": "hardlink", "sha256": "d9871f7d1a0558ea48f3cea547e6c40b542db2254c123fe5dadb39d4fbb75332", "sha256_in_prefix": "d9871f7d1a0558ea48f3cea547e6c40b542db2254c123fe5dadb39d4fbb75332", "size_in_bytes": 5997}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/metadata.json", "path_type": "hardlink", "sha256": "4a1191e99a0be0117da7f77433a1078db2be14d0c264bbee07649d7ebd6011a1", "sha256_in_prefix": "4a1191e99a0be0117da7f77433a1078db2be14d0c264bbee07649d7ebd6011a1", "size_in_bytes": 206}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/osrt_sum.py", "path_type": "hardlink", "sha256": "4f3edcbe6e496fe88872097279920ee63d0f0e255db33fe2b796d2a463c87e1f", "sha256_in_prefix": "4f3edcbe6e496fe88872097279920ee63d0f0e255db33fe2b796d2a463c87e1f", "size_in_bytes": 4461}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/stats.ipynb", "path_type": "hardlink", "sha256": "7075a933109b7e9aa7f8997357b2193f373c68bd7fffa332fed41146dac92ff6", "sha256_in_prefix": "7075a933109b7e9aa7f8997357b2193f373c68bd7fffa332fed41146dac92ff6", "size_in_bytes": 4805}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "bf7e8b2819fe15b8eb00c9889e115c74968e7e49cdd62a04bb70cd5688c81e8a", "sha256_in_prefix": "bf7e8b2819fe15b8eb00c9889e115c74968e7e49cdd62a04bb70cd5688c81e8a", "size_in_bytes": 3215}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/metadata.json", "path_type": "hardlink", "sha256": "6759ca3df8ada8dda7de09589d6f09923abd28e6bb1663ae9cd82117b71b2a51", "sha256_in_prefix": "6759ca3df8ada8dda7de09589d6f09923abd28e6bb1663ae9cd82117b71b2a51", "size_in_bytes": 727}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/ucx_gpu_time_util_map.py", "path_type": "hardlink", "sha256": "a451837b93cf3d0385d448714613e845b907e773c96ff6b204f8f7ec7163a5f3", "sha256_in_prefix": "a451837b93cf3d0385d448714613e845b907e773c96ff6b204f8f7ec7163a5f3", "size_in_bytes": 6488}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/common.txt", "path_type": "hardlink", "sha256": "6e839527145b9976ab48b2840a302c8f7904f7bf3fa67ae8529cdab907803843", "sha256_in_prefix": "6e839527145b9976ab48b2840a302c8f7904f7bf3fa67ae8529cdab907803843", "size_in_bytes": 32}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/dask.txt", "path_type": "hardlink", "sha256": "4e70e288f9d23ea3b8b74b5f61a5be1872e0c980e3fbeb241be3447e8a543ece", "sha256_in_prefix": "4e70e288f9d23ea3b8b74b5f61a5be1872e0c980e3fbeb241be3447e8a543ece", "size_in_bytes": 19}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/jupyter.txt", "path_type": "hardlink", "sha256": "f3f636484abf79f2ea23c1ed3f054780eab6080a4fa327e49e4374b27bf1380c", "sha256_in_prefix": "f3f636484abf79f2ea23c1ed3f054780eab6080a4fa327e49e4374b27bf1380c", "size_in_bytes": 44}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/_sqlite3.cpython-312-x86_64-linux-gnu.so", "path_type": "hardlink", "sha256": "93265919137540c92ff52766e862c931dfe01a9962efcddad600319ccf5581af", "sha256_in_prefix": "93265919137540c92ff52766e862c931dfe01a9962efcddad600319ccf5581af", "size_in_bytes": 565248}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/sqlite3/__init__.py", "path_type": "hardlink", "sha256": "8c2cf6c0598d0d4f96f28ee6b4e2abc17c1dd2021f601301d958789635bbf2c8", "sha256_in_prefix": "8c2cf6c0598d0d4f96f28ee6b4e2abc17c1dd2021f601301d958789635bbf2c8", "size_in_bytes": 2571}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/sqlite3/__main__.py", "path_type": "hardlink", "sha256": "eb222aec2cd77d0ee30e9a12d343cf3321f0956733f27b37492df598b36c7ac3", "sha256_in_prefix": "eb222aec2cd77d0ee30e9a12d343cf3321f0956733f27b37492df598b36c7ac3", "size_in_bytes": 3982}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/sqlite3/dbapi2.py", "path_type": "hardlink", "sha256": "4595e60f0893cd6d8c6937c8ba5f7d9beb232e33a5548579b6598a46853a5c13", "sha256_in_prefix": "4595e60f0893cd6d8c6937c8ba5f7d9beb232e33a5548579b6598a46853a5c13", "size_in_bytes": 3739}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/312/sqlite3/dump.py", "path_type": "hardlink", "sha256": "83b6fb156ec8c6478c7284f3d75d2380e47ec2c07898c0d20ef7cec01e159e06", "sha256_in_prefix": "83b6fb156ec8c6478c7284f3d75d2380e47ec2c07898c0d20ef7cec01e159e06", "size_in_bytes": 3559}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/_sqlite3.cpython-310-x86_64-linux-gnu.so", "path_type": "hardlink", "sha256": "c3bf6b3d53bc9beb527f6b8ccb3ea7a0a2f706b76c8b164852efd2a338f01b95", "sha256_in_prefix": "c3bf6b3d53bc9beb527f6b8ccb3ea7a0a2f706b76c8b164852efd2a338f01b95", "size_in_bytes": 110888}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/__init__.py", "path_type": "hardlink", "sha256": "3df225315effca29c26196714cf4653a554671ec877019b4bb9d2c0d3a951dd6", "sha256_in_prefix": "3df225315effca29c26196714cf4653a554671ec877019b4bb9d2c0d3a951dd6", "size_in_bytes": 2607}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/dbapi2.py", "path_type": "hardlink", "sha256": "7296221686beb47624ea7bf4ab82e9d5aa4e25160042946d2827868897762694", "sha256_in_prefix": "7296221686beb47624ea7bf4ab82e9d5aa4e25160042946d2827868897762694", "size_in_bytes": 3426}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/dump.py", "path_type": "hardlink", "sha256": "30769c19582b0f62506e6bf9e4f36a86f9fd92f2e5c618f770eb14da0c05f16e", "sha256_in_prefix": "30769c19582b0f62506e6bf9e4f36a86f9fd92f2e5c618f770eb14da0c05f16e", "size_in_bytes": 3374}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/README.txt", "path_type": "hardlink", "sha256": "5cea627396f2f88286220ef392a6a7a63e30001580ecb24218215ad7048d9150", "sha256_in_prefix": "5cea627396f2f88286220ef392a6a7a63e30001580ecb24218215ad7048d9150", "size_in_bytes": 1834}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/_sql.py", "path_type": "hardlink", "sha256": "481f67fe10eff85f28ae9191807fd7a5f6d45b13969e190d0929d21d32237a90", "sha256_in_prefix": "481f67fe10eff85f28ae9191807fd7a5f6d45b13969e190d0929d21d32237a90", "size_in_bytes": 1599}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/_sqlfile.py", "path_type": "hardlink", "sha256": "badf8cc4285b5de0b336d7c244b54504aa91624963ab7f02d8d249e5fdfa5f55", "sha256_in_prefix": "badf8cc4285b5de0b336d7c244b54504aa91624963ab7f02d8d249e5fdfa5f55", "size_in_bytes": 1934}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/_tbl.py", "path_type": "hardlink", "sha256": "180f2fcfec9c7bb32851779684fec64aa55eeb633d2a583727554a39874bcffc", "sha256_in_prefix": "180f2fcfec9c7bb32851779684fec64aa55eeb633d2a583727554a39874bcffc", "size_in_bytes": 1865}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/_values.py", "path_type": "hardlink", "sha256": "0ee7456104077287f9785c73f46606d7b923d674b9573aae5aaf5a71216732dc", "sha256_in_prefix": "0ee7456104077287f9785c73f46606d7b923d674b9573aae5aaf5a71216732dc", "size_in_bytes": 2047}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_api_gpu_sum.py", "path_type": "hardlink", "sha256": "2e927304e3ad545594f1c1dac4fa42fcdb039b50da5a3be823e5f762c361b20c", "sha256_in_prefix": "2e927304e3ad545594f1c1dac4fa42fcdb039b50da5a3be823e5f762c361b20c", "size_in_bytes": 6609}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_api_sum.py", "path_type": "hardlink", "sha256": "d42a0a8ebd60a757a546bef8f87b7090807ffcc17e61aa6f3f679812820998f7", "sha256_in_prefix": "d42a0a8ebd60a757a546bef8f87b7090807ffcc17e61aa6f3f679812820998f7", "size_in_bytes": 3225}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_api_trace.py", "path_type": "hardlink", "sha256": "b3f1ea9c66ccb23e5c89fed64984e458be48d43b29a9d1c34c3dcdd9c44096af", "sha256_in_prefix": "b3f1ea9c66ccb23e5c89fed64984e458be48d43b29a9d1c34c3dcdd9c44096af", "size_in_bytes": 2555}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_kern_gb_sum.py", "path_type": "hardlink", "sha256": "71e331d9ed0bf2f16c14ae4ae2f0c8a23f7903fe5fa9d41409a6a829ea5f7305", "sha256_in_prefix": "71e331d9ed0bf2f16c14ae4ae2f0c8a23f7903fe5fa9d41409a6a829ea5f7305", "size_in_bytes": 4711}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_kern_sum.py", "path_type": "hardlink", "sha256": "fd3f6bb66e27601d814b7d17ad79258da1fb51497c090ae66572be9352c3ff52", "sha256_in_prefix": "fd3f6bb66e27601d814b7d17ad79258da1fb51497c090ae66572be9352c3ff52", "size_in_bytes": 4863}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_mem_size_sum.py", "path_type": "hardlink", "sha256": "034a5a19618ad4bc71c3ee7183e5414e7e05f85d720556f038ed0e2d97fb724b", "sha256_in_prefix": "034a5a19618ad4bc71c3ee7183e5414e7e05f85d720556f038ed0e2d97fb724b", "size_in_bytes": 3504}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_mem_time_sum.py", "path_type": "hardlink", "sha256": "0dc3b6e6f6adb8caad2db977d8a3193c1486ea2b196b969bd14eea5923e67da7", "sha256_in_prefix": "0dc3b6e6f6adb8caad2db977d8a3193c1486ea2b196b969bd14eea5923e67da7", "size_in_bytes": 4144}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_sum.py", "path_type": "hardlink", "sha256": "a271c6e8f1405df84bd4fecc53a03735902e2b1356c4eb6b1dcd19a803eda5db", "sha256_in_prefix": "a271c6e8f1405df84bd4fecc53a03735902e2b1356c4eb6b1dcd19a803eda5db", "size_in_bytes": 5987}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_gpu_trace.py", "path_type": "hardlink", "sha256": "880d80bcad5a22f81acde13ab547558722761f7d3bab8eebcf8410001a4f41b2", "sha256_in_prefix": "880d80bcad5a22f81acde13ab547558722761f7d3bab8eebcf8410001a4f41b2", "size_in_bytes": 9055}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_kern_exec_sum.py", "path_type": "hardlink", "sha256": "2c8fde88766fd522a77d3e54d4adba536d2c8efea377584f37bb62ec9e64f46e", "sha256_in_prefix": "2c8fde88766fd522a77d3e54d4adba536d2c8efea377584f37bb62ec9e64f46e", "size_in_bytes": 7154}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/cuda_kern_exec_trace.py", "path_type": "hardlink", "sha256": "ba258f536f7776b6c1f1ca2152e28903354665bdaf5bb7b878c146daf9cf9d1d", "sha256_in_prefix": "ba258f536f7776b6c1f1ca2152e28903354665bdaf5bb7b878c146daf9cf9d1d", "size_in_bytes": 5863}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/dx11_pix_sum.py", "path_type": "hardlink", "sha256": "397c5dddfceed08ad82bc70937576141bfaa970b59dac9d6182e062efbb928a7", "sha256_in_prefix": "397c5dddfceed08ad82bc70937576141bfaa970b59dac9d6182e062efbb928a7", "size_in_bytes": 3287}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/dx12_gpu_marker_sum.py", "path_type": "hardlink", "sha256": "6591c2497e1729615782342f49f701648ac0d8c254642d4aba0960c36a693c60", "sha256_in_prefix": "6591c2497e1729615782342f49f701648ac0d8c254642d4aba0960c36a693c60", "size_in_bytes": 3076}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/dx12_pix_sum.py", "path_type": "hardlink", "sha256": "d47d8b6e852c1e116de94f52bcc73d011656773701414a8c0dec042e32e0e90b", "sha256_in_prefix": "d47d8b6e852c1e116de94f52bcc73d011656773701414a8c0dec042e32e0e90b", "size_in_bytes": 3288}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/mpi_event_sum.py", "path_type": "hardlink", "sha256": "6fa76709b00a05c5a8dfabf7bbbe6d10b180357d79972b631cec6c2a9d79f87f", "sha256_in_prefix": "6fa76709b00a05c5a8dfabf7bbbe6d10b180357d79972b631cec6c2a9d79f87f", "size_in_bytes": 4757}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/mpi_event_trace.py", "path_type": "hardlink", "sha256": "ca764572c0365e2a265c5fcbce83e59ed2d6be0903f6306e7875f0e48282507a", "sha256_in_prefix": "ca764572c0365e2a265c5fcbce83e59ed2d6be0903f6306e7875f0e48282507a", "size_in_bytes": 5139}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/network_congestion.py", "path_type": "hardlink", "sha256": "f60d587bff68b5b8398937d28cf735db463b19341db83ce6249dbed238fbe927", "sha256_in_prefix": "f60d587bff68b5b8398937d28cf735db463b19341db83ce6249dbed238fbe927", "size_in_bytes": 7001}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_gpu_proj_sum.py", "path_type": "hardlink", "sha256": "8ccf581b51351069938ff5eecd96daf677fba3b65dc00de5e11fa0631263da3a", "sha256_in_prefix": "8ccf581b51351069938ff5eecd96daf677fba3b65dc00de5e11fa0631263da3a", "size_in_bytes": 12172}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_gpu_proj_trace.py", "path_type": "hardlink", "sha256": "6258baa0d2fa2e11d7425d9e5739b6bf342688de13a4d689e84024ce9251ec04", "sha256_in_prefix": "6258baa0d2fa2e11d7425d9e5739b6bf342688de13a4d689e84024ce9251ec04", "size_in_bytes": 10727}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_kern_sum.py", "path_type": "hardlink", "sha256": "bab770cd15cf9621ae4500e1f3c1a9bb433109011df3acd731bc37f9ab5c8d4e", "sha256_in_prefix": "bab770cd15cf9621ae4500e1f3c1a9bb433109011df3acd731bc37f9ab5c8d4e", "size_in_bytes": 9880}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_pushpop_sum.py", "path_type": "hardlink", "sha256": "fd69cefe38342589a42d819392e6f459a744fed96ce3d97fb82d3fd30de16a93", "sha256_in_prefix": "fd69cefe38342589a42d819392e6f459a744fed96ce3d97fb82d3fd30de16a93", "size_in_bytes": 4129}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_pushpop_trace.py", "path_type": "hardlink", "sha256": "2d2906c1ece9697e1316bcd7a010f436da8c6f705e58c8373cef3e18b0a5a259", "sha256_in_prefix": "2d2906c1ece9697e1316bcd7a010f436da8c6f705e58c8373cef3e18b0a5a259", "size_in_bytes": 9501}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_startend_sum.py", "path_type": "hardlink", "sha256": "568a97d5e399ffc9c40325adf65d933f1ce8e6eb56944ab90a4d069282442176", "sha256_in_prefix": "568a97d5e399ffc9c40325adf65d933f1ce8e6eb56944ab90a4d069282442176", "size_in_bytes": 4139}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvtx_sum.py", "path_type": "hardlink", "sha256": "c9dc65ddff7037e931864f33155db463e0c9b8135736cb3622b7da8e424534eb", "sha256_in_prefix": "c9dc65ddff7037e931864f33155db463e0c9b8135736cb3622b7da8e424534eb", "size_in_bytes": 5038}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/nvvideo_api_sum.py", "path_type": "hardlink", "sha256": "11fd24f975777e1a5a7c806aaaf78c7d8ebd8f565177cc533e71567a9e3a543c", "sha256_in_prefix": "11fd24f975777e1a5a7c806aaaf78c7d8ebd8f565177cc533e71567a9e3a543c", "size_in_bytes": 3983}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/openacc_sum.py", "path_type": "hardlink", "sha256": "24ec1e360ba1fcd24df600058a1f2254e926d14408b63e6d42ff678530830d2a", "sha256_in_prefix": "24ec1e360ba1fcd24df600058a1f2254e926d14408b63e6d42ff678530830d2a", "size_in_bytes": 3865}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/opengl_khr_gpu_range_sum.py", "path_type": "hardlink", "sha256": "20ca6feb0d6ef0c5f5c2df2c77b088def47f0ac504970362185ecbd86c4bce56", "sha256_in_prefix": "20ca6feb0d6ef0c5f5c2df2c77b088def47f0ac504970362185ecbd86c4bce56", "size_in_bytes": 3575}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/opengl_khr_range_sum.py", "path_type": "hardlink", "sha256": "131d1fc8111235e3be871b3090ee4f8d40fb856f578858ec89fe07577688e13f", "sha256_in_prefix": "131d1fc8111235e3be871b3090ee4f8d40fb856f578858ec89fe07577688e13f", "size_in_bytes": 3553}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/openmp_sum.py", "path_type": "hardlink", "sha256": "ab7ba98cb33536c08ab5feda21aca501bea969fd2be324f862432b995e273d35", "sha256_in_prefix": "ab7ba98cb33536c08ab5feda21aca501bea969fd2be324f862432b995e273d35", "size_in_bytes": 3334}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/osrt_sum.py", "path_type": "hardlink", "sha256": "462ab435968514afd8ec0c14c775fc46c357bbed7bbf14818a03636849f2fd0e", "sha256_in_prefix": "462ab435968514afd8ec0c14c775fc46c357bbed7bbf14818a03636849f2fd0e", "size_in_bytes": 3133}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/syscall_sum.py", "path_type": "hardlink", "sha256": "ecd67a1ea9ff4addf5cbbb7815c3d22505c2a9412c5426105d829e95e582fd72", "sha256_in_prefix": "ecd67a1ea9ff4addf5cbbb7815c3d22505c2a9412c5426105d829e95e582fd72", "size_in_bytes": 3098}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/um_cpu_page_faults_sum.py", "path_type": "hardlink", "sha256": "118dacbb41dc71529eb5cc1e01d456fe102c5c84f902b871ef4b2b06c74e911a", "sha256_in_prefix": "118dacbb41dc71529eb5cc1e01d456fe102c5c84f902b871ef4b2b06c74e911a", "size_in_bytes": 2458}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/um_sum.py", "path_type": "hardlink", "sha256": "b7451a1b2640a89d4fca1790e61a81073c90accb52375fdff1d9d5feaf14cba8", "sha256_in_prefix": "b7451a1b2640a89d4fca1790e61a81073c90accb52375fdff1d9d5feaf14cba8", "size_in_bytes": 5839}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/um_total_sum.py", "path_type": "hardlink", "sha256": "ff1b0b93edc195b616b00de72a57a4ceae86ee24e367fa9c81544d2a8a6ec764", "sha256_in_prefix": "ff1b0b93edc195b616b00de72a57a4ceae86ee24e367fa9c81544d2a8a6ec764", "size_in_bytes": 3889}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/vulkan_api_sum.py", "path_type": "hardlink", "sha256": "1eb46dba9eeaeb3bb865ef474094c1855d8d395a2824d56842320c6ae30c91dc", "sha256_in_prefix": "1eb46dba9eeaeb3bb865ef474094c1855d8d395a2824d56842320c6ae30c91dc", "size_in_bytes": 3134}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/vulkan_api_trace.py", "path_type": "hardlink", "sha256": "c61659115168bd634aa39247244248622a3f17c1e9f4a8b882a396d0841d0af8", "sha256_in_prefix": "c61659115168bd634aa39247244248622a3f17c1e9f4a8b882a396d0841d0af8", "size_in_bytes": 2494}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/vulkan_gpu_marker_sum.py", "path_type": "hardlink", "sha256": "b1fe7affbab86541c8f97631a5c4113cc72f5cd365de8e68f20d3242da787816", "sha256_in_prefix": "b1fe7affbab86541c8f97631a5c4113cc72f5cd365de8e68f20d3242da787816", "size_in_bytes": 3538}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/vulkan_marker_sum.py", "path_type": "hardlink", "sha256": "6936986891c04797ff4d776bc0e4813017bf3a53cc79b565f87c62068feb3013", "sha256_in_prefix": "6936986891c04797ff4d776bc0e4813017bf3a53cc79b565f87c62068feb3013", "size_in_bytes": 3314}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/reports/wddm_queue_sum.py", "path_type": "hardlink", "sha256": "fed9f9dad9d6c935e5fa32e8b2cee151a5909623a236da64dc7a38a3c22fe74c", "sha256_in_prefix": "fed9f9dad9d6c935e5fa32e8b2cee151a5909623a236da64dc7a38a3c22fe74c", "size_in_bytes": 8048}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/README.txt", "path_type": "hardlink", "sha256": "5cea627396f2f88286220ef392a6a7a63e30001580ecb24218215ad7048d9150", "sha256_in_prefix": "5cea627396f2f88286220ef392a6a7a63e30001580ecb24218215ad7048d9150", "size_in_bytes": 1834}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/cuda_api_sync.py", "path_type": "hardlink", "sha256": "c49eb386d3814ecb21db67fbf3350d1e567eb8147091eb62ceeb230fbeae72c4", "sha256_in_prefix": "c49eb386d3814ecb21db67fbf3350d1e567eb8147091eb62ceeb230fbeae72c4", "size_in_bytes": 4289}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/cuda_memcpy_async.py", "path_type": "hardlink", "sha256": "4ee853aaa5d2a45bcdc2919d2f99e88887ddb960e9245a5edc0587f0c86bcbc5", "sha256_in_prefix": "4ee853aaa5d2a45bcdc2919d2f99e88887ddb960e9245a5edc0587f0c86bcbc5", "size_in_bytes": 5274}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/cuda_memcpy_sync.py", "path_type": "hardlink", "sha256": "8d26e68e85b98edfe53d0ee0c63f26e9b8381dab4e6ccad65354a98a89a7082a", "sha256_in_prefix": "8d26e68e85b98edfe53d0ee0c63f26e9b8381dab4e6ccad65354a98a89a7082a", "size_in_bytes": 5440}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/cuda_memset_sync.py", "path_type": "hardlink", "sha256": "77ce940b2372ac193cdce179844ee36a5e8a7280b0fa6ebe4913790184b303de", "sha256_in_prefix": "77ce940b2372ac193cdce179844ee36a5e8a7280b0fa6ebe4913790184b303de", "size_in_bytes": 5141}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/dx12_mem_ops.py", "path_type": "hardlink", "sha256": "119460df024b6a1fb483b8b1c3a073eb55c1b9f6590373bb86590367dcc8070f", "sha256_in_prefix": "119460df024b6a1fb483b8b1c3a073eb55c1b9f6590373bb86590367dcc8070f", "size_in_bytes": 6690}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/gpu_gaps.py", "path_type": "hardlink", "sha256": "87ea513042afb69145f1aeb57fa788663fce678e77d57b437d9541e0bca2c237", "sha256_in_prefix": "87ea513042afb69145f1aeb57fa788663fce678e77d57b437d9541e0bca2c237", "size_in_bytes": 6949}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/rules/gpu_time_util.py", "path_type": "hardlink", "sha256": "a14897c4f7f652cca3f9cb746008f80c7199460a5bfc106a2c75aa826908636b", "sha256_in_prefix": "a14897c4f7f652cca3f9cb746008f80c7199460a5bfc106a2c75aa826908636b", "size_in_bytes": 10857}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/sqlite3.dll", "path_type": "hardlink", "sha256": "eaff2378c7f77ee9a3a25867b6c3ca46c1dd8bfe079b863884a9d184159356e4", "sha256_in_prefix": "eaff2378c7f77ee9a3a25867b6c3ca46c1dd8bfe079b863884a9d184159356e4", "size_in_bytes": 15301200}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/sqlite3.exe", "path_type": "hardlink", "sha256": "da5dd1076fce76ade47efff1ac89a5e4126e867ff79926932074e54190ab1fad", "sha256_in_prefix": "da5dd1076fce76ade47efff1ac89a5e4126e867ff79926932074e54190ab1fad", "size_in_bytes": 15494216}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/targetsettings.xml", "path_type": "hardlink", "sha256": "0cf44e963557e93fc2aba17d020380594074a391e4806ed226f1f1a45e515827", "sha256_in_prefix": "0cf44e963557e93fc2aba17d020380594074a391e4806ed226f1f1a45e515827", "size_in_bytes": 6377}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/vulkan-layers/VkLayer_nsight-sys_systemwide_windows.json", "path_type": "hardlink", "sha256": "20e74a1e3917a2e8dee7e451b8855b3a9752abcba3251414321145ceeb01ce4d", "sha256_in_prefix": "20e74a1e3917a2e8dee7e451b8855b3a9752abcba3251414321145ceeb01ce4d", "size_in_bytes": 3128}, {"_path": "Library/nsight-compute/2025.1.1/host/target-windows-x64/vulkan-layers/VkLayer_nsight-sys_windows.json", "path_type": "hardlink", "sha256": "3d418c5e95aa2d62eab7ee4aa940c8616d6005106df5a4fffd343838318f56f6", "sha256_in_prefix": "3d418c5e95aa2d62eab7ee4aa940c8616d6005106df5a4fffd343838318f56f6", "size_in_bytes": 3428}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AgentAPI.dll", "path_type": "hardlink", "sha256": "a1e868d815f68a66b66d15ac892cf06575b130ecec22e04669fc06138d64e0c0", "sha256_in_prefix": "a1e868d815f68a66b66d15ac892cf06575b130ecec22e04669fc06138d64e0c0", "size_in_bytes": 1080384}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Analysis.dll", "path_type": "hardlink", "sha256": "0fe70ed49dbfd3f7c7ea600d84771107023df5c820e7d40e780dbcea6b233a22", "sha256_in_prefix": "0fe70ed49dbfd3f7c7ea600d84771107023df5c820e7d40e780dbcea6b233a22", "size_in_bytes": 32637512}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AnalysisContainersData.dll", "path_type": "hardlink", "sha256": "4bf17f17bc5639cc81cfe7d0d95c124f3af6f34b0566b5bd2c59456ae0a0afc7", "sha256_in_prefix": "4bf17f17bc5639cc81cfe7d0d95c124f3af6f34b0566b5bd2c59456ae0a0afc7", "size_in_bytes": 618056}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AnalysisData.dll", "path_type": "hardlink", "sha256": "aecd2d2fc27009e461a97b3d66dd32177d8b95056e79a97aa53275c8dbbf189b", "sha256_in_prefix": "aecd2d2fc27009e461a97b3d66dd32177d8b95056e79a97aa53275c8dbbf189b", "size_in_bytes": 5253192}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AnalysisProto.dll", "path_type": "hardlink", "sha256": "d566a2e1652955522b5cd471c2e5762ee1c90a465b23f47daae73751061880a8", "sha256_in_prefix": "d566a2e1652955522b5cd471c2e5762ee1c90a465b23f47daae73751061880a8", "size_in_bytes": 52296}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AppLib.dll", "path_type": "hardlink", "sha256": "713e2d46b53ff58f037061778e2555608399d16956389e75355e4fee529634ab", "sha256_in_prefix": "713e2d46b53ff58f037061778e2555608399d16956389e75355e4fee529634ab", "size_in_bytes": 2542144}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/AppLibInterfaces.dll", "path_type": "hardlink", "sha256": "bcb688731ea84761fe11030b5344c221f3062cd549fed792d32624500f71f69d", "sha256_in_prefix": "bcb688731ea84761fe11030b5344c221f3062cd549fed792d32624500f71f69d", "size_in_bytes": 135744}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Assert.dll", "path_type": "hardlink", "sha256": "f9b1caaafaa8738f622106689ea481cd02397b41a88bb2d69ae757af59c0841b", "sha256_in_prefix": "f9b1caaafaa8738f622106689ea481cd02397b41a88bb2d69ae757af59c0841b", "size_in_bytes": 196664}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/CommonProtoServices.dll", "path_type": "hardlink", "sha256": "e2e8a93a2195ef718ba5d32c9a1cb31128e7f5f12079fdaae40a99339fa28f0e", "sha256_in_prefix": "e2e8a93a2195ef718ba5d32c9a1cb31128e7f5f12079fdaae40a99339fa28f0e", "size_in_bytes": 3573816}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/CommonProtoStreamSections.dll", "path_type": "hardlink", "sha256": "6b881ae4c8379cc6294c09159ce4cc4376d3d676efe576fef3701b10ed8d5ad6", "sha256_in_prefix": "6b881ae4c8379cc6294c09159ce4cc4376d3d676efe576fef3701b10ed8d5ad6", "size_in_bytes": 72248}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Core.dll", "path_type": "hardlink", "sha256": "3681d14bf5069811f300049d20fba4cd5942eb2d64cb6b48af648f2ea4538887", "sha256_in_prefix": "3681d14bf5069811f300049d20fba4cd5942eb2d64cb6b48af648f2ea4538887", "size_in_bytes": 783432}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/CrashReporter.exe", "path_type": "hardlink", "sha256": "1932044ed5bf7e3eb61086a515a94a75ec1e9e250f70368589d815eb1c37d02a", "sha256_in_prefix": "1932044ed5bf7e3eb61086a515a94a75ec1e9e250f70368589d815eb1c37d02a", "size_in_bytes": 1102432}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/CudaDrvApiWrapper.dll", "path_type": "hardlink", "sha256": "7ccf6a29dc0be8578820e0082a22ecb37715944f2d018d8ae3732912cd84f890", "sha256_in_prefix": "7ccf6a29dc0be8578820e0082a22ecb37715944f2d018d8ae3732912cd84f890", "size_in_bytes": 286816}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/DeviceProperty.dll", "path_type": "hardlink", "sha256": "778d9ece1d1dcc8e5ae015ff79b75e656a990f3fd8fba00d53a063756724a214", "sha256_in_prefix": "778d9ece1d1dcc8e5ae015ff79b75e656a990f3fd8fba00d53a063756724a214", "size_in_bytes": 254536}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/DevicePropertyProto.dll", "path_type": "hardlink", "sha256": "8932491ef9813d06652be894b66b9033bf9503a67a7a2ca962b9d77c1772d023", "sha256_in_prefix": "8932491ef9813d06652be894b66b9033bf9503a67a7a2ca962b9d77c1772d023", "size_in_bytes": 133192}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ETWEventsHandlers.dll", "path_type": "hardlink", "sha256": "a039340d17640234c8003b7f64b1b6d58764f6918404d1a00dedae8c623120eb", "sha256_in_prefix": "a039340d17640234c8003b7f64b1b6d58764f6918404d1a00dedae8c623120eb", "size_in_bytes": 1174584}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/EventSource.dll", "path_type": "hardlink", "sha256": "725fb0d6e1ed2b3dffb8d26efc5d26609db8fc7eff171f7e8b8627ab822aab1b", "sha256_in_prefix": "725fb0d6e1ed2b3dffb8d26efc5d26609db8fc7eff171f7e8b8627ab822aab1b", "size_in_bytes": 113720}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/EventsView.dll", "path_type": "hardlink", "sha256": "fc3bacefc488187ad314920734bd6d255b137fac3f5a341c657ab0a403ffc444", "sha256_in_prefix": "fc3bacefc488187ad314920734bd6d255b137fac3f5a341c657ab0a403ffc444", "size_in_bytes": 286784}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ExternalIntegration.xml", "path_type": "hardlink", "sha256": "3eb4ca832b929de40b787f25e3f3cc3ebcec695c999a6537cfb31169fb2a6cbf", "sha256_in_prefix": "3eb4ca832b929de40b787f25e3f3cc3ebcec695c999a6537cfb31169fb2a6cbf", "size_in_bytes": 1477}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/GenericHierarchy.dll", "path_type": "hardlink", "sha256": "c3493e29b30ecf453a1ada917c842d5c23ede541bddb727bf7dfd084d817fe9f", "sha256_in_prefix": "c3493e29b30ecf453a1ada917c842d5c23ede541bddb727bf7dfd084d817fe9f", "size_in_bytes": 1162832}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/GpuInfo.dll", "path_type": "hardlink", "sha256": "50b71137e273afdeab1e16bb72abe69869ac52ba0eb4d75d6e248978370a5211", "sha256_in_prefix": "50b71137e273afdeab1e16bb72abe69869ac52ba0eb4d75d6e248978370a5211", "size_in_bytes": 790088}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/GpuTraits.dll", "path_type": "hardlink", "sha256": "b084d6818d6ade9ef830e71439e1c63816050729ea1dfce4709422f1bd9aba07", "sha256_in_prefix": "b084d6818d6ade9ef830e71439e1c63816050729ea1dfce4709422f1bd9aba07", "size_in_bytes": 219720}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/HostCommon.dll", "path_type": "hardlink", "sha256": "90e57f02fe5dd84548240c2b19327e532c506d8d88aedc5d4bc01d668fca755d", "sha256_in_prefix": "90e57f02fe5dd84548240c2b19327e532c506d8d88aedc5d4bc01d668fca755d", "size_in_bytes": 20568}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InjectionCommunicator.dll", "path_type": "hardlink", "sha256": "8c7156f9ebaffee2da6206ce5badd0397a0cac9fd98bc14571b6c7953d1cff52", "sha256_in_prefix": "8c7156f9ebaffee2da6206ce5badd0397a0cac9fd98bc14571b6c7953d1cff52", "size_in_bytes": 296016}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceData.dll", "path_type": "hardlink", "sha256": "c9b47ae20b907e158c4b274072797043ca4665e9d8de3418b32d2d209d3ea4bd", "sha256_in_prefix": "c9b47ae20b907e158c4b274072797043ca4665e9d8de3418b32d2d209d3ea4bd", "size_in_bytes": 980544}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceShared.dll", "path_type": "hardlink", "sha256": "6fb867f4bc68b9c21bf8bba979a5f573dbddc4969e7deeb5cc122d7e88815fef", "sha256_in_prefix": "6fb867f4bc68b9c21bf8bba979a5f573dbddc4969e7deeb5cc122d7e88815fef", "size_in_bytes": 424024}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceSharedBase.dll", "path_type": "hardlink", "sha256": "bed5054a2f4dd79956ebd2205d12ea2b1d5974ac91988eb82d20002dcb3ff56c", "sha256_in_prefix": "bed5054a2f4dd79956ebd2205d12ea2b1d5974ac91988eb82d20002dcb3ff56c", "size_in_bytes": 89664}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceSharedCore.dll", "path_type": "hardlink", "sha256": "6f620d21cd6be95913fa7ff4683d5c8f3ef48a64b0de7d7712b5416413e6f2fe", "sha256_in_prefix": "6f620d21cd6be95913fa7ff4683d5c8f3ef48a64b0de7d7712b5416413e6f2fe", "size_in_bytes": 86080}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/InterfaceSharedLoggers.dll", "path_type": "hardlink", "sha256": "598500f4305e72a9400f3a3bb86b4ffd6f2e3b30af717f2ad896e5545108e4c1", "sha256_in_prefix": "598500f4305e72a9400f3a3bb86b4ffd6f2e3b30af717f2ad896e5545108e4c1", "size_in_bytes": 19520}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NetworkInfo.dll", "path_type": "hardlink", "sha256": "720e8de558308bcf5ac25fc998c32ada7c252dd946f2d3963bb01891f0cadddf", "sha256_in_prefix": "720e8de558308bcf5ac25fc998c32ada7c252dd946f2d3963bb01891f0cadddf", "size_in_bytes": 82024}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NvLog.dll", "path_type": "hardlink", "sha256": "2fab153032b0c0c30b6db8708f83124006a392a6d3bc48fb9e508c7d2bbd76c4", "sha256_in_prefix": "2fab153032b0c0c30b6db8708f83124006a392a6d3bc48fb9e508c7d2bbd76c4", "size_in_bytes": 59472}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NvQtGui.dll", "path_type": "hardlink", "sha256": "a5036f44b48d8145001490bb3439d75c77b7623d58e71e4d1538a2da5f3a8f33", "sha256_in_prefix": "a5036f44b48d8145001490bb3439d75c77b7623d58e71e4d1538a2da5f3a8f33", "size_in_bytes": 9663056}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NvmlWrapper.dll", "path_type": "hardlink", "sha256": "3ace41d1fca45e56e5e0099caeb85eafa175e1e603e020f526fe006ce1638866", "sha256_in_prefix": "3ace41d1fca45e56e5e0099caeb85eafa175e1e603e020f526fe006ce1638866", "size_in_bytes": 59464}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/NvtxExtData.dll", "path_type": "hardlink", "sha256": "5e660dfafc37fcb9c6e0f263ace983ac0b663047abf2a8b09462b28b705657d1", "sha256_in_prefix": "5e660dfafc37fcb9c6e0f263ace983ac0b663047abf2a8b09462b28b705657d1", "size_in_bytes": 431152}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/CorePlugin/CorePlugin.dll", "path_type": "hardlink", "sha256": "ffbb418cfc6313cb9cab4b78c6950502eecb8502d74a051b65ae95b3a981e86c", "sha256_in_prefix": "ffbb418cfc6313cb9cab4b78c6950502eecb8502d74a051b65ae95b3a981e86c", "size_in_bytes": 633448}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/ExternalIntegrationPlugin.dll", "path_type": "hardlink", "sha256": "c19b4d10f30b988037b46ea71d62ab6128e6ab49be360aaded4a144479b7002d", "sha256_in_prefix": "c19b4d10f30b988037b46ea71d62ab6128e6ab49be360aaded4a144479b7002d", "size_in_bytes": 76880}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/LinuxPlatformPlugin.dll", "path_type": "hardlink", "sha256": "feda81ac308388ada7fc860608a5aa33ae76e1b537a87fd7912b72578194cab0", "sha256_in_prefix": "feda81ac308388ada7fc860608a5aa33ae76e1b537a87fd7912b72578194cab0", "size_in_bytes": 2048592}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/Manifest.js", "path_type": "hardlink", "sha256": "6ab5b3d23c3ad87ebb4339d549bd312c11a7e9a9f9850645080db13a6b8b9330", "sha256_in_prefix": "6ab5b3d23c3ad87ebb4339d549bd312c11a7e9a9f9850645080db13a6b8b9330", "size_in_bytes": 31647}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/QuadDPlugin.dll", "path_type": "hardlink", "sha256": "cc2d0dc2d11bad930b93d24b807e574bf818f0364af4e2b79012e6abfba2c989", "sha256_in_prefix": "cc2d0dc2d11bad930b93d24b807e574bf818f0364af4e2b79012e6abfba2c989", "size_in_bytes": 7483960}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/default.layout", "path_type": "hardlink", "sha256": "13e3e1525ba1103a1a6ee34b613cf46417da07d283b7b7744923d9bd9ce475f8", "sha256_in_prefix": "13e3e1525ba1103a1a6ee34b613cf46417da07d283b7b7744923d9bd9ce475f8", "size_in_bytes": 991}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/RebelPlugin/RebelPlugin.dll", "path_type": "hardlink", "sha256": "fcb561c114e7fb215d189aac621e3af34426a46cb15306af466394a80d62726d", "sha256_in_prefix": "fcb561c114e7fb215d189aac621e3af34426a46cb15306af466394a80d62726d", "size_in_bytes": 36976744}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/SassDebuggerPlugin/SassDebuggerPlugin.dll", "path_type": "hardlink", "sha256": "2c4c2129f0fd765399b41ea99a083e1a95daf812eb30719f525cb58f6fa66f17", "sha256_in_prefix": "2c4c2129f0fd765399b41ea99a083e1a95daf812eb30719f525cb58f6fa66f17", "size_in_bytes": 1829992}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/TPSConnectionPlugin.dll", "path_type": "hardlink", "sha256": "9d9b0d8c9941e954305655eb79cb64e92f6081c25c673ed6b5a23914cdfd3a30", "sha256_in_prefix": "9d9b0d8c9941e954305655eb79cb64e92f6081c25c673ed6b5a23914cdfd3a30", "size_in_bytes": 1769576}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/TPSSystemServerPlugin.dll", "path_type": "hardlink", "sha256": "e99ea5a18afb9e368c4c5f7c169aee0b06a6e78ff941fb9da03960152342999a", "sha256_in_prefix": "e99ea5a18afb9e368c4c5f7c169aee0b06a6e78ff941fb9da03960152342999a", "size_in_bytes": 1609832}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/TimelinePlugin/Manifest.js", "path_type": "hardlink", "sha256": "146f70ce20fad10e5bba482242d68f9c3155dbc6cf3b1d18793276f8d4ebe2f5", "sha256_in_prefix": "146f70ce20fad10e5bba482242d68f9c3155dbc6cf3b1d18793276f8d4ebe2f5", "size_in_bytes": 1292}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/TimelinePlugin/TimelinePlugin.dll", "path_type": "hardlink", "sha256": "ffdfcef13a168c677154870faf814f5aeb72ce120d27823640bb8da281b2fb4a", "sha256_in_prefix": "ffdfcef13a168c677154870faf814f5aeb72ce120d27823640bb8da281b2fb4a", "size_in_bytes": 27728}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/WindowsPlatformPlugin.dll", "path_type": "hardlink", "sha256": "e1a068b7915ba0979b14a44fcd64cd8b06c6d90cc5e2243126be0fca0d6d2373", "sha256_in_prefix": "e1a068b7915ba0979b14a44fcd64cd8b06c6d90cc5e2243126be0fca0d6d2373", "size_in_bytes": 1828944}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qgif.dll", "path_type": "hardlink", "sha256": "6ff0b57822dae5132e1640afe4f8fd6b75e21cf3f1eae53d70373c25a5506581", "sha256_in_prefix": "6ff0b57822dae5132e1640afe4f8fd6b75e21cf3f1eae53d70373c25a5506581", "size_in_bytes": 48784}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qico.dll", "path_type": "hardlink", "sha256": "bceee911a3ffc1ed7b09a9d79374053fa813a04a22c40b0a4984b845582e3e8f", "sha256_in_prefix": "bceee911a3ffc1ed7b09a9d79374053fa813a04a22c40b0a4984b845582e3e8f", "size_in_bytes": 47248}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qjpeg.dll", "path_type": "hardlink", "sha256": "03ebe96116bf6e98fe967f046e62ab269ff863a3bf4dc9a817e0704b6199899a", "sha256_in_prefix": "03ebe96116bf6e98fe967f046e62ab269ff863a3bf4dc9a817e0704b6199899a", "size_in_bytes": 565392}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qsvg.dll", "path_type": "hardlink", "sha256": "8878473e57bdc0a754a6df4fcdc5c13ed5500adbb0a057f73b21674514adcfc6", "sha256_in_prefix": "8878473e57bdc0a754a6df4fcdc5c13ed5500adbb0a057f73b21674514adcfc6", "size_in_bytes": 40080}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qtga.dll", "path_type": "hardlink", "sha256": "19814f857bd277debc937da91fa871a5a19f1fd2fbd7d34673842c6a80fc581b", "sha256_in_prefix": "19814f857bd277debc937da91fa871a5a19f1fd2fbd7d34673842c6a80fc581b", "size_in_bytes": 39056}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qtiff.dll", "path_type": "hardlink", "sha256": "ed8b3e98816584ad3ef7be3449b5b00b76fd0be0e2d6515cfb30e6a5afbe25cc", "sha256_in_prefix": "ed8b3e98816584ad3ef7be3449b5b00b76fd0be0e2d6515cfb30e6a5afbe25cc", "size_in_bytes": 427664}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qwbmp.dll", "path_type": "hardlink", "sha256": "92283f9f9588a12c630848c0949421dcb9aa33cd6545ff1e3e480ce3d7e7e617", "sha256_in_prefix": "92283f9f9588a12c630848c0949421dcb9aa33cd6545ff1e3e480ce3d7e7e617", "size_in_bytes": 38032}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/platforms/qwindows.dll", "path_type": "hardlink", "sha256": "2bdc29b85bd94170f97aadb1cd447eefe7a3ddf7950c535c81a9ef63e17d1ddc", "sha256_in_prefix": "2bdc29b85bd94170f97aadb1cd447eefe7a3ddf7950c535c81a9ef63e17d1ddc", "size_in_bytes": 890000}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/tls/qcertonlybackend.dll", "path_type": "hardlink", "sha256": "e56916ac77619f4fa291d2024a5ca9a782de3b9992f90de8dedbd821b5bcf394", "sha256_in_prefix": "e56916ac77619f4fa291d2024a5ca9a782de3b9992f90de8dedbd821b5bcf394", "size_in_bytes": 103568}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Plugins/tls/qopensslbackend.dll", "path_type": "hardlink", "sha256": "f4ef459b46f0bb45b276d214bb2625e621c5b524d433fa72b2b10a6f4e81994b", "sha256_in_prefix": "f4ef459b46f0bb45b276d214bb2625e621c5b524d433fa72b2b10a6f4e81994b", "size_in_bytes": 318096}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ProcessLauncher.dll", "path_type": "hardlink", "sha256": "9b71b6565d12c236696e6991984eb492846417d1e47d36d1dd92b5ba64a5a125", "sha256_in_prefix": "9b71b6565d12c236696e6991984eb492846417d1e47d36d1dd92b5ba64a5a125", "size_in_bytes": 510040}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ProtobufComm.dll", "path_type": "hardlink", "sha256": "1a6c65ead1e0dead7d753b10528d4dc7a33fbdc9456cbd4165003e28091e77db", "sha256_in_prefix": "1a6c65ead1e0dead7d753b10528d4dc7a33fbdc9456cbd4165003e28091e77db", "size_in_bytes": 443984}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ProtobufCommClient.dll", "path_type": "hardlink", "sha256": "df06b604f0b6ddfb2a8b275b378145d2d5959f0246bc18d8cca7ed93c5de310f", "sha256_in_prefix": "df06b604f0b6ddfb2a8b275b378145d2d5959f0246bc18d8cca7ed93c5de310f", "size_in_bytes": 312912}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ProtobufCommProto.dll", "path_type": "hardlink", "sha256": "36c82b903dd6f25cb1733bc68efc57cb272db319f4bb92f3db2727c856d2d65e", "sha256_in_prefix": "36c82b903dd6f25cb1733bc68efc57cb272db319f4bb92f3db2727c856d2d65e", "size_in_bytes": 106040}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QdstrmImporter.exe", "path_type": "hardlink", "sha256": "db6c4dcdc1021d9a852727a0c25947e6e82b20f2aef9f096ac4cf62253cf648f", "sha256_in_prefix": "db6c4dcdc1021d9a852727a0c25947e6e82b20f2aef9f096ac4cf62253cf648f", "size_in_bytes": 168512}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Charts.dll", "path_type": "hardlink", "sha256": "efaaa7ede6f63ad8a7831daf84ae5dcf27c05ebb92052f72e7506bead612ccdf", "sha256_in_prefix": "efaaa7ede6f63ad8a7831daf84ae5dcf27c05ebb92052f72e7506bead612ccdf", "size_in_bytes": 1753744}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Concurrent.dll", "path_type": "hardlink", "sha256": "d1b98a6935decfe30f41922e757be29f4681e456da8e8384d6943953038b470d", "sha256_in_prefix": "d1b98a6935decfe30f41922e757be29f4681e456da8e8384d6943953038b470d", "size_in_bytes": 35472}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Core.dll", "path_type": "hardlink", "sha256": "225bd38093416c825f2e3220213f64e1079e9ab20f4738decc0fc6eb992e8a9e", "sha256_in_prefix": "225bd38093416c825f2e3220213f64e1079e9ab20f4738decc0fc6eb992e8a9e", "size_in_bytes": 6326416}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Designer.dll", "path_type": "hardlink", "sha256": "029dbe13e8ce50e77645f02685d4ee6d3449d08b5c17a2482fa8c5da5e9e77d0", "sha256_in_prefix": "029dbe13e8ce50e77645f02685d4ee6d3449d08b5c17a2482fa8c5da5e9e77d0", "size_in_bytes": 5310096}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6DesignerComponents.dll", "path_type": "hardlink", "sha256": "001415ad0a7c23f40b72c76b453610da8e53b1bf3dd86d1fe99a8a1220b9d07a", "sha256_in_prefix": "001415ad0a7c23f40b72c76b453610da8e53b1bf3dd86d1fe99a8a1220b9d07a", "size_in_bytes": 2552976}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Gui.dll", "path_type": "hardlink", "sha256": "fc2464f62d7915ddeaebb5490bee6d60e7b42ad5a223d5812f0993c27c35be19", "sha256_in_prefix": "fc2464f62d7915ddeaebb5490bee6d60e7b42ad5a223d5812f0993c27c35be19", "size_in_bytes": 8932496}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Help.dll", "path_type": "hardlink", "sha256": "6f8f340651f953908b28733f842e625177d1f8627e186b09908918c22c87dfe5", "sha256_in_prefix": "6f8f340651f953908b28733f842e625177d1f8627e186b09908918c22c87dfe5", "size_in_bytes": 609424}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Multimedia.dll", "path_type": "hardlink", "sha256": "7b2aaffd8bd1b042d1d028b071d4fbb42420f52d04f45de06c4a80315b9f1b29", "sha256_in_prefix": "7b2aaffd8bd1b042d1d028b071d4fbb42420f52d04f45de06c4a80315b9f1b29", "size_in_bytes": 853648}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6MultimediaQuick.dll", "path_type": "hardlink", "sha256": "ed7788d7be5639d8326c5d61f7d18a123bec79f805e43986bf8ec43c8fe458a8", "sha256_in_prefix": "ed7788d7be5639d8326c5d61f7d18a123bec79f805e43986bf8ec43c8fe458a8", "size_in_bytes": 235152}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6MultimediaWidgets.dll", "path_type": "hardlink", "sha256": "e02b7f46296f711947803a0937f35c84905be142a44ce8355add442a97b976cf", "sha256_in_prefix": "e02b7f46296f711947803a0937f35c84905be142a44ce8355add442a97b976cf", "size_in_bytes": 62608}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Network.dll", "path_type": "hardlink", "sha256": "1071f4f88c65317401bf93a2ffb55e661adcbb84f05911879ab21a6656521a68", "sha256_in_prefix": "1071f4f88c65317401bf93a2ffb55e661adcbb84f05911879ab21a6656521a68", "size_in_bytes": 1515664}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6OpenGL.dll", "path_type": "hardlink", "sha256": "054b0a8d87fc735aa2eb281e5078f8d28bd1c395b7e32de13ef64a8bbc10bb04", "sha256_in_prefix": "054b0a8d87fc735aa2eb281e5078f8d28bd1c395b7e32de13ef64a8bbc10bb04", "size_in_bytes": 1970320}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6OpenGLWidgets.dll", "path_type": "hardlink", "sha256": "d3c7f83da5629ffe596ef0c380bff8d508e81677553b31de3b89c91df5b3dba4", "sha256_in_prefix": "d3c7f83da5629ffe596ef0c380bff8d508e81677553b31de3b89c91df5b3dba4", "size_in_bytes": 64144}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Positioning.dll", "path_type": "hardlink", "sha256": "0de7080d921cf92cb6602d03712d406ede81d5174725ab2115dc5430b871a7d1", "sha256_in_prefix": "0de7080d921cf92cb6602d03712d406ede81d5174725ab2115dc5430b871a7d1", "size_in_bytes": 519824}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6PrintSupport.dll", "path_type": "hardlink", "sha256": "34099d6c1aa4836fa49a01acbfeec106fb766dbac0e1d42dfc6fcc0c4901afa1", "sha256_in_prefix": "34099d6c1aa4836fa49a01acbfeec106fb766dbac0e1d42dfc6fcc0c4901afa1", "size_in_bytes": 406160}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Qml.dll", "path_type": "hardlink", "sha256": "477747d49a8b7f51c408fe7a49cc3dcfa99078040d3059c5586c77d9b04d1a0d", "sha256_in_prefix": "477747d49a8b7f51c408fe7a49cc3dcfa99078040d3059c5586c77d9b04d1a0d", "size_in_bytes": 5048464}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6QmlModels.dll", "path_type": "hardlink", "sha256": "4ae023a87636f5c70c08dbd787e47eecfa0ac15ff741677db323d70bd70a36a1", "sha256_in_prefix": "4ae023a87636f5c70c08dbd787e47eecfa0ac15ff741677db323d70bd70a36a1", "size_in_bytes": 725648}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Quick.dll", "path_type": "hardlink", "sha256": "35e8842051211a1654d6717b8786357e7a93b21a004f941151e7a4af23e16a84", "sha256_in_prefix": "35e8842051211a1654d6717b8786357e7a93b21a004f941151e7a4af23e16a84", "size_in_bytes": 5514384}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6QuickParticles.dll", "path_type": "hardlink", "sha256": "249964bc83d68068c0f25b2f456e060b0aeb49911cfdb3d16a79d393973602bd", "sha256_in_prefix": "249964bc83d68068c0f25b2f456e060b0aeb49911cfdb3d16a79d393973602bd", "size_in_bytes": 595088}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6QuickTest.dll", "path_type": "hardlink", "sha256": "43575f010c5ac99094645876baaba9b48be577fc514791da02a9f95d92f2e3e0", "sha256_in_prefix": "43575f010c5ac99094645876baaba9b48be577fc514791da02a9f95d92f2e3e0", "size_in_bytes": 306832}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6QuickWidgets.dll", "path_type": "hardlink", "sha256": "827c85b5267f76cb58baefeacfda286a50d72ee92de42587f6823ce8f8d6d802", "sha256_in_prefix": "827c85b5267f76cb58baefeacfda286a50d72ee92de42587f6823ce8f8d6d802", "size_in_bytes": 119440}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Sensors.dll", "path_type": "hardlink", "sha256": "d2ab331bc4e365642d292e5b4f5a3bb0708649f357e0b5861d16f2dc066faf96", "sha256_in_prefix": "d2ab331bc4e365642d292e5b4f5a3bb0708649f357e0b5861d16f2dc066faf96", "size_in_bytes": 223888}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Sql.dll", "path_type": "hardlink", "sha256": "cec14ed64352d5c6e1e043d716cbd2d4575ddfff2e48633c6e6fa2670895ee59", "sha256_in_prefix": "cec14ed64352d5c6e1e043d716cbd2d4575ddfff2e48633c6e6fa2670895ee59", "size_in_bytes": 298640}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6StateMachine.dll", "path_type": "hardlink", "sha256": "ffa3e42785df83cc4258a3afd903852ad0deffaec6b9b9402e78a3d75b2da742", "sha256_in_prefix": "ffa3e42785df83cc4258a3afd903852ad0deffaec6b9b9402e78a3d75b2da742", "size_in_bytes": 350864}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Svg.dll", "path_type": "hardlink", "sha256": "22c48c35d9915bc89b13d2dca91c74b8531989a887faf642c795bf593e00306a", "sha256_in_prefix": "22c48c35d9915bc89b13d2dca91c74b8531989a887faf642c795bf593e00306a", "size_in_bytes": 386192}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6SvgWidgets.dll", "path_type": "hardlink", "sha256": "5abaad262368f5fdbf90038d15a410bab92cc4aea61d6c76a760cd198b08db19", "sha256_in_prefix": "5abaad262368f5fdbf90038d15a410bab92cc4aea61d6c76a760cd198b08db19", "size_in_bytes": 57488}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Test.dll", "path_type": "hardlink", "sha256": "c39ba6ae914ac54d2f58f2f4900608b36c463a9521772bbfef39bfd23dac0a7c", "sha256_in_prefix": "c39ba6ae914ac54d2f58f2f4900608b36c463a9521772bbfef39bfd23dac0a7c", "size_in_bytes": 350864}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6UiTools.dll", "path_type": "hardlink", "sha256": "257e7eb9dc6a3ec2004b72ca86be2cfb65c18923aaffaaaf5160f5ebc7c275eb", "sha256_in_prefix": "257e7eb9dc6a3ec2004b72ca86be2cfb65c18923aaffaaaf5160f5ebc7c275eb", "size_in_bytes": 717968}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6WebChannel.dll", "path_type": "hardlink", "sha256": "a27e6bf4eedf09061821b067decb894435caf37bc0864c25c8f4d52481470a60", "sha256_in_prefix": "a27e6bf4eedf09061821b067decb894435caf37bc0864c25c8f4d52481470a60", "size_in_bytes": 251024}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6WebEngineCore.dll", "path_type": "hardlink", "sha256": "eb9e99c15dcb3b077752d74213fd40e2e1c187f0500ef70edd0cad4ad52cdc76", "sha256_in_prefix": "eb9e99c15dcb3b077752d74213fd40e2e1c187f0500ef70edd0cad4ad52cdc76", "size_in_bytes": 143159440}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6WebEngineWidgets.dll", "path_type": "hardlink", "sha256": "02deb3a59a9d60e0912f7de4f5641a7df1c6384f54da22db06b60988063500ae", "sha256_in_prefix": "02deb3a59a9d60e0912f7de4f5641a7df1c6384f54da22db06b60988063500ae", "size_in_bytes": 182416}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6WebSockets.dll", "path_type": "hardlink", "sha256": "ac774267d461fdce1df4e6bb6b1b846076a047b03477f487b9f6a1f7b1d37bf4", "sha256_in_prefix": "ac774267d461fdce1df4e6bb6b1b846076a047b03477f487b9f6a1f7b1d37bf4", "size_in_bytes": 215184}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Widgets.dll", "path_type": "hardlink", "sha256": "c71e65b882a84f47114590784a256f14ba19202ec30b218ce4841b2c7256060b", "sha256_in_prefix": "c71e65b882a84f47114590784a256f14ba19202ec30b218ce4841b2c7256060b", "size_in_bytes": 6469776}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/Qt6Xml.dll", "path_type": "hardlink", "sha256": "8b772f5f227b266c47655d02843bf51be6c50729acc28db7dced488d62f7ed4f", "sha256_in_prefix": "8b772f5f227b266c47655d02843bf51be6c50729acc28db7dced488d62f7ed4f", "size_in_bytes": 155280}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QtPropertyBrowser.dll", "path_type": "hardlink", "sha256": "fac9de7ffcd109ec82a5dfb085817d40c167ca4312d4917ad5665e19656c5d17", "sha256_in_prefix": "fac9de7ffcd109ec82a5dfb085817d40c167ca4312d4917ad5665e19656c5d17", "size_in_bytes": 773704}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QtWebEngineProcess.exe", "path_type": "hardlink", "sha256": "b32623faeda460add4fb6ee73a0e7c288e8eb33e28f4ce0d9b748867ff4f7508", "sha256_in_prefix": "b32623faeda460add4fb6ee73a0e7c288e8eb33e28f4ce0d9b748867ff4f7508", "size_in_bytes": 583824}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QuiverContainers.dll", "path_type": "hardlink", "sha256": "09ae901b7792c099e2290e4fac5329e8c37b6baac5ffceae476ef954f3630056", "sha256_in_prefix": "09ae901b7792c099e2290e4fac5329e8c37b6baac5ffceae476ef954f3630056", "size_in_bytes": 263760}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/QuiverEvents.dll", "path_type": "hardlink", "sha256": "49749ceb841c213424db6d35d0ce30cb1aecd09d7ce9c82a7f2726f235d082ac", "sha256_in_prefix": "49749ceb841c213424db6d35d0ce30cb1aecd09d7ce9c82a7f2726f235d082ac", "size_in_bytes": 581688}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/SshClient.dll", "path_type": "hardlink", "sha256": "c463b0db1e04da2ba28bb4c652fcdaae269c0bf636ab17dc80e58e8332f7895b", "sha256_in_prefix": "c463b0db1e04da2ba28bb4c652fcdaae269c0bf636ab17dc80e58e8332f7895b", "size_in_bytes": 778304}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/StreamSections.dll", "path_type": "hardlink", "sha256": "58c9d783f3fb1334eb8e6d7f9b5f2133306c8a0a7e502f7f4aba494f7e7aa8ec", "sha256_in_prefix": "58c9d783f3fb1334eb8e6d7f9b5f2133306c8a0a7e502f7f4aba494f7e7aa8ec", "size_in_bytes": 399448}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/SymbolAnalyzerLight.dll", "path_type": "hardlink", "sha256": "188e61ba2dd2aef92070cac4079bf65fe1b8b4c9a7c449eb28fbce577bcfd2b1", "sha256_in_prefix": "188e61ba2dd2aef92070cac4079bf65fe1b8b4c9a7c449eb28fbce577bcfd2b1", "size_in_bytes": 206392}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/SymbolDemangler.dll", "path_type": "hardlink", "sha256": "8650041795f5e209ed295163cc21bf38a0c4357967b2b98024a0c30be8921a41", "sha256_in_prefix": "8650041795f5e209ed295163cc21bf38a0c4357967b2b98024a0c30be8921a41", "size_in_bytes": 279608}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TelemetryQuadDClient.dll", "path_type": "hardlink", "sha256": "3a349c4cb7488674ee6cbdd9f603b7e0f19983a8370908ed00ccaf162ed3183b", "sha256_in_prefix": "3a349c4cb7488674ee6cbdd9f603b7e0f19983a8370908ed00ccaf162ed3183b", "size_in_bytes": 107584}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TimelineAssert.dll", "path_type": "hardlink", "sha256": "a388b5b9eaaf84b39b93c119de2fc9f7c4ba0a3071613bc03a61fdb0300eb28b", "sha256_in_prefix": "a388b5b9eaaf84b39b93c119de2fc9f7c4ba0a3071613bc03a61fdb0300eb28b", "size_in_bytes": 20048}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TimelineCommon.dll", "path_type": "hardlink", "sha256": "f64428fd43083981b3a9ab99ff8aec76781ffa00ed59131f8954e17ef527ece4", "sha256_in_prefix": "f64428fd43083981b3a9ab99ff8aec76781ffa00ed59131f8954e17ef527ece4", "size_in_bytes": 114256}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TimelineUIUtils.dll", "path_type": "hardlink", "sha256": "7d0b45ebce9d0bc8408488f18536b123c4e0c438c0a0256e223c5572f63766d7", "sha256_in_prefix": "7d0b45ebce9d0bc8408488f18536b123c4e0c438c0a0256e223c5572f63766d7", "size_in_bytes": 220240}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/TimelineWidget.dll", "path_type": "hardlink", "sha256": "d3e83e5793123b15c230ffbfee34ffa6a6f71404a31f4a51534cff13e0255fc8", "sha256_in_prefix": "d3e83e5793123b15c230ffbfee34ffa6a6f71404a31f4a51534cff13e0255fc8", "size_in_bytes": 1548880}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-file-l1-2-0.dll", "path_type": "hardlink", "sha256": "bd9e07bbc62ce82dbc30c23069a17fbfa17f1c26a9c19e50fe754d494e6cd0b1", "sha256_in_prefix": "bd9e07bbc62ce82dbc30c23069a17fbfa17f1c26a9c19e50fe754d494e6cd0b1", "size_in_bytes": 18624}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-file-l2-1-0.dll", "path_type": "hardlink", "sha256": "355633a84db0816ab6a340a086fb41c65854c313bd08d427a17389c42a1e5b69", "sha256_in_prefix": "355633a84db0816ab6a340a086fb41c65854c313bd08d427a17389c42a1e5b69", "size_in_bytes": 18624}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-localization-l1-2-0.dll", "path_type": "hardlink", "sha256": "b39a515b9e48fc6589703d45e14dcea2273a02d7fa6f2e1d17985c0228d32564", "sha256_in_prefix": "b39a515b9e48fc6589703d45e14dcea2273a02d7fa6f2e1d17985c0228d32564", "size_in_bytes": 21184}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-processthreads-l1-1-1.dll", "path_type": "hardlink", "sha256": "ccb974c24ddfa7446278ca55fc8b236d0605d2caaf273db8390d1813fc70cd5b", "sha256_in_prefix": "ccb974c24ddfa7446278ca55fc8b236d0605d2caaf273db8390d1813fc70cd5b", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-synch-l1-2-0.dll", "path_type": "hardlink", "sha256": "0c5c4dfea72595fb7ae410f8fa8da983b53a83ce81aea144fa20cab613e641b7", "sha256_in_prefix": "0c5c4dfea72595fb7ae410f8fa8da983b53a83ce81aea144fa20cab613e641b7", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-core-timezone-l1-1-0.dll", "path_type": "hardlink", "sha256": "7d4252ab1b79c5801b58a08ce16efd3b30d8235733028e5823f3709bd0a98bcf", "sha256_in_prefix": "7d4252ab1b79c5801b58a08ce16efd3b30d8235733028e5823f3709bd0a98bcf", "size_in_bytes": 18624}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-convert-l1-1-0.dll", "path_type": "hardlink", "sha256": "cd5256b2fb46deaa440950e4a68466b2b0ff61f28888383094182561738d10a9", "sha256_in_prefix": "cd5256b2fb46deaa440950e4a68466b2b0ff61f28888383094182561738d10a9", "size_in_bytes": 22720}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-environment-l1-1-0.dll", "path_type": "hardlink", "sha256": "c4ed8f65c5a0dbf325482a69ab9f8cbd8c97d6120b87ce90ac4cba54ac7d377a", "sha256_in_prefix": "c4ed8f65c5a0dbf325482a69ab9f8cbd8c97d6120b87ce90ac4cba54ac7d377a", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-filesystem-l1-1-0.dll", "path_type": "hardlink", "sha256": "6c86e40c956eb6a77313fa8dd9c46579c5421fa890043f724c004a66796d37a6", "sha256_in_prefix": "6c86e40c956eb6a77313fa8dd9c46579c5421fa890043f724c004a66796d37a6", "size_in_bytes": 20672}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-heap-l1-1-0.dll", "path_type": "hardlink", "sha256": "7050043b0362c928aa63dd7800e5b123c775425eba21a5c57cbc052ebc1b0ba2", "sha256_in_prefix": "7050043b0362c928aa63dd7800e5b123c775425eba21a5c57cbc052ebc1b0ba2", "size_in_bytes": 19648}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-locale-l1-1-0.dll", "path_type": "hardlink", "sha256": "fab41a942f623590402e4150a29d0f6f918ee096dba1e8b320ade3ec286c7475", "sha256_in_prefix": "fab41a942f623590402e4150a29d0f6f918ee096dba1e8b320ade3ec286c7475", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-math-l1-1-0.dll", "path_type": "hardlink", "sha256": "9b05a43fdc185497e8c2cea3c6b9eb0d74327bd70913a298a6e8af64514190e8", "sha256_in_prefix": "9b05a43fdc185497e8c2cea3c6b9eb0d74327bd70913a298a6e8af64514190e8", "size_in_bytes": 27840}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-multibyte-l1-1-0.dll", "path_type": "hardlink", "sha256": "d7e676b9f1e162957d0549ab0b91e2cd754643490b0654bf9a86aa1e77cb3c37", "sha256_in_prefix": "d7e676b9f1e162957d0549ab0b91e2cd754643490b0654bf9a86aa1e77cb3c37", "size_in_bytes": 26816}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-runtime-l1-1-0.dll", "path_type": "hardlink", "sha256": "bf55134f17b93d8ac4d8159a952bee17cb0c925f5256aa7f747c13e5f2d00661", "sha256_in_prefix": "bf55134f17b93d8ac4d8159a952bee17cb0c925f5256aa7f747c13e5f2d00661", "size_in_bytes": 23232}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-stdio-l1-1-0.dll", "path_type": "hardlink", "sha256": "293c76a26fbc0c86dcf5906dd9d9ddc77a5609ea8c191e88bdc907c03b80a3a5", "sha256_in_prefix": "293c76a26fbc0c86dcf5906dd9d9ddc77a5609ea8c191e88bdc907c03b80a3a5", "size_in_bytes": 24768}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-string-l1-1-0.dll", "path_type": "hardlink", "sha256": "01e3c0aa24ce9f8d62753702df5d7a827c390af5e2b76d1f1a5b96c777fd1a4e", "sha256_in_prefix": "01e3c0aa24ce9f8d62753702df5d7a827c390af5e2b76d1f1a5b96c777fd1a4e", "size_in_bytes": 24768}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-time-l1-1-0.dll", "path_type": "hardlink", "sha256": "f4163cbc464a82fce47442447351265a287561c8d64ecc2f2f97f5e73bcb4347", "sha256_in_prefix": "f4163cbc464a82fce47442447351265a287561c8d64ecc2f2f97f5e73bcb4347", "size_in_bytes": 21184}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-utility-l1-1-0.dll", "path_type": "hardlink", "sha256": "bba068f29609630e8c6547f1e9219e11077426c4f1e4a93b712bfba11a149358", "sha256_in_prefix": "bba068f29609630e8c6547f1e9219e11077426c4f1e4a93b712bfba11a149358", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/arrow.dll", "path_type": "hardlink", "sha256": "4edab86c7a191131cb9a042e8f2d2e236daac365ade46c0d4806debaefeb9f24", "sha256_in_prefix": "4edab86c7a191131cb9a042e8f2d2e236daac365ade46c0d4806debaefeb9f24", "size_in_bytes": 15241272}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_atomic-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "eeef3f98b4a1bdfb2e6a9bbd2fd62a40ee8ca33fb78cd10ca1e344134d4d3260", "sha256_in_prefix": "eeef3f98b4a1bdfb2e6a9bbd2fd62a40ee8ca33fb78cd10ca1e344134d4d3260", "size_in_bytes": 27192}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_chrono-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "80abcc68d244b92e68dd8db13ce1cb9d56b8fdc7fe95b955e7bb80342abea86a", "sha256_in_prefix": "80abcc68d244b92e68dd8db13ce1cb9d56b8fdc7fe95b955e7bb80342abea86a", "size_in_bytes": 44600}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_container-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "03462d6578fdc6a4bd82d673a04ab1f618720e2e5a482135876f45af398cfbc6", "sha256_in_prefix": "03462d6578fdc6a4bd82d673a04ab1f618720e2e5a482135876f45af398cfbc6", "size_in_bytes": 65080}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_date_time-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "e94425a58dbad6cb18f263a0afd169e05de60b3af145d0dca5854fc87f7f533a", "sha256_in_prefix": "e94425a58dbad6cb18f263a0afd169e05de60b3af145d0dca5854fc87f7f533a", "size_in_bytes": 19512}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_filesystem-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "88e746970e8b61998cab8988d24ed84958e72365abf53c9778fa1127618bab90", "sha256_in_prefix": "88e746970e8b61998cab8988d24ed84958e72365abf53c9778fa1127618bab90", "size_in_bytes": 140344}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_iostreams-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "312a451fe4f1d35366ed7a36b6e1a5a4b77a4fd1f46e900732c731b2097b7b7e", "sha256_in_prefix": "312a451fe4f1d35366ed7a36b6e1a5a4b77a4fd1f46e900732c731b2097b7b7e", "size_in_bytes": 123448}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_locale-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "8f3455c22f1f14ccf33d2e06b29386199123aadae6863fb49e27201b96810ecc", "sha256_in_prefix": "8f3455c22f1f14ccf33d2e06b29386199123aadae6863fb49e27201b96810ecc", "size_in_bytes": 482360}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_program_options-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "b1b36ad39c614e8d6cbcaf6b1a7c2001adc876e1d0179d5a14d38324b71e708f", "sha256_in_prefix": "b1b36ad39c614e8d6cbcaf6b1a7c2001adc876e1d0179d5a14d38324b71e708f", "size_in_bytes": 357432}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_python310-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "c534325e3fd1ef1c61d5f8be872325c3bbbaf3b21b2ef41ddee40c790886821b", "sha256_in_prefix": "c534325e3fd1ef1c61d5f8be872325c3bbbaf3b21b2ef41ddee40c790886821b", "size_in_bytes": 203832}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_regex-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "99d47ae15adc80cc432c6674dc6921eb567feeda885fdf96edbe6cf93fbba6b4", "sha256_in_prefix": "99d47ae15adc80cc432c6674dc6921eb567feeda885fdf96edbe6cf93fbba6b4", "size_in_bytes": 239672}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_serialization-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "fb12097b19dbdd8471326740be20d525bf644c45f299267b817e8fbb75eeb412", "sha256_in_prefix": "fb12097b19dbdd8471326740be20d525bf644c45f299267b817e8fbb75eeb412", "size_in_bytes": 218680}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_system-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "8626d56068636fe12afa83d95724ecd6c1348779034ab62e8f2d67e17ba5552e", "sha256_in_prefix": "8626d56068636fe12afa83d95724ecd6c1348779034ab62e8f2d67e17ba5552e", "size_in_bytes": 19512}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_thread-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "8c8d0d1eaf52cedf9f8c78fc12c3e8ee66bf76382bd896e4019bbd1074643261", "sha256_in_prefix": "8c8d0d1eaf52cedf9f8c78fc12c3e8ee66bf76382bd896e4019bbd1074643261", "size_in_bytes": 85048}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/boost_timer-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "115680b08f2084f3e531465c12bf17b31c8fe3aa373e4bcd4608f8ec6bc80e5d", "sha256_in_prefix": "115680b08f2084f3e531465c12bf17b31c8fe3aa373e4bcd4608f8ec6bc80e5d", "size_in_bytes": 41528}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/exporter.dll", "path_type": "hardlink", "sha256": "66849391971a129abff9b2e4114fff5b6809b40490674c8702dd6d4b67d38688", "sha256_in_prefix": "66849391971a129abff9b2e4114fff5b6809b40490674c8702dd6d4b67d38688", "size_in_bytes": 25623104}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/libcrypto-3-x64.dll", "path_type": "hardlink", "sha256": "d18dcf82ac5d92b45602a822a9b89f1010393430bdb8d332938f27be70ebdbf2", "sha256_in_prefix": "d18dcf82ac5d92b45602a822a9b89f1010393430bdb8d332938f27be70ebdbf2", "size_in_bytes": 4598320}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/libssl-3-x64.dll", "path_type": "hardlink", "sha256": "ea1cc27bfb03e419626f96154e93f5e9d711ecda9cd6780342dd5a6e1910f18d", "sha256_in_prefix": "ea1cc27bfb03e419626f96154e93f5e9d711ecda9cd6780342dd5a6e1910f18d", "size_in_bytes": 784448}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/msdia140.dll", "path_type": "hardlink", "sha256": "ba67e8b5f387843191ace001ce2ca2ab279458be5e5967e964af2ae62eaaadca", "sha256_in_prefix": "ba67e8b5f387843191ace001ce2ca2ab279458be5e5967e964af2ae62eaaadca", "size_in_bytes": 1870736}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ncu-ui.exe", "path_type": "hardlink", "sha256": "0ea48a7aba452f9bfb35f61529f8e6d6c23b42562dcbbe8862ef9747ffa0395a", "sha256_in_prefix": "0ea48a7aba452f9bfb35f61529f8e6d6c23b42562dcbbe8862ef9747ffa0395a", "size_in_bytes": 1020480}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/nvsym.dll", "path_type": "hardlink", "sha256": "c017040144e76fcb56980b0240d7627300330253479d8bc39364171f3572d51d", "sha256_in_prefix": "c017040144e76fcb56980b0240d7627300330253479d8bc39364171f3572d51d", "size_in_bytes": 4415024}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/odin.dll", "path_type": "hardlink", "sha256": "871840f904de58637a73c943a82ecd3650364af0799d33a26a2dc82e060d80ad", "sha256_in_prefix": "871840f904de58637a73c943a82ecd3650364af0799d33a26a2dc82e060d80ad", "size_in_bytes": 1347152}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/opengl32sw.dll", "path_type": "hardlink", "sha256": "019b7a47a13700ffb648852394663c3ffc4f68d36e56ef7460cc5ff6231a55e9", "sha256_in_prefix": "019b7a47a13700ffb648852394663c3ffc4f68d36e56ef7460cc5ff6231a55e9", "size_in_bytes": 33130032}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/parquet.dll", "path_type": "hardlink", "sha256": "18732e67f7bf02afdcd20a1faf7210271e0450a93711915776483921d8c1fc33", "sha256_in_prefix": "18732e67f7bf02afdcd20a1faf7210271e0450a93711915776483921d8c1fc33", "size_in_bytes": 2189368}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/protobuf-shared.dll", "path_type": "hardlink", "sha256": "c480c3dc8caac5335fcfd5fbb4d67b36fc5699629451eced7de97267b85916d9", "sha256_in_prefix": "c480c3dc8caac5335fcfd5fbb4d67b36fc5699629451eced7de97267b85916d9", "size_in_bytes": 2524240}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/qt.conf", "path_type": "hardlink", "sha256": "6d421d82aa08563ad1a26d44883c58512127693c42feb387645111358323ff06", "sha256_in_prefix": "6d421d82aa08563ad1a26d44883c58512127693c42feb387645111358323ff06", "size_in_bytes": 19}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/icudtl.dat", "path_type": "hardlink", "sha256": "ee3c8883effd90edfb0ff5b758c560cbca25d1598fcb55b80ef67e990dd19d41", "sha256_in_prefix": "ee3c8883effd90edfb0ff5b758c560cbca25d1598fcb55b80ef67e990dd19d41", "size_in_bytes": 10544880}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_devtools_resources.pak", "path_type": "hardlink", "sha256": "4ac9247677d698621f1a3c460c021425afc2fb90ca58fb6c448734bb0fafc955", "sha256_in_prefix": "4ac9247677d698621f1a3c460c021425afc2fb90ca58fb6c448734bb0fafc955", "size_in_bytes": 2695703}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources.pak", "path_type": "hardlink", "sha256": "4cd1ee210232895b074af79b454d969dbaeb35e825ef9169b310fb092c358176", "sha256_in_prefix": "4cd1ee210232895b074af79b454d969dbaeb35e825ef9169b310fb092c358176", "size_in_bytes": 2197928}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources_100p.pak", "path_type": "hardlink", "sha256": "0380de172e7fcc892e6c5acf6eb0799866953ba1dbcde37e35259acfc8adc7c1", "sha256_in_prefix": "0380de172e7fcc892e6c5acf6eb0799866953ba1dbcde37e35259acfc8adc7c1", "size_in_bytes": 148283}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources_200p.pak", "path_type": "hardlink", "sha256": "b5a9884fd53d32abf37260a917eb21b9cd6dda5cf690866c35e6be6dc7096dd0", "sha256_in_prefix": "b5a9884fd53d32abf37260a917eb21b9cd6dda5cf690866c35e6be6dc7096dd0", "size_in_bytes": 193702}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/resources/v8_context_snapshot.bin", "path_type": "hardlink", "sha256": "845c72073f7af721221519b4278b243bdc4900f0f51b7176920df85af7cb51e1", "sha256_in_prefix": "845c72073f7af721221519b4278b243bdc4900f0f51b7176920df85af7cb51e1", "size_in_bytes": 588152}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/sqlite3.dll", "path_type": "hardlink", "sha256": "a59cc771d31e888312ac31fc14dbc3831e1972318ab952f7302073891f4ceeff", "sha256_in_prefix": "a59cc771d31e888312ac31fc14dbc3831e1972318ab952f7302073891f4ceeff", "size_in_bytes": 15291392}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ssh.dll", "path_type": "hardlink", "sha256": "3ad693497715b8a9ffb082a3f7b277a41cd3bd9342060517307f2b2944a346cd", "sha256_in_prefix": "3ad693497715b8a9ffb082a3f7b277a41cd3bd9342060517307f2b2944a346cd", "size_in_bytes": 398912}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/am.pak", "path_type": "hardlink", "sha256": "526e25624bfa1538694e5afb79ad982e7121fd5a4e940eebbdbb57a00b75ee2c", "sha256_in_prefix": "526e25624bfa1538694e5afb79ad982e7121fd5a4e940eebbdbb57a00b75ee2c", "size_in_bytes": 645831}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ar.pak", "path_type": "hardlink", "sha256": "0764940562441b4712a4937e2b2527e53d8acdf98c59f771fb1dd1ca5feb91bf", "sha256_in_prefix": "0764940562441b4712a4937e2b2527e53d8acdf98c59f771fb1dd1ca5feb91bf", "size_in_bytes": 703890}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/bg.pak", "path_type": "hardlink", "sha256": "ea14971dca3bab239cf2cbca9ca679fab319e14d8326de419ff5bf0c645de83a", "sha256_in_prefix": "ea14971dca3bab239cf2cbca9ca679fab319e14d8326de419ff5bf0c645de83a", "size_in_bytes": 736080}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/bn.pak", "path_type": "hardlink", "sha256": "e505637ae353d02108a8cf0b9c2e2b12a21fa743ad928ddb6060b81422ee4014", "sha256_in_prefix": "e505637ae353d02108a8cf0b9c2e2b12a21fa743ad928ddb6060b81422ee4014", "size_in_bytes": 954456}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ca.pak", "path_type": "hardlink", "sha256": "585f58f35572a4bfffd3b3ff93c2210db9c45f5077205e82a6e26ff1c3ef980c", "sha256_in_prefix": "585f58f35572a4bfffd3b3ff93c2210db9c45f5077205e82a6e26ff1c3ef980c", "size_in_bytes": 448057}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/cs.pak", "path_type": "hardlink", "sha256": "3ee215684cc01686c8dba24e9ae9db9bbc7eeb6c9b3ed35f1f277c556e717079", "sha256_in_prefix": "3ee215684cc01686c8dba24e9ae9db9bbc7eeb6c9b3ed35f1f277c556e717079", "size_in_bytes": 459836}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/da.pak", "path_type": "hardlink", "sha256": "73876874c08d3e7e22777cd0227771e7aea2110fa4f6c1dd4bc6f8b5b539fb37", "sha256_in_prefix": "73876874c08d3e7e22777cd0227771e7aea2110fa4f6c1dd4bc6f8b5b539fb37", "size_in_bytes": 416054}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/de.pak", "path_type": "hardlink", "sha256": "764acea99195223b9c25da3988b7b33a5ebcaaa4a033b8227b57666e424cb83e", "sha256_in_prefix": "764acea99195223b9c25da3988b7b33a5ebcaaa4a033b8227b57666e424cb83e", "size_in_bytes": 445731}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/el.pak", "path_type": "hardlink", "sha256": "112627b4cfbe68124f6c42206c4ed2ffc475ee843ef9389047724402bc67cd37", "sha256_in_prefix": "112627b4cfbe68124f6c42206c4ed2ffc475ee843ef9389047724402bc67cd37", "size_in_bytes": 808360}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/en-GB.pak", "path_type": "hardlink", "sha256": "9ea46da1842fb0405ad2e77c73bc0a9a45b87188be8ee885c5e6e337709e0bfd", "sha256_in_prefix": "9ea46da1842fb0405ad2e77c73bc0a9a45b87188be8ee885c5e6e337709e0bfd", "size_in_bytes": 362199}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/en-US.pak", "path_type": "hardlink", "sha256": "6afc79ef72e64c100cd2264489420f9f8bbfa6cc41511e578e08a2bab2dbcbc1", "sha256_in_prefix": "6afc79ef72e64c100cd2264489420f9f8bbfa6cc41511e578e08a2bab2dbcbc1", "size_in_bytes": 365415}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/es-419.pak", "path_type": "hardlink", "sha256": "8fe88a2e0f718c69b1d2e4ec006f49ebecd34217712783a2fd8d5e3b0417ff20", "sha256_in_prefix": "8fe88a2e0f718c69b1d2e4ec006f49ebecd34217712783a2fd8d5e3b0417ff20", "size_in_bytes": 441755}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/es.pak", "path_type": "hardlink", "sha256": "5ce258c1a42921f8f53465b384a8b4d0603566ef774e13b84e00dd9e3d1ca58f", "sha256_in_prefix": "5ce258c1a42921f8f53465b384a8b4d0603566ef774e13b84e00dd9e3d1ca58f", "size_in_bytes": 442318}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/et.pak", "path_type": "hardlink", "sha256": "8ce763b05cb78b7e5ba8162ff5386e8f39b5c9821b5d816e6cea16f25935b9dd", "sha256_in_prefix": "8ce763b05cb78b7e5ba8162ff5386e8f39b5c9821b5d816e6cea16f25935b9dd", "size_in_bytes": 399658}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fa.pak", "path_type": "hardlink", "sha256": "c6a91af7f6f9518a59b738be140fec3d231013f5e91945046b194dff5e6856b1", "sha256_in_prefix": "c6a91af7f6f9518a59b738be140fec3d231013f5e91945046b194dff5e6856b1", "size_in_bytes": 656735}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fi.pak", "path_type": "hardlink", "sha256": "23dc064434a79b55b364520db46a91856aea6afa2cf9fac5c63598bb350234ea", "sha256_in_prefix": "23dc064434a79b55b364520db46a91856aea6afa2cf9fac5c63598bb350234ea", "size_in_bytes": 409290}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fil.pak", "path_type": "hardlink", "sha256": "718bcb71ad1530f8ac8109547d210f657c188295f9c95d93802c3e4f549d73fd", "sha256_in_prefix": "718bcb71ad1530f8ac8109547d210f657c188295f9c95d93802c3e4f549d73fd", "size_in_bytes": 462097}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fr.pak", "path_type": "hardlink", "sha256": "27cf7bddb376b4fdd1226dace0a92356ceded4ee84afc6d4b060546b5ebd9744", "sha256_in_prefix": "27cf7bddb376b4fdd1226dace0a92356ceded4ee84afc6d4b060546b5ebd9744", "size_in_bytes": 479289}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/gu.pak", "path_type": "hardlink", "sha256": "ccfb648d8268bef2dfc22072507af763d25c0e1cac73e90836dad86a4a1f265c", "sha256_in_prefix": "ccfb648d8268bef2dfc22072507af763d25c0e1cac73e90836dad86a4a1f265c", "size_in_bytes": 928484}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/he.pak", "path_type": "hardlink", "sha256": "61fdac6d80b6c1d75750d8f6a6395e4af3be414796058910327093cce0c07a38", "sha256_in_prefix": "61fdac6d80b6c1d75750d8f6a6395e4af3be414796058910327093cce0c07a38", "size_in_bytes": 572937}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hi.pak", "path_type": "hardlink", "sha256": "e835b17f79fb09fe5ff5737c8a5f37a7785ae4daceca69ac58d2d896ef1b8f16", "sha256_in_prefix": "e835b17f79fb09fe5ff5737c8a5f37a7785ae4daceca69ac58d2d896ef1b8f16", "size_in_bytes": 976435}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hr.pak", "path_type": "hardlink", "sha256": "afbee1d7463855356055988c9f6406a7d7fce2b1afba04ff9cf35655a879ea9a", "sha256_in_prefix": "afbee1d7463855356055988c9f6406a7d7fce2b1afba04ff9cf35655a879ea9a", "size_in_bytes": 445338}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hu.pak", "path_type": "hardlink", "sha256": "976aa6895cea6ecc3f6bf9477a04f62c9a00887fd806a6305ee4a9ff5e37496d", "sha256_in_prefix": "976aa6895cea6ecc3f6bf9477a04f62c9a00887fd806a6305ee4a9ff5e37496d", "size_in_bytes": 480244}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/id.pak", "path_type": "hardlink", "sha256": "437cba78a45b984cebca2adbeb731afbebc3a3cc1429e2b2395a5fb42799f83e", "sha256_in_prefix": "437cba78a45b984cebca2adbeb731afbebc3a3cc1429e2b2395a5fb42799f83e", "size_in_bytes": 393573}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/it.pak", "path_type": "hardlink", "sha256": "e82f58921cff6a95d3107950d6ea7b6c72fcfe4eb7e4fb3e1ddbddae736ea232", "sha256_in_prefix": "e82f58921cff6a95d3107950d6ea7b6c72fcfe4eb7e4fb3e1ddbddae736ea232", "size_in_bytes": 435945}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ja.pak", "path_type": "hardlink", "sha256": "ce13991f3d015ca93260067dcbc30da7b60386177e23f62d7cc261daf9841c13", "sha256_in_prefix": "ce13991f3d015ca93260067dcbc30da7b60386177e23f62d7cc261daf9841c13", "size_in_bytes": 532631}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/kn.pak", "path_type": "hardlink", "sha256": "318f67ea3dc03bbca613411dfcda858dc9ea91e309d39a3375c3c18546249b98", "sha256_in_prefix": "318f67ea3dc03bbca613411dfcda858dc9ea91e309d39a3375c3c18546249b98", "size_in_bytes": 1068825}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ko.pak", "path_type": "hardlink", "sha256": "d9b28694ecc99138bbd1945f8b5dd5b149c154bcec6a8a3ea8c5c965ea0049d5", "sha256_in_prefix": "d9b28694ecc99138bbd1945f8b5dd5b149c154bcec6a8a3ea8c5c965ea0049d5", "size_in_bytes": 449627}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/lt.pak", "path_type": "hardlink", "sha256": "3ab229d9b4abf0853389d2666d605a7b5c744864dae5eb8eea272e5d6fc8f53b", "sha256_in_prefix": "3ab229d9b4abf0853389d2666d605a7b5c744864dae5eb8eea272e5d6fc8f53b", "size_in_bytes": 481958}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/lv.pak", "path_type": "hardlink", "sha256": "9e1e28fc39003c93bf1d5c109d05ab0b80335d8a83d26cc1c565cc5870bf0d31", "sha256_in_prefix": "9e1e28fc39003c93bf1d5c109d05ab0b80335d8a83d26cc1c565cc5870bf0d31", "size_in_bytes": 480138}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ml.pak", "path_type": "hardlink", "sha256": "5dea8ac40adb7a9170f939e0b0297b14d2274ab81a90c6f3ee9ccd597b839566", "sha256_in_prefix": "5dea8ac40adb7a9170f939e0b0297b14d2274ab81a90c6f3ee9ccd597b839566", "size_in_bytes": 1113504}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/mr.pak", "path_type": "hardlink", "sha256": "b3644d9067d237b15bc0315a8181f6cd1d7b8e5bc23480a5569a0ac48defd831", "sha256_in_prefix": "b3644d9067d237b15bc0315a8181f6cd1d7b8e5bc23480a5569a0ac48defd831", "size_in_bytes": 911910}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ms.pak", "path_type": "hardlink", "sha256": "a1081976b1815b49ccc86313557cba22cceb61f88c2032aea3aeef64adfd7c85", "sha256_in_prefix": "a1081976b1815b49ccc86313557cba22cceb61f88c2032aea3aeef64adfd7c85", "size_in_bytes": 411416}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/nb.pak", "path_type": "hardlink", "sha256": "af7ec98e818a5bb83cedde4206ee6fe186230dff46cf2c674d038bdca57ea31d", "sha256_in_prefix": "af7ec98e818a5bb83cedde4206ee6fe186230dff46cf2c674d038bdca57ea31d", "size_in_bytes": 402494}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/nl.pak", "path_type": "hardlink", "sha256": "7503e8cd861762f096ccce0ecce1b8ef1bbfe689687b147ebcad671a336835a4", "sha256_in_prefix": "7503e8cd861762f096ccce0ecce1b8ef1bbfe689687b147ebcad671a336835a4", "size_in_bytes": 414990}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pl.pak", "path_type": "hardlink", "sha256": "f2262c67ed743a28de30e60496c9543302b0be84860779d5bc84501b1d15b92f", "sha256_in_prefix": "f2262c67ed743a28de30e60496c9543302b0be84860779d5bc84501b1d15b92f", "size_in_bytes": 462708}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pt-BR.pak", "path_type": "hardlink", "sha256": "8a84a368f51099eb8957fea7c3072885d7e59ad71f40f82f776e83ec06d98d9a", "sha256_in_prefix": "8a84a368f51099eb8957fea7c3072885d7e59ad71f40f82f776e83ec06d98d9a", "size_in_bytes": 436805}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pt-PT.pak", "path_type": "hardlink", "sha256": "872fd7924c6c9f4ea95c72e52a137da470c67c5b3121f8793fb2cd0ae4cc067a", "sha256_in_prefix": "872fd7924c6c9f4ea95c72e52a137da470c67c5b3121f8793fb2cd0ae4cc067a", "size_in_bytes": 438210}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ro.pak", "path_type": "hardlink", "sha256": "a2f5e1fe1b31b9ec1644e1f2c44db8fac0f45363818a291e5ef731d76c5723f2", "sha256_in_prefix": "a2f5e1fe1b31b9ec1644e1f2c44db8fac0f45363818a291e5ef731d76c5723f2", "size_in_bytes": 452838}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ru.pak", "path_type": "hardlink", "sha256": "b20338c16b595e2687a4c579e261741129f14d2d0a54f90d531061ab5fd77327", "sha256_in_prefix": "b20338c16b595e2687a4c579e261741129f14d2d0a54f90d531061ab5fd77327", "size_in_bytes": 737609}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sk.pak", "path_type": "hardlink", "sha256": "3e969c1eee0187dadddd3e4f2c798a50bbbeffa57c53927c5df1822bed32f391", "sha256_in_prefix": "3e969c1eee0187dadddd3e4f2c798a50bbbeffa57c53927c5df1822bed32f391", "size_in_bytes": 467767}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sl.pak", "path_type": "hardlink", "sha256": "3a6efb93181b99db2a31ec9305904106467510c6374224350efd06a0b7001e6b", "sha256_in_prefix": "3a6efb93181b99db2a31ec9305904106467510c6374224350efd06a0b7001e6b", "size_in_bytes": 449293}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sr.pak", "path_type": "hardlink", "sha256": "c228d9a56d6a39a6c89a2218eb21c2c8956332cfab566cbb4e8a6655397a1f48", "sha256_in_prefix": "c228d9a56d6a39a6c89a2218eb21c2c8956332cfab566cbb4e8a6655397a1f48", "size_in_bytes": 693989}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sv.pak", "path_type": "hardlink", "sha256": "4c640213c3f35d78b7d481342f8ea72dcb9c66f94a9fd8c913e202faf3cc2007", "sha256_in_prefix": "4c640213c3f35d78b7d481342f8ea72dcb9c66f94a9fd8c913e202faf3cc2007", "size_in_bytes": 404856}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sw.pak", "path_type": "hardlink", "sha256": "83c5b9b704552ac0d270ca5b65a6c99772cc568af0823ebc644a7a1f823bc0d5", "sha256_in_prefix": "83c5b9b704552ac0d270ca5b65a6c99772cc568af0823ebc644a7a1f823bc0d5", "size_in_bytes": 425432}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ta.pak", "path_type": "hardlink", "sha256": "c401cc3043d3cd0d7540d7e44ea15af65e37572b3c6560654e4b5c937b25f088", "sha256_in_prefix": "c401cc3043d3cd0d7540d7e44ea15af65e37572b3c6560654e4b5c937b25f088", "size_in_bytes": 1099244}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/te.pak", "path_type": "hardlink", "sha256": "6ca6c32b0581bfb9d921ca671ac6b2f54a0c02583dacf2495d7149df4a120240", "sha256_in_prefix": "6ca6c32b0581bfb9d921ca671ac6b2f54a0c02583dacf2495d7149df4a120240", "size_in_bytes": 1016537}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/th.pak", "path_type": "hardlink", "sha256": "ad93df60735c23e74cbf895e3057c2daa9ff27fd905cc8c77f291d1e73e2dd5b", "sha256_in_prefix": "ad93df60735c23e74cbf895e3057c2daa9ff27fd905cc8c77f291d1e73e2dd5b", "size_in_bytes": 854668}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/tr.pak", "path_type": "hardlink", "sha256": "6865aeef283465b607bde6474f19a4eb7527764746fb910aa7a43458d0fc8a9e", "sha256_in_prefix": "6865aeef283465b607bde6474f19a4eb7527764746fb910aa7a43458d0fc8a9e", "size_in_bytes": 432994}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/uk.pak", "path_type": "hardlink", "sha256": "f326d9384fa0bd27f25f44bb1552f62abb74e281126123ca44e1d6719f50abdf", "sha256_in_prefix": "f326d9384fa0bd27f25f44bb1552f62abb74e281126123ca44e1d6719f50abdf", "size_in_bytes": 741281}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/vi.pak", "path_type": "hardlink", "sha256": "829cbc199352e79b5d43d21d1621e55234c3c1f28e8f3876856e46a638b0e4b2", "sha256_in_prefix": "829cbc199352e79b5d43d21d1621e55234c3c1f28e8f3876856e46a638b0e4b2", "size_in_bytes": 515158}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/zh-CN.pak", "path_type": "hardlink", "sha256": "4a758974e8668f4d9a3d2ab9cdece30a422852757ab29b545eaaed097eb110c0", "sha256_in_prefix": "4a758974e8668f4d9a3d2ab9cdece30a422852757ab29b545eaaed097eb110c0", "size_in_bytes": 372751}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/zh-TW.pak", "path_type": "hardlink", "sha256": "83f99e4e4433ab0a54ddb8df380f10fd0ef915ba7beb9bb4189cd6ef43c0fb0c", "sha256_in_prefix": "83f99e4e4433ab0a54ddb8df380f10fd0ef915ba7beb9bb4189cd6ef43c0fb0c", "size_in_bytes": 369142}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/ucrtbase.dll", "path_type": "hardlink", "sha256": "2ab2a74bcb5bfd8248d232eb3bc56698fb5173b9ff7fc0daf87d8120d0f448d7", "sha256_in_prefix": "2ab2a74bcb5bfd8248d232eb3bc56698fb5173b9ff7fc0daf87d8120d0f448d7", "size_in_bytes": 982720}, {"_path": "Library/nsight-compute/2025.1.1/host/windows-desktop-win7-x64/zlib.dll", "path_type": "hardlink", "sha256": "a630a668ca13747c9dc5e84024d3cdb802d2af27757c39b91b257e605a867ca2", "sha256_in_prefix": "a630a668ca13747c9dc5e84024d3cdb802d2af27757c39b91b257e605a867ca2", "size_in_bytes": 98376}, {"_path": "Library/nsight-compute/2025.1.1/ncu-ui.bat", "path_type": "hardlink", "sha256": "7bbcf82fa204c2a2613212ed12a96618aaba6403d009b3aef6b65bccbb2489e4", "sha256_in_prefix": "7bbcf82fa204c2a2613212ed12a96618aaba6403d009b3aef6b65bccbb2489e4", "size_in_bytes": 71}, {"_path": "Library/nsight-compute/2025.1.1/ncu.bat", "path_type": "hardlink", "sha256": "cbbe35bff8f28cf1dd8ea4ddc2f9097e475edea505b407f673609a3ff7627219", "sha256_in_prefix": "cbbe35bff8f28cf1dd8ea4ddc2f9097e475edea505b407f673609a3ff7627219", "size_in_bytes": 61}, {"_path": "Library/nsight-compute/2025.1.1/sections/AchievedOccupancy.py", "path_type": "hardlink", "sha256": "fb35eb2a707c61749543279058c7072311bf65935dba13f8115dabdec1d43dda", "sha256_in_prefix": "fb35eb2a707c61749543279058c7072311bf65935dba13f8115dabdec1d43dda", "size_in_bytes": 7710}, {"_path": "Library/nsight-compute/2025.1.1/sections/C2CLink.section", "path_type": "hardlink", "sha256": "c5238cb67b494e797beda6e29646e42c45a3a0f60dd525a61511408f7952500e", "sha256_in_prefix": "c5238cb67b494e797beda6e29646e42c45a3a0f60dd525a61511408f7952500e", "size_in_bytes": 968}, {"_path": "Library/nsight-compute/2025.1.1/sections/CPIStall.py", "path_type": "hardlink", "sha256": "011cc5ce693f554dd4683f1988e2c173b9e1f3d62bde897f904f85e3049fbda6", "sha256_in_prefix": "011cc5ce693f554dd4683f1988e2c173b9e1f3d62bde897f904f85e3049fbda6", "size_in_bytes": 21917}, {"_path": "Library/nsight-compute/2025.1.1/sections/Compute.py", "path_type": "hardlink", "sha256": "96fb41b7edec13ff20c15ff5a94e594c7d0c2a74ca176460f1cd4cf01f1b8679", "sha256_in_prefix": "96fb41b7edec13ff20c15ff5a94e594c7d0c2a74ca176460f1cd4cf01f1b8679", "size_in_bytes": 2113}, {"_path": "Library/nsight-compute/2025.1.1/sections/ComputeWorkloadAnalysis.section", "path_type": "hardlink", "sha256": "14f69ac85c6f181e5c7e0a84aee6221dfbfb183b4a3eb0a040650216193b8dea", "sha256_in_prefix": "14f69ac85c6f181e5c7e0a84aee6221dfbfb183b4a3eb0a040650216193b8dea", "size_in_bytes": 13177}, {"_path": "Library/nsight-compute/2025.1.1/sections/FPInstructions.py", "path_type": "hardlink", "sha256": "ebf921004501f4d9c29f248adce5621db34280fca509910db7a7d1f24c342ac4", "sha256_in_prefix": "ebf921004501f4d9c29f248adce5621db34280fca509910db7a7d1f24c342ac4", "size_in_bytes": 6846}, {"_path": "Library/nsight-compute/2025.1.1/sections/HighPipeUtilization.py", "path_type": "hardlink", "sha256": "01b83d4a0332e460b785d334ee494fe5398645932ba69a5201d87dfaa327ed2f", "sha256_in_prefix": "01b83d4a0332e460b785d334ee494fe5398645932ba69a5201d87dfaa327ed2f", "size_in_bytes": 24328}, {"_path": "Library/nsight-compute/2025.1.1/sections/InstructionStatistics.section", "path_type": "hardlink", "sha256": "2dad2e34555acc7fe11d674cb576bad757ab4bda501751912a6f7ef1aa958943", "sha256_in_prefix": "2dad2e34555acc7fe11d674cb576bad757ab4bda501751912a6f7ef1aa958943", "size_in_bytes": 2041}, {"_path": "Library/nsight-compute/2025.1.1/sections/IssueSlotUtilization.py", "path_type": "hardlink", "sha256": "966a9051a53120492df40a35de82a0e2929c23b2d40389ec3bb02136c3e8aa80", "sha256_in_prefix": "966a9051a53120492df40a35de82a0e2929c23b2d40389ec3bb02136c3e8aa80", "size_in_bytes": 6019}, {"_path": "Library/nsight-compute/2025.1.1/sections/LaunchStatistics.py", "path_type": "hardlink", "sha256": "8b79947e8d5417d60c0b972fa2098f0d9197605fe00611b8961c4f30d026c286", "sha256_in_prefix": "8b79947e8d5417d60c0b972fa2098f0d9197605fe00611b8961c4f30d026c286", "size_in_bytes": 19547}, {"_path": "Library/nsight-compute/2025.1.1/sections/LaunchStatistics.section", "path_type": "hardlink", "sha256": "5cd2f581d375d93050cd4c57a1f29f8709b555fe71913adb1125ab019896baed", "sha256_in_prefix": "5cd2f581d375d93050cd4c57a1f29f8709b555fe71913adb1125ab019896baed", "size_in_bytes": 4335}, {"_path": "Library/nsight-compute/2025.1.1/sections/Memory.py", "path_type": "hardlink", "sha256": "5a90eaf036626d89396ab64c3053a68e006fb4913ac02070d5cf56c2a06a57fc", "sha256_in_prefix": "5a90eaf036626d89396ab64c3053a68e006fb4913ac02070d5cf56c2a06a57fc", "size_in_bytes": 2583}, {"_path": "Library/nsight-compute/2025.1.1/sections/MemoryApertureUsage.py", "path_type": "hardlink", "sha256": "1262756dc178309e9b8f4ec1071a359f7e63bd03bb067723add83ce9c905cff4", "sha256_in_prefix": "1262756dc178309e9b8f4ec1071a359f7e63bd03bb067723add83ce9c905cff4", "size_in_bytes": 9140}, {"_path": "Library/nsight-compute/2025.1.1/sections/MemoryCacheAccessPattern.py", "path_type": "hardlink", "sha256": "3d6ad19913b0f7297293db45ee04f6426c8cabc482a919c5b8d3a54b805090cf", "sha256_in_prefix": "3d6ad19913b0f7297293db45ee04f6426c8cabc482a919c5b8d3a54b805090cf", "size_in_bytes": 17577}, {"_path": "Library/nsight-compute/2025.1.1/sections/MemoryL2Compression.py", "path_type": "hardlink", "sha256": "fbba402fc2f5e2b4bd8c6e3fac0b185870a302d452f92b1ca19fa324fc14a6d4", "sha256_in_prefix": "fbba402fc2f5e2b4bd8c6e3fac0b185870a302d452f92b1ca19fa324fc14a6d4", "size_in_bytes": 8487}, {"_path": "Library/nsight-compute/2025.1.1/sections/MemoryWorkloadAnalysis.section", "path_type": "hardlink", "sha256": "09a6ad3931b5a69aabc0102bd013538ba7f542a0d07fcb9f090b5c6bde308f57", "sha256_in_prefix": "09a6ad3931b5a69aabc0102bd013538ba7f542a0d07fcb9f090b5c6bde308f57", "size_in_bytes": 2844}, {"_path": "Library/nsight-compute/2025.1.1/sections/MemoryWorkloadAnalysis_Chart.section", "path_type": "hardlink", "sha256": "8e5292eda48f287cf74faa0d7d6e5c7e60394c8d29ed707f79504a87c0c08547", "sha256_in_prefix": "8e5292eda48f287cf74faa0d7d6e5c7e60394c8d29ed707f79504a87c0c08547", "size_in_bytes": 3381}, {"_path": "Library/nsight-compute/2025.1.1/sections/MemoryWorkloadAnalysis_Tables.section", "path_type": "hardlink", "sha256": "df366a2eb2f8e92a2bce794963e8d420594d5041c3d53d89ee36d3c8342f6ebe", "sha256_in_prefix": "df366a2eb2f8e92a2bce794963e8d420594d5041c3d53d89ee36d3c8342f6ebe", "size_in_bytes": 4285}, {"_path": "Library/nsight-compute/2025.1.1/sections/NumaAffinity.section", "path_type": "hardlink", "sha256": "bd528098fae348cec198ac9e60c9811f5020251057a5ce19750a5e3c35369678", "sha256_in_prefix": "bd528098fae348cec198ac9e60c9811f5020251057a5ce19750a5e3c35369678", "size_in_bytes": 1033}, {"_path": "Library/nsight-compute/2025.1.1/sections/NvRules.py", "path_type": "hardlink", "sha256": "863d81ae54d13d15424eb7d3d796196b96faa1dafa4de6909bd73d4e2835b480", "sha256_in_prefix": "863d81ae54d13d15424eb7d3d796196b96faa1dafa4de6909bd73d4e2835b480", "size_in_bytes": 155263}, {"_path": "Library/nsight-compute/2025.1.1/sections/Nvlink.section", "path_type": "hardlink", "sha256": "6f2c8ceb008dea1f959b9f8ebbcb46fb4288182c13e926596d9866dc71116b8f", "sha256_in_prefix": "6f2c8ceb008dea1f959b9f8ebbcb46fb4288182c13e926596d9866dc71116b8f", "size_in_bytes": 5147}, {"_path": "Library/nsight-compute/2025.1.1/sections/Nvlink_Tables.section", "path_type": "hardlink", "sha256": "36b3ffbc338078513657152cf7358a3ed7a215f2a33b49881940a97902217c12", "sha256_in_prefix": "36b3ffbc338078513657152cf7358a3ed7a215f2a33b49881940a97902217c12", "size_in_bytes": 451}, {"_path": "Library/nsight-compute/2025.1.1/sections/Nvlink_Topology.section", "path_type": "hardlink", "sha256": "54195e7412f8c649f3c53d2d65f7c557a2cd13870fcc52be8adf27c71df68c66", "sha256_in_prefix": "54195e7412f8c649f3c53d2d65f7c557a2cd13870fcc52be8adf27c71df68c66", "size_in_bytes": 410}, {"_path": "Library/nsight-compute/2025.1.1/sections/Occupancy.section", "path_type": "hardlink", "sha256": "e3d62ece3fdd23fe6f988ce45740383f97e67e576339f8f60f433b9d754547a3", "sha256_in_prefix": "e3d62ece3fdd23fe6f988ce45740383f97e67e576339f8f60f433b9d754547a3", "size_in_bytes": 11036}, {"_path": "Library/nsight-compute/2025.1.1/sections/PCSamplingData.py", "path_type": "hardlink", "sha256": "a49a3e5836cb63cf176c79a74bfa4980586bb45fddbebafb37180af54e257345", "sha256_in_prefix": "a49a3e5836cb63cf176c79a74bfa4980586bb45fddbebafb37180af54e257345", "size_in_bytes": 2916}, {"_path": "Library/nsight-compute/2025.1.1/sections/PMSamplingData.py", "path_type": "hardlink", "sha256": "f3d2bb75cfbf1376b35c8972e6a2fb4a75bf52da1c295dfddebe15de60d6c4c3", "sha256_in_prefix": "f3d2bb75cfbf1376b35c8972e6a2fb4a75bf52da1c295dfddebe15de60d6c4c3", "size_in_bytes": 4272}, {"_path": "Library/nsight-compute/2025.1.1/sections/PmSampling.section", "path_type": "hardlink", "sha256": "48c5b1637ba5694758fd65c0c8cd2efa19b3062cfd0e5615b94430da855d112f", "sha256_in_prefix": "48c5b1637ba5694758fd65c0c8cd2efa19b3062cfd0e5615b94430da855d112f", "size_in_bytes": 37541}, {"_path": "Library/nsight-compute/2025.1.1/sections/PmSampling_WarpStates.section", "path_type": "hardlink", "sha256": "2b3a79b2a1aac50580d148994026eb3aef95e6c7c5387381fd09973293f06807", "sha256_in_prefix": "2b3a79b2a1aac50580d148994026eb3aef95e6c7c5387381fd09973293f06807", "size_in_bytes": 8373}, {"_path": "Library/nsight-compute/2025.1.1/sections/RequestedMetrics.py", "path_type": "hardlink", "sha256": "7fc365f08512410960751d89692baa5aa5e55018b5906076a565b7956e669dbf", "sha256_in_prefix": "7fc365f08512410960751d89692baa5aa5e55018b5906076a565b7956e669dbf", "size_in_bytes": 9581}, {"_path": "Library/nsight-compute/2025.1.1/sections/SchedulerStatistics.section", "path_type": "hardlink", "sha256": "3c78d2bc0f22b0780f15402b8241f275c5d106dc4a38307368537acd7cf81e89", "sha256_in_prefix": "3c78d2bc0f22b0780f15402b8241f275c5d106dc4a38307368537acd7cf81e89", "size_in_bytes": 2396}, {"_path": "Library/nsight-compute/2025.1.1/sections/SharedMemoryConflicts.py", "path_type": "hardlink", "sha256": "9b529764c3f6ef437d1b02335578da20e67d358d1c92512ce48dc26b68af29db", "sha256_in_prefix": "9b529764c3f6ef437d1b02335578da20e67d358d1c92512ce48dc26b68af29db", "size_in_bytes": 6362}, {"_path": "Library/nsight-compute/2025.1.1/sections/SlowPipeLimiter.py", "path_type": "hardlink", "sha256": "b64c5161d76c8173e02a7b1c087f98eff4faed3c8222fd1b2d7fd32b10c381c0", "sha256_in_prefix": "b64c5161d76c8173e02a7b1c087f98eff4faed3c8222fd1b2d7fd32b10c381c0", "size_in_bytes": 5289}, {"_path": "Library/nsight-compute/2025.1.1/sections/SourceCounters.section", "path_type": "hardlink", "sha256": "8f47025b12964cece782889b46bca2113d568d1758729f9341e41ae035e16460", "sha256_in_prefix": "8f47025b12964cece782889b46bca2113d568d1758729f9341e41ae035e16460", "size_in_bytes": 15153}, {"_path": "Library/nsight-compute/2025.1.1/sections/SpeedOfLight.py", "path_type": "hardlink", "sha256": "8fdf97bfa8d8f3a049d1c2cee94318622ad5364c69a253638a0347258ffd299b", "sha256_in_prefix": "8fdf97bfa8d8f3a049d1c2cee94318622ad5364c69a253638a0347258ffd299b", "size_in_bytes": 9652}, {"_path": "Library/nsight-compute/2025.1.1/sections/SpeedOfLight.section", "path_type": "hardlink", "sha256": "fa450051339de3471cbd6d055accc71ecf70224641b353ff74bd140811119f7e", "sha256_in_prefix": "fa450051339de3471cbd6d055accc71ecf70224641b353ff74bd140811119f7e", "size_in_bytes": 4180}, {"_path": "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_HierarchicalDoubleRooflineChart.section", "path_type": "hardlink", "sha256": "882dcbe02551fa17ab489a508fedc42e4df84e77719c65d507f53f1a4d2e23b5", "sha256_in_prefix": "882dcbe02551fa17ab489a508fedc42e4df84e77719c65d507f53f1a4d2e23b5", "size_in_bytes": 13903}, {"_path": "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_HierarchicalHalfRooflineChart.section", "path_type": "hardlink", "sha256": "405c32069f29f0d9d29fbd403c87da210d6724a0cfe2e8e14145fdd2de1aaf42", "sha256_in_prefix": "405c32069f29f0d9d29fbd403c87da210d6724a0cfe2e8e14145fdd2de1aaf42", "size_in_bytes": 14223}, {"_path": "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_HierarchicalSingleRooflineChart.section", "path_type": "hardlink", "sha256": "0df18b4b020cd0ec0ae0cc131809581483a2da1e311335e97b630573cf5ada59", "sha256_in_prefix": "0df18b4b020cd0ec0ae0cc131809581483a2da1e311335e97b630573cf5ada59", "size_in_bytes": 13903}, {"_path": "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_HierarchicalTensorRooflineChart.section", "path_type": "hardlink", "sha256": "6e74fa7aefed95d8b3003b27a2c976df973857a52751fdf5c37215c0b83a9d45", "sha256_in_prefix": "6e74fa7aefed95d8b3003b27a2c976df973857a52751fdf5c37215c0b83a9d45", "size_in_bytes": 1132366}, {"_path": "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_Roofline.py", "path_type": "hardlink", "sha256": "d94bd3d8324f903a68691b2b8fb2434936ebea61fc1bab93a64ddfc73365d3aa", "sha256_in_prefix": "d94bd3d8324f903a68691b2b8fb2434936ebea61fc1bab93a64ddfc73365d3aa", "size_in_bytes": 7577}, {"_path": "Library/nsight-compute/2025.1.1/sections/SpeedOfLight_RooflineChart.section", "path_type": "hardlink", "sha256": "401dd41620182d24d78f4d1381d0afe61a15264317cf6bc995432476b2e951e5", "sha256_in_prefix": "401dd41620182d24d78f4d1381d0afe61a15264317cf6bc995432476b2e951e5", "size_in_bytes": 10587}, {"_path": "Library/nsight-compute/2025.1.1/sections/TheoreticalOccupancy.py", "path_type": "hardlink", "sha256": "ad3aed8537252289e443241951a004a350693a6d03c133ed6be1a0f2795b089e", "sha256_in_prefix": "ad3aed8537252289e443241951a004a350693a6d03c133ed6be1a0f2795b089e", "size_in_bytes": 8672}, {"_path": "Library/nsight-compute/2025.1.1/sections/ThreadDivergence.py", "path_type": "hardlink", "sha256": "1be17e191334cd0eacfcc0abc7fc4f0caa9a22b90ce56b1483547578aff55af0", "sha256_in_prefix": "1be17e191334cd0eacfcc0abc7fc4f0caa9a22b90ce56b1483547578aff55af0", "size_in_bytes": 5076}, {"_path": "Library/nsight-compute/2025.1.1/sections/UncoalescedAccess.chart", "path_type": "hardlink", "sha256": "93beab947b9f54ff613a9f739c6862e6c15d01f5acf1fe974af19f321aa1aa40", "sha256_in_prefix": "93beab947b9f54ff613a9f739c6862e6c15d01f5acf1fe974af19f321aa1aa40", "size_in_bytes": 161}, {"_path": "Library/nsight-compute/2025.1.1/sections/UncoalescedAccess.py", "path_type": "hardlink", "sha256": "57095bd94c7367fd4d1877cce12d09cf18919315ef12fdf4f362c76de5a01b6c", "sha256_in_prefix": "57095bd94c7367fd4d1877cce12d09cf18919315ef12fdf4f362c76de5a01b6c", "size_in_bytes": 7490}, {"_path": "Library/nsight-compute/2025.1.1/sections/UncoalescedSharedAccess.chart", "path_type": "hardlink", "sha256": "05cdaf18c975f32a5dbc482393970eb499c6968909da14f8a5ae851073faf99c", "sha256_in_prefix": "05cdaf18c975f32a5dbc482393970eb499c6968909da14f8a5ae851073faf99c", "size_in_bytes": 143}, {"_path": "Library/nsight-compute/2025.1.1/sections/UncoalescedSharedAccess.py", "path_type": "hardlink", "sha256": "fe4bdaa9b063e8f2254e6667607d9e0826d6ab5e7737b9db731bf2095e93aa67", "sha256_in_prefix": "fe4bdaa9b063e8f2254e6667607d9e0826d6ab5e7737b9db731bf2095e93aa67", "size_in_bytes": 7826}, {"_path": "Library/nsight-compute/2025.1.1/sections/WarpStateStatistics.section", "path_type": "hardlink", "sha256": "11f36d341adec5d0ecbb6e5255c98cff9c0618e5301a6f1bf6811e84797fb358", "sha256_in_prefix": "11f36d341adec5d0ecbb6e5255c98cff9c0618e5301a6f1bf6811e84797fb358", "size_in_bytes": 4752}, {"_path": "Library/nsight-compute/2025.1.1/sections/WorkloadDistribution.section", "path_type": "hardlink", "sha256": "4579e9544967cec4ecc49ee1bbddc84fed88395b68b3d8d3dabab05b39f88468", "sha256_in_prefix": "4579e9544967cec4ecc49ee1bbddc84fed88395b68b3d8d3dabab05b39f88468", "size_in_bytes": 5150}, {"_path": "Library/nsight-compute/2025.1.1/sections/WorkloadImbalance.py", "path_type": "hardlink", "sha256": "90899f958d8e8bbfef96e8075ba00b3ec8aa03f12a234d32656e6a0f1f7e6e09", "sha256_in_prefix": "90899f958d8e8bbfef96e8075ba00b3ec8aa03f12a234d32656e6a0f1f7e6e09", "size_in_bytes": 8909}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/TreeLauncherSubreaper", "path_type": "hardlink", "sha256": "611303d7729cd8eb1fa4f95608fa96354d61ccc312e05c2b430dbcebe80fbeba", "sha256_in_prefix": "611303d7729cd8eb1fa4f95608fa96354d61ccc312e05c2b430dbcebe80fbeba", "size_in_bytes": 753144}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/TreeLauncherTargetLdPreloadHelper", "path_type": "hardlink", "sha256": "49b3cdad1e31bd664a3aa8c985b5b9bdefaace38068b507ef735221ecf284427", "sha256_in_prefix": "49b3cdad1e31bd664a3aa8c985b5b9bdefaace38068b507ef735221ecf284427", "size_in_bytes": 14336}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libInterceptorInjectionTarget.so", "path_type": "hardlink", "sha256": "0be65c5a16895087f66f1a4af83f21844447cf1b3975f94cdf819e62beec386c", "sha256_in_prefix": "0be65c5a16895087f66f1a4af83f21844447cf1b3975f94cdf819e62beec386c", "size_in_bytes": 876360}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherPlaceholder.so", "path_type": "hardlink", "sha256": "e86d0db97534ddc17115e52fed2b88fa0d5bc66072568a1187d1c19e5dda2c9a", "sha256_in_prefix": "e86d0db97534ddc17115e52fed2b88fa0d5bc66072568a1187d1c19e5dda2c9a", "size_in_bytes": 14000}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherTargetInjection.so", "path_type": "hardlink", "sha256": "1ab5dea7c281457b32136ee13cb8fc5949272588921e5d0a1c3322fcc43eb8d5", "sha256_in_prefix": "1ab5dea7c281457b32136ee13cb8fc5949272588921e5d0a1c3322fcc43eb8d5", "size_in_bytes": 3021640}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherTargetUpdatePreloadInjection.so", "path_type": "hardlink", "sha256": "aac8b2c7993b5929d9423bc59f502d3c4e56e39fa2d1ba521557a3f8dc55608d", "sha256_in_prefix": "aac8b2c7993b5929d9423bc59f502d3c4e56e39fa2d1ba521557a3f8dc55608d", "size_in_bytes": 1342880}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libcuda-injection.so", "path_type": "hardlink", "sha256": "70ee0b9ab3e52d12c678e2e70a12cd16a7bdd3e985ea2cb443ef8593ee6f5f3d", "sha256_in_prefix": "70ee0b9ab3e52d12c678e2e70a12cd16a7bdd3e985ea2cb443ef8593ee6f5f3d", "size_in_bytes": 13991912}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libnvperf_host.so", "path_type": "hardlink", "sha256": "041c842e77745d42ae86f464fa10f0cce5651b755d8dbad9bc0e1d0afa354d46", "sha256_in_prefix": "041c842e77745d42ae86f464fa10f0cce5651b755d8dbad9bc0e1d0afa354d46", "size_in_bytes": 25924240}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/libnvperf_target.so", "path_type": "hardlink", "sha256": "cc4159c8fa56a742833825ecabd33461e79a05c70357dd626f7b662a0d69b1cf", "sha256_in_prefix": "cc4159c8fa56a742833825ecabd33461e79a05c70357dd626f7b662a0d69b1cf", "size_in_bytes": 5361328}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x64/ncu", "path_type": "hardlink", "sha256": "0bd1ff6b9f2b456b5b6ca6613fc79a9f6fab4e5bdd20b7fed110064305bd7ac5", "sha256_in_prefix": "0bd1ff6b9f2b456b5b6ca6613fc79a9f6fab4e5bdd20b7fed110064305bd7ac5", "size_in_bytes": 62706344}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/TreeLauncherTargetLdPreloadHelper", "path_type": "hardlink", "sha256": "35c6740198024910230154b96ba4d28afc6397b789dba4ec89631189f5eb92b2", "sha256_in_prefix": "35c6740198024910230154b96ba4d28afc6397b789dba4ec89631189f5eb92b2", "size_in_bytes": 13696}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/libInterceptorInjectionTarget.so", "path_type": "hardlink", "sha256": "3118f12aac55109b7f9283f2eec124689f79b88161c1d9c0d02f33b288ba7f2a", "sha256_in_prefix": "3118f12aac55109b7f9283f2eec124689f79b88161c1d9c0d02f33b288ba7f2a", "size_in_bytes": 919828}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherPlaceholder.so", "path_type": "hardlink", "sha256": "39618c7ff5c62f5bc7ee88b384ae92545ba70760f6fbaebf0432abb2cfeca198", "sha256_in_prefix": "39618c7ff5c62f5bc7ee88b384ae92545ba70760f6fbaebf0432abb2cfeca198", "size_in_bytes": 13432}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherTargetInjection.so", "path_type": "hardlink", "sha256": "0a88fe91e316b141a932ce0256658e5cc79a2524ed5045397ad75573420c9e18", "sha256_in_prefix": "0a88fe91e316b141a932ce0256658e5cc79a2524ed5045397ad75573420c9e18", "size_in_bytes": 3223552}, {"_path": "Library/nsight-compute/2025.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherTargetUpdatePreloadInjection.so", "path_type": "hardlink", "sha256": "37a146ceacc7ee58707267005b12cf8f1645d75f7851e811e6c5305316c2646f", "sha256_in_prefix": "37a146ceacc7ee58707267005b12cf8f1645d75f7851e811e6c5305316c2646f", "size_in_bytes": 1423488}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/InterceptorInjectionTarget.dll", "path_type": "hardlink", "sha256": "c6ca9a8d447bc771ec8fceb1149afc3792c8af5032f810464179b43e6ec33398", "sha256_in_prefix": "c6ca9a8d447bc771ec8fceb1149afc3792c8af5032f810464179b43e6ec33398", "size_in_bytes": 1119824}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/TreeLauncherTargetInjection.dll", "path_type": "hardlink", "sha256": "622de236807211f9f8a3460e2fd566ef6a87d38485f31ce40486bce57f5f5fab", "sha256_in_prefix": "622de236807211f9f8a3460e2fd566ef6a87d38485f31ce40486bce57f5f5fab", "size_in_bytes": 1616488}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/cuda-injection.dll", "path_type": "hardlink", "sha256": "4318072b617f784b5ad2056456629a9492bfaee7a7aa89e864830211a8890f6c", "sha256_in_prefix": "4318072b617f784b5ad2056456629a9492bfaee7a7aa89e864830211a8890f6c", "size_in_bytes": 10325096}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/icudt71.dll", "path_type": "hardlink", "sha256": "8a36ae98632e55c8549302794de8cc1b0846c5dd781834f933eeeb68c802780f", "sha256_in_prefix": "8a36ae98632e55c8549302794de8cc1b0846c5dd781834f933eeeb68c802780f", "size_in_bytes": 30431824}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/icuuc71.dll", "path_type": "hardlink", "sha256": "d288dc19e9c813c8eb547853eb1adfccbdd442ecfb89dee3f6ef60fa2015b73d", "sha256_in_prefix": "d288dc19e9c813c8eb547853eb1adfccbdd442ecfb89dee3f6ef60fa2015b73d", "size_in_bytes": 2263144}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/ncu.exe", "path_type": "hardlink", "sha256": "35880e6c0c041543f562f9d97a71ebcb12f32b9ca294536119c881a655a5b814", "sha256_in_prefix": "35880e6c0c041543f562f9d97a71ebcb12f32b9ca294536119c881a655a5b814", "size_in_bytes": 26621512}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/nvperf_host.dll", "path_type": "hardlink", "sha256": "0897de0b78f91370715c40d8e8f5b1d2e28fd557fd193c2f6080e4cc0b1cef50", "sha256_in_prefix": "0897de0b78f91370715c40d8e8f5b1d2e28fd557fd193c2f6080e4cc0b1cef50", "size_in_bytes": 21642344}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x64/nvperf_target.dll", "path_type": "hardlink", "sha256": "6c2ffdeeaa89c571d3c76d051e46322a1ac65f53066c6f56398e65ff04c8c2e8", "sha256_in_prefix": "6c2ffdeeaa89c571d3c76d051e46322a1ac65f53066c6f56398e65ff04c8c2e8", "size_in_bytes": 2758736}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x86/InterceptorInjectionTarget.dll", "path_type": "hardlink", "sha256": "224cad8553749f1671da117c6513ccb313b74ed17dbacb4dd408d547b2b00dc4", "sha256_in_prefix": "224cad8553749f1671da117c6513ccb313b74ed17dbacb4dd408d547b2b00dc4", "size_in_bytes": 921168}, {"_path": "Library/nsight-compute/2025.1.1/target/windows-desktop-win7-x86/TreeLauncherTargetInjection.dll", "path_type": "hardlink", "sha256": "6c2ef645986c68616627dcdc0e401a3eca5cecda07e819f53931bb39acc5f0c4", "sha256_in_prefix": "6c2ef645986c68616627dcdc0e401a3eca5cecda07e819f53931bb39acc5f0c4", "size_in_bytes": 1401960}, {"_path": "Scripts/CrashReporter.bat", "path_type": "hardlink", "sha256": "f8a1e7a877bf8a2d0f4a953ab516d5aba3ef7de22c46cab3ce97982374a06022", "sha256_in_prefix": "f8a1e7a877bf8a2d0f4a953ab516d5aba3ef7de22c46cab3ce97982374a06022", "size_in_bytes": 75}, {"_path": "Scripts/CudaGpuInfoDumper.bat", "path_type": "hardlink", "sha256": "8f1fa3f9b6fc89a220dcb1bf625ee9d40deb30dbe1c7dc1af59de340fd0629fa", "sha256_in_prefix": "8f1fa3f9b6fc89a220dcb1bf625ee9d40deb30dbe1c7dc1af59de340fd0629fa", "size_in_bytes": 79}, {"_path": "Scripts/QdstrmImporter.bat", "path_type": "hardlink", "sha256": "fcb75099902da7bfc750627b838d50dd06eaf0ebea3a25ca735d5cac726a3948", "sha256_in_prefix": "fcb75099902da7bfc750627b838d50dd06eaf0ebea3a25ca735d5cac726a3948", "size_in_bytes": 76}, {"_path": "Scripts/QtWebEngineProcess.bat", "path_type": "hardlink", "sha256": "3604a07c692860a677962240f8d520723db9591c60fd6942d611f50e5550cb53", "sha256_in_prefix": "3604a07c692860a677962240f8d520723db9591c60fd6942d611f50e5550cb53", "size_in_bytes": 80}, {"_path": "Scripts/ncu-ui.bat", "path_type": "hardlink", "sha256": "c195e85a6040299cf628757799a6e655bd0a1fb2757dd94dcf9c345fc38e03f0", "sha256_in_prefix": "c195e85a6040299cf628757799a6e655bd0a1fb2757dd94dcf9c345fc38e03f0", "size_in_bytes": 68}, {"_path": "Scripts/ncu.bat", "path_type": "hardlink", "sha256": "abc2eeb336d5fea0951cdb172c0400d9863fe2b20a645bd2634e21754e114226", "sha256_in_prefix": "abc2eeb336d5fea0951cdb172c0400d9863fe2b20a645bd2634e21754e114226", "size_in_bytes": 65}, {"_path": "Scripts/nsight-sys-service.bat", "path_type": "hardlink", "sha256": "420662a0818189590eacb986b18a1ee40b4f4e4267b44cc90949820c03fcd961", "sha256_in_prefix": "420662a0818189590eacb986b18a1ee40b4f4e4267b44cc90949820c03fcd961", "size_in_bytes": 80}, {"_path": "Scripts/nsys.bat", "path_type": "hardlink", "sha256": "c000628a16d122afe74e2f90a8263409880babfefab7db20739ef85847ed80e5", "sha256_in_prefix": "c000628a16d122afe74e2f90a8263409880babfefab7db20739ef85847ed80e5", "size_in_bytes": 66}, {"_path": "Scripts/python.bat", "path_type": "hardlink", "sha256": "113fbf07a58ed0a9aab70a76fc8db5a50309cd9d77fd1e2990f3ed673c8ca295", "sha256_in_prefix": "113fbf07a58ed0a9aab70a76fc8db5a50309cd9d77fd1e2990f3ed673c8ca295", "size_in_bytes": 68}, {"_path": "Scripts/sqlite3.bat", "path_type": "hardlink", "sha256": "6829387bb355b91d9753647c932b55f892a5297c327e354e17172d7927276a46", "sha256_in_prefix": "6829387bb355b91d9753647c932b55f892a5297c327e354e17172d7927276a46", "size_in_bytes": 69}], "paths_version": 1}, "requested_spec": "None", "sha256": "085890678a6f97314fa0d47621a38197492f97146e8bf37171d046089861614b", "size": 276809940, "subdir": "win-64", "timestamp": 1739386802000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/nsight-compute-2025.1.1.2-0.conda", "version": "2025.1.1.2"}