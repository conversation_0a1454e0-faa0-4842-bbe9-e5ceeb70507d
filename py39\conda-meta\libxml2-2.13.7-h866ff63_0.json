{"build": "h866ff63_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0", "zlib >=1.2.13,<1.3.0a0", "libiconv >=1.16,<2.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libxml2-2.13.7-h866ff63_0", "files": ["Library/bin/libxml2.dll", "Library/bin/runxmlconf.exe", "Library/bin/xmlcatalog.exe", "Library/bin/xmllint.exe", "Library/include/libxml2/libxml/HTMLparser.h", "Library/include/libxml2/libxml/HTMLtree.h", "Library/include/libxml2/libxml/SAX.h", "Library/include/libxml2/libxml/SAX2.h", "Library/include/libxml2/libxml/c14n.h", "Library/include/libxml2/libxml/catalog.h", "Library/include/libxml2/libxml/chvalid.h", "Library/include/libxml2/libxml/debugXML.h", "Library/include/libxml2/libxml/dict.h", "Library/include/libxml2/libxml/encoding.h", "Library/include/libxml2/libxml/entities.h", "Library/include/libxml2/libxml/globals.h", "Library/include/libxml2/libxml/hash.h", "Library/include/libxml2/libxml/list.h", "Library/include/libxml2/libxml/nanoftp.h", "Library/include/libxml2/libxml/nanohttp.h", "Library/include/libxml2/libxml/parser.h", "Library/include/libxml2/libxml/parserInternals.h", "Library/include/libxml2/libxml/pattern.h", "Library/include/libxml2/libxml/relaxng.h", "Library/include/libxml2/libxml/schemasInternals.h", "Library/include/libxml2/libxml/schematron.h", "Library/include/libxml2/libxml/threads.h", "Library/include/libxml2/libxml/tree.h", "Library/include/libxml2/libxml/uri.h", "Library/include/libxml2/libxml/valid.h", "Library/include/libxml2/libxml/xinclude.h", "Library/include/libxml2/libxml/xlink.h", "Library/include/libxml2/libxml/xmlIO.h", "Library/include/libxml2/libxml/xmlautomata.h", "Library/include/libxml2/libxml/xmlerror.h", "Library/include/libxml2/libxml/xmlexports.h", "Library/include/libxml2/libxml/xmlmemory.h", "Library/include/libxml2/libxml/xmlmodule.h", "Library/include/libxml2/libxml/xmlreader.h", "Library/include/libxml2/libxml/xmlregexp.h", "Library/include/libxml2/libxml/xmlsave.h", "Library/include/libxml2/libxml/xmlschemas.h", "Library/include/libxml2/libxml/xmlschemastypes.h", "Library/include/libxml2/libxml/xmlstring.h", "Library/include/libxml2/libxml/xmlunicode.h", "Library/include/libxml2/libxml/xmlversion.h", "Library/include/libxml2/libxml/xmlwriter.h", "Library/include/libxml2/libxml/xpath.h", "Library/include/libxml2/libxml/xpathInternals.h", "Library/include/libxml2/libxml/xpointer.h", "Library/lib/libxml2.lib", "Library/lib/libxml2_a.lib", "Library/lib/libxml2_a_dll.lib", "Library/lib/pkgconfig/libxml-2.0.pc"], "fn": "libxml2-2.13.7-h866ff63_0.conda", "license": "MIT", "link": {"source": "D:\\anaconda3\\pkgs\\libxml2-2.13.7-h866ff63_0", "type": 1}, "md5": "c9bc0fbf27c660f8c34424991dc93c3f", "name": "libxml2", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libxml2-2.13.7-h866ff63_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/libxml2.dll", "path_type": "hardlink", "sha256": "9add3ea6a54dcb2ed27ada7c26b98c3d7b2bb1ee1bc52b3e3336b42e7488301b", "sha256_in_prefix": "9add3ea6a54dcb2ed27ada7c26b98c3d7b2bb1ee1bc52b3e3336b42e7488301b", "size_in_bytes": 1324816}, {"_path": "Library/bin/runxmlconf.exe", "path_type": "hardlink", "sha256": "133e310d927f45aab900bb5354198e8dea0e99c0018a74700be3a3b56d7f94df", "sha256_in_prefix": "133e310d927f45aab900bb5354198e8dea0e99c0018a74700be3a3b56d7f94df", "size_in_bytes": 30480}, {"_path": "Library/bin/xmlcatalog.exe", "path_type": "hardlink", "sha256": "a26b0775efa9e0cdbef95b995707a943e853a557c5d7fae0a7cf4e115aa2c49c", "sha256_in_prefix": "a26b0775efa9e0cdbef95b995707a943e853a557c5d7fae0a7cf4e115aa2c49c", "size_in_bytes": 30992}, {"_path": "Library/bin/xmllint.exe", "path_type": "hardlink", "sha256": "82f952af754e27e62f87ee5b9d2ac34c11fa9274e9252d0098545aefaaa8bcfc", "sha256_in_prefix": "82f952af754e27e62f87ee5b9d2ac34c11fa9274e9252d0098545aefaaa8bcfc", "size_in_bytes": 67856}, {"_path": "Library/include/libxml2/libxml/HTMLparser.h", "path_type": "hardlink", "sha256": "6d334194ad458aeceaadbbd445c6bc31bf255a5c5776ff45905371dec4c89752", "sha256_in_prefix": "6d334194ad458aeceaadbbd445c6bc31bf255a5c5776ff45905371dec4c89752", "size_in_bytes": 9849}, {"_path": "Library/include/libxml2/libxml/HTMLtree.h", "path_type": "hardlink", "sha256": "c5031e1130ed63c5d99e457dac6908e14814229ef5238cf63b747f3f5004c1ce", "sha256_in_prefix": "c5031e1130ed63c5d99e457dac6908e14814229ef5238cf63b747f3f5004c1ce", "size_in_bytes": 3502}, {"_path": "Library/include/libxml2/libxml/SAX.h", "path_type": "hardlink", "sha256": "4c6da9908227100d6f2db31f841d369998584a84cac57fa6d116941a6632cc2a", "sha256_in_prefix": "4c6da9908227100d6f2db31f841d369998584a84cac57fa6d116941a6632cc2a", "size_in_bytes": 4418}, {"_path": "Library/include/libxml2/libxml/SAX2.h", "path_type": "hardlink", "sha256": "e5f88dbeb426920233819264fb7f0fd6dc5fb3e8c8bc070bfbf843004da9405b", "sha256_in_prefix": "e5f88dbeb426920233819264fb7f0fd6dc5fb3e8c8bc070bfbf843004da9405b", "size_in_bytes": 4430}, {"_path": "Library/include/libxml2/libxml/c14n.h", "path_type": "hardlink", "sha256": "052057c3a9c866eb42f265af6d1acb2e6a168f0df046dfa733ddef8d718c0a6f", "sha256_in_prefix": "052057c3a9c866eb42f265af6d1acb2e6a168f0df046dfa733ddef8d718c0a6f", "size_in_bytes": 2742}, {"_path": "Library/include/libxml2/libxml/catalog.h", "path_type": "hardlink", "sha256": "fda4efa8f96f5bd592c06af452c52be741f7c6f2cadca8c84fb344684f0be16c", "sha256_in_prefix": "fda4efa8f96f5bd592c06af452c52be741f7c6f2cadca8c84fb344684f0be16c", "size_in_bytes": 4618}, {"_path": "Library/include/libxml2/libxml/chvalid.h", "path_type": "hardlink", "sha256": "4d971c78da7a0b0d10958c08a8af46c7262a2f9522023a50ca3b7fcab6464d01", "sha256_in_prefix": "4d971c78da7a0b0d10958c08a8af46c7262a2f9522023a50ca3b7fcab6464d01", "size_in_bytes": 5087}, {"_path": "Library/include/libxml2/libxml/debugXML.h", "path_type": "hardlink", "sha256": "8a99e6670290f39e2d23b5e0d653098af0aed35d3599d5776bb33327a5d3c2bd", "sha256_in_prefix": "8a99e6670290f39e2d23b5e0d653098af0aed35d3599d5776bb33327a5d3c2bd", "size_in_bytes": 4934}, {"_path": "Library/include/libxml2/libxml/dict.h", "path_type": "hardlink", "sha256": "4b079a3c632d4d37f88dee9d35322813371f12fa6c013f7f3e147b142d0afab4", "sha256_in_prefix": "4b079a3c632d4d37f88dee9d35322813371f12fa6c013f7f3e147b142d0afab4", "size_in_bytes": 1770}, {"_path": "Library/include/libxml2/libxml/encoding.h", "path_type": "hardlink", "sha256": "e8851672beeb7c2bf6e1aaed733d9a42cdab1a0c8b5a51bd403288a09aa55fc1", "sha256_in_prefix": "e8851672beeb7c2bf6e1aaed733d9a42cdab1a0c8b5a51bd403288a09aa55fc1", "size_in_bytes": 8369}, {"_path": "Library/include/libxml2/libxml/entities.h", "path_type": "hardlink", "sha256": "78ed3ea4551ed62d93a1baeaad5e084357610effab34ed2d87906f3751fa73c6", "sha256_in_prefix": "78ed3ea4551ed62d93a1baeaad5e084357610effab34ed2d87906f3751fa73c6", "size_in_bytes": 4907}, {"_path": "Library/include/libxml2/libxml/globals.h", "path_type": "hardlink", "sha256": "182a79232ec83ab7b4302d456c54dda6276249e5454f87c0dadd0569c6e2c344", "sha256_in_prefix": "182a79232ec83ab7b4302d456c54dda6276249e5454f87c0dadd0569c6e2c344", "size_in_bytes": 890}, {"_path": "Library/include/libxml2/libxml/hash.h", "path_type": "hardlink", "sha256": "2882290182817c6514df27568467a15327ee6ae673fcfb348326a66f3428feb7", "sha256_in_prefix": "2882290182817c6514df27568467a15327ee6ae673fcfb348326a66f3428feb7", "size_in_bytes": 7017}, {"_path": "Library/include/libxml2/libxml/list.h", "path_type": "hardlink", "sha256": "a21ee224d41a8d103f707b0d93d09c14f624696dac99fe09fcca5e74f3e30b89", "sha256_in_prefix": "a21ee224d41a8d103f707b0d93d09c14f624696dac99fe09fcca5e74f3e30b89", "size_in_bytes": 3128}, {"_path": "Library/include/libxml2/libxml/nanoftp.h", "path_type": "hardlink", "sha256": "abe3869afde5b534d36c1fe7e9e8691852694bac30304fa93539317188472afa", "sha256_in_prefix": "abe3869afde5b534d36c1fe7e9e8691852694bac30304fa93539317188472afa", "size_in_bytes": 4013}, {"_path": "Library/include/libxml2/libxml/nanohttp.h", "path_type": "hardlink", "sha256": "6cb6f362303200a98fdc2a2630e3c7e97694226bba6cd004485d47ad5b51bded", "sha256_in_prefix": "6cb6f362303200a98fdc2a2630e3c7e97694226bba6cd004485d47ad5b51bded", "size_in_bytes": 2124}, {"_path": "Library/include/libxml2/libxml/parser.h", "path_type": "hardlink", "sha256": "c54589d22aff8825c0628247be38e606048b733a6a0366f3aa014ff01baf75d3", "sha256_in_prefix": "c54589d22aff8825c0628247be38e606048b733a6a0366f3aa014ff01baf75d3", "size_in_bytes": 44153}, {"_path": "Library/include/libxml2/libxml/parserInternals.h", "path_type": "hardlink", "sha256": "c3f51eba83663c400bf5e99dc555692f74648c7f20d25a46d158d8cb877923c7", "sha256_in_prefix": "c3f51eba83663c400bf5e99dc555692f74648c7f20d25a46d158d8cb877923c7", "size_in_bytes": 16810}, {"_path": "Library/include/libxml2/libxml/pattern.h", "path_type": "hardlink", "sha256": "5378a38f02d834a2ab64ce192a9347a71aadfe5ec1eccbf9c43d753e778ce770", "sha256_in_prefix": "5378a38f02d834a2ab64ce192a9347a71aadfe5ec1eccbf9c43d753e778ce770", "size_in_bytes": 2640}, {"_path": "Library/include/libxml2/libxml/relaxng.h", "path_type": "hardlink", "sha256": "9a2ca0046680c69c621b5ea4cc659b6a1f4f97ce13ba7cea28841ff467139211", "sha256_in_prefix": "9a2ca0046680c69c621b5ea4cc659b6a1f4f97ce13ba7cea28841ff467139211", "size_in_bytes": 5830}, {"_path": "Library/include/libxml2/libxml/schemasInternals.h", "path_type": "hardlink", "sha256": "57c338227df37f6e045f9e58b7875c7f1c29ecda471985622a72cac2dcab3c9e", "sha256_in_prefix": "57c338227df37f6e045f9e58b7875c7f1c29ecda471985622a72cac2dcab3c9e", "size_in_bytes": 26233}, {"_path": "Library/include/libxml2/libxml/schematron.h", "path_type": "hardlink", "sha256": "f0484f0e1bed94cc65f5ed02e6b49b12bb8ebf3252e410bf38e15babd4576676", "sha256_in_prefix": "f0484f0e1bed94cc65f5ed02e6b49b12bb8ebf3252e410bf38e15babd4576676", "size_in_bytes": 4255}, {"_path": "Library/include/libxml2/libxml/threads.h", "path_type": "hardlink", "sha256": "bd65effcdbdfedad6225564edb627b9c2519913c0cd69956ea52ffbfd59c91dd", "sha256_in_prefix": "bd65effcdbdfedad6225564edb627b9c2519913c0cd69956ea52ffbfd59c91dd", "size_in_bytes": 1730}, {"_path": "Library/include/libxml2/libxml/tree.h", "path_type": "hardlink", "sha256": "ad21354b7d69ba4d7874eb7b8c971ab27ed3c0f7c3fbf9fb1086544536fc809b", "sha256_in_prefix": "ad21354b7d69ba4d7874eb7b8c971ab27ed3c0f7c3fbf9fb1086544536fc809b", "size_in_bytes": 38871}, {"_path": "Library/include/libxml2/libxml/uri.h", "path_type": "hardlink", "sha256": "27db5e24799ee73f3cddce2dc05e6822611863e137c52be17521a9c9156dbc88", "sha256_in_prefix": "27db5e24799ee73f3cddce2dc05e6822611863e137c52be17521a9c9156dbc88", "size_in_bytes": 2855}, {"_path": "Library/include/libxml2/libxml/valid.h", "path_type": "hardlink", "sha256": "5128984012dc2538d26da86cedefbfd1b594c4eef4f43f2b37f3152d51b55299", "sha256_in_prefix": "5128984012dc2538d26da86cedefbfd1b594c4eef4f43f2b37f3152d51b55299", "size_in_bytes": 13305}, {"_path": "Library/include/libxml2/libxml/xinclude.h", "path_type": "hardlink", "sha256": "8f21da57a43cb22f28cafd18112915f4edde6cd404a03ebcb1ea139d7f68066f", "sha256_in_prefix": "8f21da57a43cb22f28cafd18112915f4edde6cd404a03ebcb1ea139d7f68066f", "size_in_bytes": 3109}, {"_path": "Library/include/libxml2/libxml/xlink.h", "path_type": "hardlink", "sha256": "53025542f1406b1ebdb5d32478f6220257e4a734d319933b8563321b31037709", "sha256_in_prefix": "53025542f1406b1ebdb5d32478f6220257e4a734d319933b8563321b31037709", "size_in_bytes": 5002}, {"_path": "Library/include/libxml2/libxml/xmlIO.h", "path_type": "hardlink", "sha256": "e74e2c4b67e67575243f8491a604e928e8e5e6cf5c50c17704f47aa79adeb618", "sha256_in_prefix": "e74e2c4b67e67575243f8491a604e928e8e5e6cf5c50c17704f47aa79adeb618", "size_in_bytes": 12455}, {"_path": "Library/include/libxml2/libxml/xmlautomata.h", "path_type": "hardlink", "sha256": "e95ffdf4ece67e3cb5132c070721cb06b82f06c22db98a15df8aa27c90dd5c37", "sha256_in_prefix": "e95ffdf4ece67e3cb5132c070721cb06b82f06c22db98a15df8aa27c90dd5c37", "size_in_bytes": 3787}, {"_path": "Library/include/libxml2/libxml/xmlerror.h", "path_type": "hardlink", "sha256": "a7d81d3c672b9300bd4c8d8b294db81f2ffd99a051f4525ccef779f9e677fdc7", "sha256_in_prefix": "a7d81d3c672b9300bd4c8d8b294db81f2ffd99a051f4525ccef779f9e677fdc7", "size_in_bytes": 37703}, {"_path": "Library/include/libxml2/libxml/xmlexports.h", "path_type": "hardlink", "sha256": "cf9315d6492bb5fde04d1e1c86a2b32b7d8f005de8912fe5cccb9ce557fc00a3", "sha256_in_prefix": "cf9315d6492bb5fde04d1e1c86a2b32b7d8f005de8912fe5cccb9ce557fc00a3", "size_in_bytes": 3280}, {"_path": "Library/include/libxml2/libxml/xmlmemory.h", "path_type": "hardlink", "sha256": "1b67c2d36c02bd9adbb3132ed2847c36906ad1ba83e229a6c7864f6d8da15794", "sha256_in_prefix": "1b67c2d36c02bd9adbb3132ed2847c36906ad1ba83e229a6c7864f6d8da15794", "size_in_bytes": 4904}, {"_path": "Library/include/libxml2/libxml/xmlmodule.h", "path_type": "hardlink", "sha256": "cbc026ca066b477c1fe5411fca1bba49feac9fb4d33b8ee0d0551fc1db5ec40f", "sha256_in_prefix": "cbc026ca066b477c1fe5411fca1bba49feac9fb4d33b8ee0d0551fc1db5ec40f", "size_in_bytes": 1138}, {"_path": "Library/include/libxml2/libxml/xmlreader.h", "path_type": "hardlink", "sha256": "cd8d9a554c19965d8cc6f0b84b8edc392b70cbef061c4bca80fced7517d023e3", "sha256_in_prefix": "cd8d9a554c19965d8cc6f0b84b8edc392b70cbef061c4bca80fced7517d023e3", "size_in_bytes": 12205}, {"_path": "Library/include/libxml2/libxml/xmlregexp.h", "path_type": "hardlink", "sha256": "00aebd6ed95c3e11bf8bfeb0541f95001963a2d6e0db8d5616c2f7e124cac668", "sha256_in_prefix": "00aebd6ed95c3e11bf8bfeb0541f95001963a2d6e0db8d5616c2f7e124cac668", "size_in_bytes": 5149}, {"_path": "Library/include/libxml2/libxml/xmlsave.h", "path_type": "hardlink", "sha256": "70a82ec80d1a93f0657aa4dfc49ea2fce1ac76a94b1f871d9a70a7f0d89d9e62", "sha256_in_prefix": "70a82ec80d1a93f0657aa4dfc49ea2fce1ac76a94b1f871d9a70a7f0d89d9e62", "size_in_bytes": 2568}, {"_path": "Library/include/libxml2/libxml/xmlschemas.h", "path_type": "hardlink", "sha256": "9571ec61380f169bd2645f0b485987945e84c9c5f53e4cf35bbcde8e8b0e9f7e", "sha256_in_prefix": "9571ec61380f169bd2645f0b485987945e84c9c5f53e4cf35bbcde8e8b0e9f7e", "size_in_bytes": 6902}, {"_path": "Library/include/libxml2/libxml/xmlschemastypes.h", "path_type": "hardlink", "sha256": "318c251a6a0a028de51d169a2a09c25e22e63d3f4a463771c8227b4c4c99ea33", "sha256_in_prefix": "318c251a6a0a028de51d169a2a09c25e22e63d3f4a463771c8227b4c4c99ea33", "size_in_bytes": 4583}, {"_path": "Library/include/libxml2/libxml/xmlstring.h", "path_type": "hardlink", "sha256": "7793e9ab13f5235b1f982507bd526d8e80bd87b84b1dc000439a24fd1b5fe742", "sha256_in_prefix": "7793e9ab13f5235b1f982507bd526d8e80bd87b84b1dc000439a24fd1b5fe742", "size_in_bytes": 5271}, {"_path": "Library/include/libxml2/libxml/xmlunicode.h", "path_type": "hardlink", "sha256": "1a43b69fd62cb5422f877101f96a35d3ef37e7f8f0b0f5e9327dab317b87b008", "sha256_in_prefix": "1a43b69fd62cb5422f877101f96a35d3ef37e7f8f0b0f5e9327dab317b87b008", "size_in_bytes": 11125}, {"_path": "Library/include/libxml2/libxml/xmlversion.h", "path_type": "hardlink", "sha256": "9f744ab7d57dc64115e74a6c4aec246d516d00f79bcd96fcd19a21602a286e23", "sha256_in_prefix": "9f744ab7d57dc64115e74a6c4aec246d516d00f79bcd96fcd19a21602a286e23", "size_in_bytes": 5747}, {"_path": "Library/include/libxml2/libxml/xmlwriter.h", "path_type": "hardlink", "sha256": "04453060d2b1df1ca60c4f6f7a992c10aef256af525d89b5776a509f394fcbdd", "sha256_in_prefix": "04453060d2b1df1ca60c4f6f7a992c10aef256af525d89b5776a509f394fcbdd", "size_in_bytes": 20688}, {"_path": "Library/include/libxml2/libxml/xpath.h", "path_type": "hardlink", "sha256": "5e9e684a09d4e6628a513721512c321b760536950fd5f9fbf93169c1104d6081", "sha256_in_prefix": "5e9e684a09d4e6628a513721512c321b760536950fd5f9fbf93169c1104d6081", "size_in_bytes": 16578}, {"_path": "Library/include/libxml2/libxml/xpathInternals.h", "path_type": "hardlink", "sha256": "cb71743fb6b0064f6a85142698482d483107a12bdb2cdfd0daf2227df972a522", "sha256_in_prefix": "cb71743fb6b0064f6a85142698482d483107a12bdb2cdfd0daf2227df972a522", "size_in_bytes": 18419}, {"_path": "Library/include/libxml2/libxml/xpointer.h", "path_type": "hardlink", "sha256": "f4c2b0eee468c31e77a1dde995110bfecce2a04b9d825652e87d8451eb414e84", "sha256_in_prefix": "f4c2b0eee468c31e77a1dde995110bfecce2a04b9d825652e87d8451eb414e84", "size_in_bytes": 3647}, {"_path": "Library/lib/libxml2.lib", "path_type": "hardlink", "sha256": "895cfe19cd3e66ae19861dd1c798ce448c8f18f858cc1113ab2f860e7ceea027", "sha256_in_prefix": "895cfe19cd3e66ae19861dd1c798ce448c8f18f858cc1113ab2f860e7ceea027", "size_in_bytes": 374152}, {"_path": "Library/lib/libxml2_a.lib", "path_type": "hardlink", "sha256": "a238d616dfb6a91a258b6761c0976f90e04bfc765c60c6a12e19f64ffa0c5ae9", "sha256_in_prefix": "a238d616dfb6a91a258b6761c0976f90e04bfc765c60c6a12e19f64ffa0c5ae9", "size_in_bytes": 4859658}, {"_path": "Library/lib/libxml2_a_dll.lib", "path_type": "hardlink", "sha256": "7fa5db79688f1b3b6a3da1d673fc4c024711067764194d79596e9b499a2c15f1", "sha256_in_prefix": "7fa5db79688f1b3b6a3da1d673fc4c024711067764194d79596e9b499a2c15f1", "size_in_bytes": 4854626}, {"_path": "Library/lib/pkgconfig/libxml-2.0.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_d56gma4ure/croot/libxml2_1744385549587/_h_env", "sha256": "703b64f75250fad5a598bb98ea5afc6aa0627a623fb7265f2c747e194edaed5a", "sha256_in_prefix": "bb77dffe7b07b8988a18582485d61b6f2351e61edbc9d13ce0d72a74a510a59a", "size_in_bytes": 666}], "paths_version": 1}, "requested_spec": "None", "sha256": "fa213181093d3e573a6e70d8047cf9a457eca81b8f98000f74dc2e2873f51c83", "size": 3056535, "subdir": "win-64", "timestamp": 1744385698000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libxml2-2.13.7-h866ff63_0.conda", "version": "2.13.7"}