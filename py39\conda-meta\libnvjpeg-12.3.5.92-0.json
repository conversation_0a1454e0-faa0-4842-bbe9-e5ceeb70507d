{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvjpeg-12.3.5.92-0", "files": ["Library/bin/nvjpeg64_12.dll"], "fn": "libnvjpeg-12.3.5.92-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvjpeg-12.3.5.92-0", "type": 1}, "md5": "0696711a9d9ba12c5417fd3374688bdb", "name": "libnvjpeg", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvjpeg-12.3.5.92-0.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvjpeg64_12.dll", "path_type": "hardlink", "sha256": "dd65708285bdab2a782e7200d169ecc6c89166e1aba0be668f3b64bf4b89ec2e", "sha256_in_prefix": "dd65708285bdab2a782e7200d169ecc6c89166e1aba0be668f3b64bf4b89ec2e", "size_in_bytes": 6171648}], "paths_version": 1}, "requested_spec": "None", "sha256": "c916f5a612d31de895a8e4adfbaea8525125d6cec92585f7e74b8352dcf587f5", "size": 2647908, "subdir": "win-64", "timestamp": 1739448907000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/libnvjpeg-12.3.5.92-0.conda", "version": "12.3.5.92"}