{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/noarch", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart-static_win-64-12.8.90-0", "files": ["Library/lib/cudart_static.lib"], "fn": "cuda-cudart-static_win-64-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart-static_win-64-12.8.90-0", "type": 1}, "md5": "d8eb689c189c849eaf6b02e038321dfb", "name": "cuda-cudart-static_win-64", "noarch": "generic", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart-static_win-64-12.8.90-0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/lib/cudart_static.lib", "path_type": "hardlink", "sha256": "af41c66fc8543dc5876d39ce1327032bbbca87424d9016f9c066460476a251b0", "sha256_in_prefix": "af41c66fc8543dc5876d39ce1327032bbbca87424d9016f9c066460476a251b0", "size_in_bytes": 2852804}], "paths_version": 1}, "requested_spec": "None", "sha256": "d5d9da06be321c011ec8357b450f36ac43b79513ff16073d08d5851800c65731", "size": 344537, "subdir": "noarch", "timestamp": 1739448558000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/noarch/cuda-cudart-static_win-64-12.8.90-0.conda", "version": "12.8.90"}