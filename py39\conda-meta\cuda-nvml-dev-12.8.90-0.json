{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvml-dev-12.8.90-0", "files": ["Library/include/nvml.h", "Library/lib/nvml.lib"], "fn": "cuda-nvml-dev-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvml-dev-12.8.90-0", "type": 1}, "md5": "394ab92ac6d6c343de147ce4b74fce39", "name": "cuda-nvml-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvml-dev-12.8.90-0.conda", "paths_data": {"paths": [{"_path": "Library/include/nvml.h", "path_type": "hardlink", "sha256": "2469cfaa1c2497412848b66bafc57782540536df4dd39d1ab59c519722fe8b60", "sha256_in_prefix": "2469cfaa1c2497412848b66bafc57782540536df4dd39d1ab59c519722fe8b60", "size_in_bytes": 660051}, {"_path": "Library/lib/nvml.lib", "path_type": "hardlink", "sha256": "57292a7d98b0ea553bb8f6978c4b12a4d838ca2d285f4f2495ca6e983c49d917", "sha256_in_prefix": "57292a7d98b0ea553bb8f6978c4b12a4d838ca2d285f4f2495ca6e983c49d917", "size_in_bytes": 102660}], "paths_version": 1}, "requested_spec": "None", "sha256": "e4a91b6dfe11d4aab609ed8ad935bae3976283ff5faf0ce6db9409fef4246317", "size": 106085, "subdir": "win-64", "timestamp": 1739446679000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-nvml-dev-12.8.90-0.conda", "version": "12.8.90"}