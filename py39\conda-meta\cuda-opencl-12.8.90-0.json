{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "khronos-opencl-icd-loader >=2024.5.8", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-opencl-12.8.90-0", "files": ["Library/etc/OpenCL/vendors/cuda.icd"], "fn": "cuda-opencl-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-opencl-12.8.90-0", "type": 1}, "md5": "f78eabf7ddf90fd093c852bed8d55e98", "name": "cuda-opencl", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-opencl-12.8.90-0.conda", "paths_data": {"paths": [{"_path": "Library/etc/OpenCL/vendors/cuda.icd", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}], "paths_version": 1}, "requested_spec": "None", "sha256": "ce8ac5f63a2b5d07ef39a2164ea3844774200035dfdd1749a6e7d33f0a5abade", "size": 17106, "subdir": "win-64", "timestamp": 1739447963000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-opencl-12.8.90-0.conda", "version": "12.8.90"}