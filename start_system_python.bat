@echo off
echo Starting heygem service with system Python...

REM Set MKL environment variables to fix LLVM error
set MKL_DEBUG_CPU_TYPE=5
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1

REM Set other necessary environment variables
set GRADIO_TEMP_DIR=%cd%\tmp\
set HF_ENDPOINT=https://hf-mirror.com
set HF_HOME=%CD%\hf_download
set TRANSFORMERS_CACHE=%CD%\tf_download
set XFORMERS_FORCE_DISABLE_TRITON=1
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
set CUDA_VISIBLE_DEVICES=0

echo Checking Python version...
python --version

echo Checking PyTorch GPU support...
python -c "import torch; print('PyTorch version:', torch.__version__); print('CUDA available:', torch.cuda.is_available())"

echo Starting application...
python app.py

echo Program exited, press any key to close...
pause
