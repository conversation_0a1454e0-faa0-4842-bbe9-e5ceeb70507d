{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-cupti-dev 12.8.90.*", "cuda-nvdisasm 12.8.90.*", "cuda-nvprof 12.8.90.*", "cuda-sanitizer-api 12.8.93.*"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-command-line-tools-12.8.1-0", "files": [], "fn": "cuda-command-line-tools-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-command-line-tools-12.8.1-0", "type": 1}, "md5": "87cf99056c15e2fd9e18d00df2865321", "name": "cuda-command-line-tools", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-command-line-tools-12.8.1-0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "d092b55050ec53490d32aae18802a5ad8e63ffe343e5634a2fe072e513d1b7fb", "size": 17073, "subdir": "win-64", "timestamp": 1741063655000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-command-line-tools-12.8.1-0.conda", "version": "12.8.1"}