{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-cudart-static_win-64 12.8.90 0", "cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart-static-12.8.90-0", "files": [], "fn": "cuda-cudart-static-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart-static-12.8.90-0", "type": 1}, "md5": "5e914353b31ba6d03b80e1ec5ab6b47a", "name": "cuda-cudart-static", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart-static-12.8.90-0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "231e2522e5a997d8b062a15204bffbdb2ab1ff296ff8c34fd27ca829f249d634", "size": 16872, "subdir": "win-64", "timestamp": 1739448594000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-cudart-static-12.8.90-0.conda", "version": "12.8.90"}