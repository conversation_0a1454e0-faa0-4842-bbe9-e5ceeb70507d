@echo off
chcp 65001 > nul
echo 正在启动数字人服务...
REM 设置MKL环境变量解决LLVM错误
set MKL_DEBUG_CPU_TYPE=5
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1
set MKL_THREADING_LAYER=INTEL
set KMP_DUPLICATE_LIB_OK=TRUE
set MKL_ENABLE_INSTRUCTIONS=AVX2
set MKL_CBWR=AUTO

set GRADIO_TEMP_DIR=%cd%\tmp\
SET PYTHON_PATH=%cd%\py39\
rem overriding default python env vars in order not to interfere with any system python installation
SET PYTHONHOME=
SET PYTHONPATH=
SET PYTHONEXECUTABLE=%PYTHON_PATH%\python.exe
SET PYTHONWEXECUTABLE=%PYTHON_PATH%pythonw.exe
SET PYTHON_EXECUTABLE=%PYTHON_PATH%\python.exe
SET PYTHONW_EXECUTABLE=%PYTHON_PATH%pythonw.exe
SET PYTHON_BIN_PATH=%PYTHON_EXECUTABLE%
SET PYTHON_LIB_PATH=%PYTHON_PATH%\Lib\site-packages
set CU_PATH=%PYTHON_PATH%\Lib\site-packages\torch\lib
set cuda_PATH=%PYTHON_PATH%\Library\bin
SET FFMPEG_PATH=%cd%\py39\ffmpeg\bin
SET PATH=%PYTHON_PATH%;%PYTHON_PATH%\Scripts;%FFMPEG_PATH%;%CU_PATH%;%cuda_PATH%;%PATH%
set HF_ENDPOINT=https://hf-mirror.com
set HF_HOME=%CD%\hf_download
set TRANSFORMERS_CACHE=%CD%\tf_download
set XFORMERS_FORCE_DISABLE_TRITON=1
REM 设置正确的SSL证书路径
set SSL_CERT_FILE=%PYTHON_PATH%\Lib\site-packages\certifi\cacert.pem
@REM set CUDA_VISIBLE_DEVICES=0
@REM set PYTHONPATH=third_party/AcademiCodec;third_party/Matcha-TTS
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

echo.
echo 环境变量设置完成:
echo MKL_DEBUG_CPU_TYPE=%MKL_DEBUG_CPU_TYPE%
echo OMP_NUM_THREADS=%OMP_NUM_THREADS%
echo MKL_NUM_THREADS=%MKL_NUM_THREADS%
echo MKL_THREADING_LAYER=%MKL_THREADING_LAYER%
echo KMP_DUPLICATE_LIB_OK=%KMP_DUPLICATE_LIB_OK%
echo MKL_ENABLE_INSTRUCTIONS=%MKL_ENABLE_INSTRUCTIONS%
echo MKL_CBWR=%MKL_CBWR%
echo SSL_CERT_FILE=%SSL_CERT_FILE%
echo.
echo 启动数字人应用...

"%PYTHON_EXECUTABLE%" -s app.py
pause
