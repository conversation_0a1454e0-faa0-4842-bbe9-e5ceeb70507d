{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvrtc-12.8.93-0", "files": ["Library/bin/nvrtc-builtins64_128.dll", "Library/bin/nvrtc64_120_0.alt.dll", "Library/bin/nvrtc64_120_0.dll"], "fn": "cuda-nvrtc-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvrtc-12.8.93-0", "type": 1}, "md5": "833185c67a5dcfe88b6b631de3ee8dcc", "name": "cuda-nvrtc", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvrtc-12.8.93-0.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvrtc-builtins64_128.dll", "path_type": "hardlink", "sha256": "a7afa2a40cbd3f922a33d8bf71c13cb26ca707c7016a9bc5fcde36759ba668f5", "sha256_in_prefix": "a7afa2a40cbd3f922a33d8bf71c13cb26ca707c7016a9bc5fcde36759ba668f5", "size_in_bytes": 6356480}, {"_path": "Library/bin/nvrtc64_120_0.alt.dll", "path_type": "hardlink", "sha256": "5464ea01b079d78042374dc7819684910c8e9d5c63000e2d2d5aa02298ecf2ae", "sha256_in_prefix": "5464ea01b079d78042374dc7819684910c8e9d5c63000e2d2d5aa02298ecf2ae", "size_in_bytes": 86794240}, {"_path": "Library/bin/nvrtc64_120_0.dll", "path_type": "hardlink", "sha256": "02d2d7ef4690bf1a55dda1d3ac94c86ce8d16920071c87bcd3e70e94ef346e93", "sha256_in_prefix": "02d2d7ef4690bf1a55dda1d3ac94c86ce8d16920071c87bcd3e70e94ef346e93", "size_in_bytes": 86728192}], "paths_version": 1}, "requested_spec": "None", "sha256": "4489726e3f0725b811303c28c6ab4731137a2037e2e7a0ca9c08817a1a996bef", "size": 56538282, "subdir": "win-64", "timestamp": 1740204473000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-nvrtc-12.8.93-0.conda", "version": "12.8.93"}