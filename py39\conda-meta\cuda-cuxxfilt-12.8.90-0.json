{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cuxxfilt-12.8.90-0", "files": ["Library/bin/cu++filt.exe", "Library/include/nv_decode.h", "Library/lib/cufilt.lib"], "fn": "cuda-cuxxfilt-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cuxxfilt-12.8.90-0", "type": 1}, "md5": "9fd1806da505c1f875f56d2c4dc2a2fd", "name": "cuda-cuxxfilt", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cuxxfilt-12.8.90-0.conda", "paths_data": {"paths": [{"_path": "Library/bin/cu++filt.exe", "path_type": "hardlink", "sha256": "a10540581b65ad6f0e793cc0e972207f7368010004be83085f9ea95ed731ae02", "sha256_in_prefix": "a10540581b65ad6f0e793cc0e972207f7368010004be83085f9ea95ed731ae02", "size_in_bytes": 217600}, {"_path": "Library/include/nv_decode.h", "path_type": "hardlink", "sha256": "7a69f3d07f81ddc8d487d51a6348d8cdeec44c9be7e465d364401b7ebb621bb7", "sha256_in_prefix": "7a69f3d07f81ddc8d487d51a6348d8cdeec44c9be7e465d364401b7ebb621bb7", "size_in_bytes": 1970}, {"_path": "Library/lib/cufilt.lib", "path_type": "hardlink", "sha256": "aa50639876d78d600c11273d3324a193f21b249ee09231e263af87493b273101", "sha256_in_prefix": "aa50639876d78d600c11273d3324a193f21b249ee09231e263af87493b273101", "size_in_bytes": 197314}], "paths_version": 1}, "requested_spec": "None", "sha256": "adaaf941fa30c5999436232c3aecf84909baff168d45bff40cd8b75a13b83bcc", "size": 149293, "subdir": "win-64", "timestamp": 1739448832000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-cuxxfilt-12.8.90-0.conda", "version": "12.8.90"}