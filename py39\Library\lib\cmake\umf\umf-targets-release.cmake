#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "umf::umf" for configuration "Release"
set_property(TARGET umf::umf APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(umf::umf PROPERTIES
  IMPORTED_IMPLIB_RELEASE "${_IMPORT_PREFIX}/lib/umf.lib"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/bin/umf.dll"
  )

list(APPEND _cmake_import_check_targets umf::umf )
list(APPEND _cmake_import_check_files_for_umf::umf "${_IMPORT_PREFIX}/lib/umf.lib" "${_IMPORT_PREFIX}/bin/umf.dll" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
