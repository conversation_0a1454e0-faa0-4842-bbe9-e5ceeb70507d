{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": ["liblibnvfatbin-static >=12.8.90"], "depends": ["cuda-version >=12.8,<12.9.0a0", "libnvfatbin 12.8.90 0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvfatbin-dev-12.8.90-0", "files": ["Library/include/nvFatbin.h", "Library/lib/nvfatbin.lib", "Library/lib/nvfatbin_static.lib"], "fn": "libnvfatbin-dev-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvfatbin-dev-12.8.90-0", "type": 1}, "md5": "223ca13e66448e475e4eeca9a123bedd", "name": "libnvfatbin-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvfatbin-dev-12.8.90-0.conda", "paths_data": {"paths": [{"_path": "Library/include/nvFatbin.h", "path_type": "hardlink", "sha256": "17008ea9772a13e719a892a3aa4223c545ec02866460887d24dc9c3f579a43f6", "sha256_in_prefix": "17008ea9772a13e719a892a3aa4223c545ec02866460887d24dc9c3f579a43f6", "size_in_bytes": 13494}, {"_path": "Library/lib/nvfatbin.lib", "path_type": "hardlink", "sha256": "df9968bbb1a2b01e65e0e8e52e87287c6d0bc68c56e26e481066bdedc0331843", "sha256_in_prefix": "df9968bbb1a2b01e65e0e8e52e87287c6d0bc68c56e26e481066bdedc0331843", "size_in_bytes": 3984}, {"_path": "Library/lib/nvfatbin_static.lib", "path_type": "hardlink", "sha256": "fca67bf5e714f30e9ea1c116fb99d6bbdcde6bdd278e236de2a25e01ccade91e", "sha256_in_prefix": "fca67bf5e714f30e9ea1c116fb99d6bbdcde6bdd278e236de2a25e01ccade91e", "size_in_bytes": 5175690}], "paths_version": 1}, "requested_spec": "None", "sha256": "0e685ed29a49919ec67437460a304daf22e3d3ad69e556739419246ab1d8ca0e", "size": 1393629, "subdir": "win-64", "timestamp": 1739448753000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/libnvfatbin-dev-12.8.90-0.conda", "version": "12.8.90"}