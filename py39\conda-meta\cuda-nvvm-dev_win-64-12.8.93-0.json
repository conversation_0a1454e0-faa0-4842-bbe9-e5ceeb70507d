{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/noarch", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvvm-dev_win-64-12.8.93-0", "files": [], "fn": "cuda-nvvm-dev_win-64-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvvm-dev_win-64-12.8.93-0", "type": 1}, "md5": "623510d23346ffca587e8917d57bff8d", "name": "cuda-nvvm-dev_win-64", "noarch": "generic", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvvm-dev_win-64-12.8.93-0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "e6fd5b5c884077a8ed24953ee52dd138006ab6ae8ca0eb2eaf3ac084534adf42", "size": 16926, "subdir": "noarch", "timestamp": 1740205159000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/noarch/cuda-nvvm-dev_win-64-12.8.93-0.conda", "version": "12.8.93"}