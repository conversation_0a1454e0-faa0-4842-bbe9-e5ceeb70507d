{"build": "h8cc25b3_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0", "files": ["Library/bin/OpenCL.dll", "Library/bin/cllayerinfo.exe", "Library/etc/OpenCL/vendors/opencl-helper.bat", "Library/include/CL/cl.h", "Library/include/CL/cl_d3d10.h", "Library/include/CL/cl_d3d11.h", "Library/include/CL/cl_dx9_media_sharing.h", "Library/include/CL/cl_dx9_media_sharing_intel.h", "Library/include/CL/cl_egl.h", "Library/include/CL/cl_ext.h", "Library/include/CL/cl_ext_intel.h", "Library/include/CL/cl_function_types.h", "Library/include/CL/cl_gl.h", "Library/include/CL/cl_gl_ext.h", "Library/include/CL/cl_half.h", "Library/include/CL/cl_icd.h", "Library/include/CL/cl_layer.h", "Library/include/CL/cl_platform.h", "Library/include/CL/cl_va_api_media_sharing_intel.h", "Library/include/CL/cl_version.h", "Library/include/CL/opencl.h", "Library/lib/OpenCL.lib", "Library/share/cmake/OpenCLICDLoader/OpenCLICDLoaderConfig.cmake", "Library/share/cmake/OpenCLICDLoader/OpenCLICDLoaderConfigVersion.cmake", "Library/share/cmake/OpenCLICDLoader/OpenCLICDLoaderTargets-release.cmake", "Library/share/cmake/OpenCLICDLoader/OpenCLICDLoaderTargets.cmake", "etc/conda/activate.d/khronos-opencl-icd-loader_activate.bat", "etc/conda/activate.d/khronos-opencl-icd-loader_activate.sh", "etc/conda/deactivate.d/khronos-opencl-icd-loader_deactivate.bat", "etc/conda/deactivate.d/khronos-opencl-icd-loader_deactivate.sh"], "fn": "khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0.conda", "license": "Apache-2.0", "link": {"source": "D:\\anaconda3\\pkgs\\khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0", "type": 1}, "md5": "d94a8e9ad411944f1de816e9c1282bdb", "name": "khronos-opencl-icd-loader", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/OpenCL.dll", "path_type": "hardlink", "sha256": "423dc70155030163efa34319320fd69d8d5e38d3eabaf21173b488d705bd54a7", "sha256_in_prefix": "423dc70155030163efa34319320fd69d8d5e38d3eabaf21173b488d705bd54a7", "size_in_bytes": 66832}, {"_path": "Library/bin/cllayerinfo.exe", "path_type": "hardlink", "sha256": "96d48ae6bb383105e4c0e225727ef2e3484b8638c83b35ad86d7101c512e34b9", "sha256_in_prefix": "96d48ae6bb383105e4c0e225727ef2e3484b8638c83b35ad86d7101c512e34b9", "size_in_bytes": 58128}, {"_path": "Library/etc/OpenCL/vendors/opencl-helper.bat", "path_type": "hardlink", "sha256": "b7f1e6eb78989303cf6934d44710eb707563a53ded9208b60632fc4946e4eede", "sha256_in_prefix": "b7f1e6eb78989303cf6934d44710eb707563a53ded9208b60632fc4946e4eede", "size_in_bytes": 424}, {"_path": "Library/include/CL/cl.h", "path_type": "hardlink", "sha256": "065f453d561c93fde178f4821fa767d58505d179fc3c2d9725d072af1129644b", "sha256_in_prefix": "065f453d561c93fde178f4821fa767d58505d179fc3c2d9725d072af1129644b", "size_in_bytes": 81631}, {"_path": "Library/include/CL/cl_d3d10.h", "path_type": "hardlink", "sha256": "c1def94c32e2b0b70e83b2abc9e4654b8f52b7e27fcc5a402757e2ede0cff1cc", "sha256_in_prefix": "c1def94c32e2b0b70e83b2abc9e4654b8f52b7e27fcc5a402757e2ede0cff1cc", "size_in_bytes": 8057}, {"_path": "Library/include/CL/cl_d3d11.h", "path_type": "hardlink", "sha256": "445bfe785685db4f6662ea14e56d567240c1608fb5688cd0dc503370dc505fb3", "sha256_in_prefix": "445bfe785685db4f6662ea14e56d567240c1608fb5688cd0dc503370dc505fb3", "size_in_bytes": 8095}, {"_path": "Library/include/CL/cl_dx9_media_sharing.h", "path_type": "hardlink", "sha256": "9741fe55c9f09e4354beb91fd039deb24c0ce839d20199d987bc0dd956f8c168", "sha256_in_prefix": "9741fe55c9f09e4354beb91fd039deb24c0ce839d20199d987bc0dd956f8c168", "size_in_bytes": 12246}, {"_path": "Library/include/CL/cl_dx9_media_sharing_intel.h", "path_type": "hardlink", "sha256": "0ce4432973392815885d4b6fe968135682502d702ec3dbde6db78e173adfa8da", "sha256_in_prefix": "0ce4432973392815885d4b6fe968135682502d702ec3dbde6db78e173adfa8da", "size_in_bytes": 959}, {"_path": "Library/include/CL/cl_egl.h", "path_type": "hardlink", "sha256": "324754771c40cb79e0f7722e72454d1d463663ee2df5defe4e428aea1a8b75f0", "sha256_in_prefix": "324754771c40cb79e0f7722e72454d1d463663ee2df5defe4e428aea1a8b75f0", "size_in_bytes": 5672}, {"_path": "Library/include/CL/cl_ext.h", "path_type": "hardlink", "sha256": "c69a238c14f2eaffefadc909c43d3efd1cb2312095979c0e74c25bc9262e0abb", "sha256_in_prefix": "c69a238c14f2eaffefadc909c43d3efd1cb2312095979c0e74c25bc9262e0abb", "size_in_bytes": 138218}, {"_path": "Library/include/CL/cl_ext_intel.h", "path_type": "hardlink", "sha256": "20fc359c30ad2339af7c8442da97ebd8e6ea4fb8f57c47b96b0f284d4c281953", "sha256_in_prefix": "20fc359c30ad2339af7c8442da97ebd8e6ea4fb8f57c47b96b0f284d4c281953", "size_in_bytes": 902}, {"_path": "Library/include/CL/cl_function_types.h", "path_type": "hardlink", "sha256": "a90d127737e412ef3edb2ffa90f6c237ddb58899b3b14a1ec412ea0eb88c8009", "sha256_in_prefix": "a90d127737e412ef3edb2ffa90f6c237ddb58899b3b14a1ec412ea0eb88c8009", "size_in_bytes": 33387}, {"_path": "Library/include/CL/cl_gl.h", "path_type": "hardlink", "sha256": "df2ca12d7dbd1338ad634634662a62d23dfdce2ed59b46e1e3134bae143e5380", "sha256_in_prefix": "df2ca12d7dbd1338ad634634662a62d23dfdce2ed59b46e1e3134bae143e5380", "size_in_bytes": 12040}, {"_path": "Library/include/CL/cl_gl_ext.h", "path_type": "hardlink", "sha256": "7fd0d4ad6072a90b05749fe4d2131b424b71d19ae809ae3d7d4862b0d0aee16a", "sha256_in_prefix": "7fd0d4ad6072a90b05749fe4d2131b424b71d19ae809ae3d7d4862b0d0aee16a", "size_in_bytes": 905}, {"_path": "Library/include/CL/cl_half.h", "path_type": "hardlink", "sha256": "c4d64cb15e710e60058d6072f8fbb1ed988eda60d16f6fc1ddb7df8c9e903109", "sha256_in_prefix": "c4d64cb15e710e60058d6072f8fbb1ed988eda60d16f6fc1ddb7df8c9e903109", "size_in_bytes": 10430}, {"_path": "Library/include/CL/cl_icd.h", "path_type": "hardlink", "sha256": "a43e3a5c9921a4f9a59001884e75d8b79a0afef50644f80543ec63f0ca71cfce", "sha256_in_prefix": "a43e3a5c9921a4f9a59001884e75d8b79a0afef50644f80543ec63f0ca71cfce", "size_in_bytes": 11505}, {"_path": "Library/include/CL/cl_layer.h", "path_type": "hardlink", "sha256": "a00b590ccc16cd6bee2c78bb6bd5803961365bcc1cda92bc1f854c1e5a845fb5", "sha256_in_prefix": "a00b590ccc16cd6bee2c78bb6bd5803961365bcc1cda92bc1f854c1e5a845fb5", "size_in_bytes": 3544}, {"_path": "Library/include/CL/cl_platform.h", "path_type": "hardlink", "sha256": "f329455a208ef048b9b58e26415c64357d573dc91d3f780c50b96f4e0a9f1b90", "sha256_in_prefix": "f329455a208ef048b9b58e26415c64357d573dc91d3f780c50b96f4e0a9f1b90", "size_in_bytes": 43430}, {"_path": "Library/include/CL/cl_va_api_media_sharing_intel.h", "path_type": "hardlink", "sha256": "251935aff70ac3e6bf2d9bbdf752bb95490e61eb508781fdec658a8b2791ae2e", "sha256_in_prefix": "251935aff70ac3e6bf2d9bbdf752bb95490e61eb508781fdec658a8b2791ae2e", "size_in_bytes": 7090}, {"_path": "Library/include/CL/cl_version.h", "path_type": "hardlink", "sha256": "1b161f73c0f0b07121314055f75df6c19d6a93e3a0d24202c6f3e2dd1615b2a2", "sha256_in_prefix": "1b161f73c0f0b07121314055f75df6c19d6a93e3a0d24202c6f3e2dd1615b2a2", "size_in_bytes": 3125}, {"_path": "Library/include/CL/opencl.h", "path_type": "hardlink", "sha256": "9a76fa63630e8fd5b30b5e6c692dbe7ab22edff708dfe6a06d04a3671a7716e1", "sha256_in_prefix": "9a76fa63630e8fd5b30b5e6c692dbe7ab22edff708dfe6a06d04a3671a7716e1", "size_in_bytes": 970}, {"_path": "Library/lib/OpenCL.lib", "path_type": "hardlink", "sha256": "d8b152a3040a616b6780b19cc38c70bb9dafd168902a7c8e1944be9e96762e83", "sha256_in_prefix": "d8b152a3040a616b6780b19cc38c70bb9dafd168902a7c8e1944be9e96762e83", "size_in_bytes": 28824}, {"_path": "Library/share/cmake/OpenCLICDLoader/OpenCLICDLoaderConfig.cmake", "path_type": "hardlink", "sha256": "1f8f47a4439fdd8e01d5d7ca83414dafee5d47e7a9474769797a662b2dbd702f", "sha256_in_prefix": "1f8f47a4439fdd8e01d5d7ca83414dafee5d47e7a9474769797a662b2dbd702f", "size_in_bytes": 65}, {"_path": "Library/share/cmake/OpenCLICDLoader/OpenCLICDLoaderConfigVersion.cmake", "path_type": "hardlink", "sha256": "8857997ba5e6938b3c1436b5dc400936a6924846b4ab34b038f7429bfeaae7d5", "sha256_in_prefix": "8857997ba5e6938b3c1436b5dc400936a6924846b4ab34b038f7429bfeaae7d5", "size_in_bytes": 1899}, {"_path": "Library/share/cmake/OpenCLICDLoader/OpenCLICDLoaderTargets-release.cmake", "path_type": "hardlink", "sha256": "32afba268148661ec1456cc884c173bc26b471bbbb324a5c09f24faf42a6ffbf", "sha256_in_prefix": "32afba268148661ec1456cc884c173bc26b471bbbb324a5c09f24faf42a6ffbf", "size_in_bytes": 1363}, {"_path": "Library/share/cmake/OpenCLICDLoader/OpenCLICDLoaderTargets.cmake", "path_type": "hardlink", "sha256": "0dff77e097723e87ff534e0f25023a8fced8840ce3222b633a86c720a49628c6", "sha256_in_prefix": "0dff77e097723e87ff534e0f25023a8fced8840ce3222b633a86c720a49628c6", "size_in_bytes": 4008}, {"_path": "etc/conda/activate.d/khronos-opencl-icd-loader_activate.bat", "path_type": "hardlink", "sha256": "267362c24e30823338564464fb4f2a1d1842e76acb8b6d871288088ebb4767d1", "sha256_in_prefix": "267362c24e30823338564464fb4f2a1d1842e76acb8b6d871288088ebb4767d1", "size_in_bytes": 385}, {"_path": "etc/conda/activate.d/khronos-opencl-icd-loader_activate.sh", "path_type": "hardlink", "sha256": "99ce6484507c7f2c9c8b0d08788ea9bdfcd2b5b6fe2505f3970e2f4b32c468b3", "sha256_in_prefix": "99ce6484507c7f2c9c8b0d08788ea9bdfcd2b5b6fe2505f3970e2f4b32c468b3", "size_in_bytes": 255}, {"_path": "etc/conda/deactivate.d/khronos-opencl-icd-loader_deactivate.bat", "path_type": "hardlink", "sha256": "210e938ed67961db298639fdd5f68791860b7d741fac70f152dfd65a82787004", "sha256_in_prefix": "210e938ed67961db298639fdd5f68791860b7d741fac70f152dfd65a82787004", "size_in_bytes": 57}, {"_path": "etc/conda/deactivate.d/khronos-opencl-icd-loader_deactivate.sh", "path_type": "hardlink", "sha256": "507a75a450cd3a60cdffe834d326f5ff002058fdb4f795e16c1315819a3198f2", "sha256_in_prefix": "507a75a450cd3a60cdffe834d326f5ff002058fdb4f795e16c1315819a3198f2", "size_in_bytes": 124}], "paths_version": 1}, "requested_spec": "None", "sha256": "453c84449d3edcf4559f1fce64954cf02b6f4b4d2df1c6c461558bfdbd9c81b4", "size": 101986, "subdir": "win-64", "timestamp": 1715878022000, "url": "https://repo.anaconda.com/pkgs/main/win-64/khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0.conda", "version": "2024.05.08"}