{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "libnvjitlink", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcusparse-12.5.8.93-0", "files": ["Library/bin/cusparse64_12.dll"], "fn": "libcusparse-12.5.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcusparse-12.5.8.93-0", "type": 1}, "md5": "106e220933460ead9f5eeffc471eaead", "name": "libcusparse", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcusparse-12.5.8.93-0.conda", "paths_data": {"paths": [{"_path": "Library/bin/cusparse64_12.dll", "path_type": "hardlink", "sha256": "f4688daa6163c47a0b5293926ec7ae367de6b4af54ea201638b308831b322a0e", "sha256_in_prefix": "f4688daa6163c47a0b5293926ec7ae367de6b4af54ea201638b308831b322a0e", "size_in_bytes": 379535872}], "paths_version": 1}, "requested_spec": "None", "sha256": "2640325a2942db25a59e53c37d37d48f7e8315e1702d3fd6273434be5df728e8", "size": 168326883, "subdir": "win-64", "timestamp": 1740561105000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/libcusparse-12.5.8.93-0.conda", "version": "12.5.8.93"}