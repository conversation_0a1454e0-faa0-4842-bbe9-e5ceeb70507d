Unified Memory Framework (UMF) Third Party Programs File

This file is the "third-party-programs.txt" file specified in the associated
Intel end user license agreement for the Intel software you are licensing.

The third party programs and their corresponding required notices and/or
license terms are listed below.
_______________________________________________________________________________

1.  Modified mimaloc new/delete implementation:

    MIT License

    Copyright (c) 2018-2021 Microsoft Corporation, <PERSON><PERSON>

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.

_______________________________________________________________________________

2.  Portable Hardware Locality (hwloc):

    Copyright (c) 2004-2006 The Trustees of Indiana University and Indiana
                            University Research and Technology Corporation. All
                            rights reserved.
    Copyright (c) 2004-2005 The University of Tennessee and The University of
                            Tennessee Research Foundation. All rights reserved.
    Copyright (c) 2004-2005 High Performance Computing Center Stuttgart,
                            University of Stuttgart. All rights reserved.
    Copyright (c) 2004-2005 The Regents of the University of California. All
                            rights reserved.
    Copyright (c) 2009      CNRS
    Copyright (c) 2009-2016 Inria. All rights reserved.
    Copyright (c) 2009-2015 Université Bordeaux
    Copyright (c) 2009-2015 Cisco Systems, Inc. All rights reserved.
    Copyright (c) 2009-2012 Oracle and/or its affiliates. All rights reserved.
    Copyright (c) 2010      IBM
    Copyright (c) 2010      Jirka Hladky
    Copyright (c) 2012      Aleksej Saushev, The NetBSD Foundation
    Copyright (c) 2012      Blue Brain Project, EPFL. All rights reserved.
    Copyright (c) 2013-2014 University of Wisconsin-La Crosse. All rights
                            reserved.
    Copyright (c) 2015      Research Organization for Information Science and
                            Technology (RIST). All rights reserved.
    Copyright (c) 2015-2016 Intel, Inc. All rights reserved.

    See COPYING in top-level directory.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:
    1. Redistributions of source code must retain the above copyright
       notice, this list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.
    3. The name of the author may not be used to endorse or promote products
       derived from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
    IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
    OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
    IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
    INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
    NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
    DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
    THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
    (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
    THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
_______________________________________________________________________________

3. jemalloc

    Copyright (C) 2002-present Jason Evans <<EMAIL>>.
    All rights reserved.
    Copyright (C) 2007-2012 Mozilla Foundation.  All rights reserved.
    Copyright (C) 2009-present Facebook, Inc.  All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are met:
    1. Redistributions of source code must retain the above copyright
    notice(s), this list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright
    notice(s), this list of conditions and the following disclaimer in the
    documentation and/or other materials provided with the distribution.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDER(S) ``AS IS'' AND ANY
    EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
    WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
    DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER(S) BE LIABLE FOR ANY
    DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
    (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
    SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
    CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
    DAMAGE.
_______________________________________________________________________________

4. ubench

    This is free and unencumbered software released into the public domain.

    Anyone is free to copy, modify, publish, use, compile, sell, or
    distribute this software, either in source code form or as a compiled
    binary, for any purpose, commercial or non-commercial, and by any
    means.

    In jurisdictions that recognize copyright laws, the author or authors
    of this software dedicate any and all copyright interest in the
    software to the public domain. We make this dedication for the benefit
    of the public at large and to the detriment of our heirs and
    successors. We intend this dedication to be an overt act of
    relinquishment in perpetuity of all present and future rights to this
    software under copyright law.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
    IN NO EVENT SHALL THE AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR
    OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
    ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
    OTHER DEALINGS IN THE SOFTWARE.

    For more information, please refer to <http://unlicense.org/>
_______________________________________________________________________________

5. Level Zero

    MIT License

    Copyright (C) 2019-2021 Intel Corporation

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
_______________________________________________________________________________

6. The Unified Runtime Project is under the Apache License v2.0 with LLVM 
   Exceptions:

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

    TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

    1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

    2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

    3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

    4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

    5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

    6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

    7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

    8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

    9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

    END OF TERMS AND CONDITIONS

    APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

    Copyright [yyyy] [name of copyright owner]

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

    ---- LLVM Exceptions to the Apache 2.0 License ----

    As an exception, if, as a result of your compiling your source code,
    portions of this Software are embedded into an Object form of such source
    code, you may redistribute such embedded portions in such Object form
    without complying with the conditions of Sections 4(a), 4(b) and 4(d) of
    the License.

    In addition, if you combine or link compiled forms of this Software with
    software that is licensed under the GPLv2 ("Combined Software") and if a
    court of competent jurisdiction determines that the patent provision
    (Section 3), the indemnity provision (Section 9) or other Section of the
    License conflicts with the conditions of the GPLv2, you may retroactively
    and prospectively choose to deem waived or otherwise exclude such
    Section(s) of the License, but only in their entirety and only with respect
    to the Combined Software.

_______________________________________________________________________________

7. googletest:

  Copyright 2008, Google Inc.
  All rights reserved.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are
  met:

      * Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
      * Redistributions in binary form must reproduce the above
  copyright notice, this list of conditions and the following disclaimer
  in the documentation and/or other materials provided with the
  distribution.
      * Neither the name of Google Inc. nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

_______________________________________________________________________________

8. uthash:

  Copyright (c) 2005-2018, Troy D. Hanson  http://troydhanson.github.com/uthash/
  All rights reserved.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:

      * Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER
  OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

_______________________________________________________________________________

9. google benchmark

   Copyright 2015 Google Inc. All rights reserved.

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   
_______________________________________________________________________________

9. NVIDIA runtime headers

  Preface
  -------

  The Software License Agreement in Chapter 1 and the Supplement
  in Chapter 2 contain license terms and conditions that govern
  the use of NVIDIA software. By accepting this agreement, you
  agree to comply with all the terms and conditions applicable
  to the product(s) included herein.

  1. License Agreement for NVIDIA Software Development Kits
  ---------------------------------------------------------


  Release Date: July 26, 2018
  ---------------------------


  Important NoticeRead before downloading, installing,
  copying or using the licensed software:
  -------------------------------------------------------

  This license agreement, including exhibits attached
  ("Agreement”) is a legal agreement between you and NVIDIA
  Corporation ("NVIDIA") and governs your use of a NVIDIA
  software development kit (“SDK”).

  Each SDK has its own set of software and materials, but here
  is a description of the types of items that may be included in
  a SDK: source code, header files, APIs, data sets and assets
  (examples include images, textures, models, scenes, videos,
  native API input/output files), binary software, sample code,
  libraries, utility programs, programming code and
  documentation.

  This Agreement can be accepted only by an adult of legal age
  of majority in the country in which the SDK is used.

  If you are entering into this Agreement on behalf of a company
  or other legal entity, you represent that you have the legal
  authority to bind the entity to this Agreement, in which case
  “you” will mean the entity you represent.

  If you don’t have the required age or authority to accept
  this Agreement, or if you don’t accept all the terms and
  conditions of this Agreement, do not download, install or use
  the SDK.

  You agree to use the SDK only for purposes that are permitted
  by (a) this Agreement, and (b) any applicable law, regulation
  or generally accepted practices or guidelines in the relevant
  jurisdictions.


  1.1. License


  1.1.1. License Grant

  Subject to the terms of this Agreement, NVIDIA hereby grants
  you a non-exclusive, non-transferable license, without the
  right to sublicense (except as expressly provided in this
  Agreement) to:

    1. Install and use the SDK,

    2. Modify and create derivative works of sample source code
      delivered in the SDK, and

    3. Distribute those portions of the SDK that are identified
      in this Agreement as distributable, as incorporated in
      object code format into a software application that meets
      the distribution requirements indicated in this Agreement.


  1.1.2. Distribution Requirements

  These are the distribution requirements for you to exercise
  the distribution grant:

    1. Your application must have material additional
      functionality, beyond the included portions of the SDK.

    2. The distributable portions of the SDK shall only be
      accessed by your application.

    3. The following notice shall be included in modifications
      and derivative works of sample source code distributed:
      “This software contains source code provided by NVIDIA
      Corporation.”

    4. Unless a developer tool is identified in this Agreement
      as distributable, it is delivered for your internal use
      only.

    5. The terms under which you distribute your application
      must be consistent with the terms of this Agreement,
      including (without limitation) terms relating to the
      license grant and license restrictions and protection of
      NVIDIA’s intellectual property rights. Additionally, you
      agree that you will protect the privacy, security and
      legal rights of your application users.

    6. You agree to notify NVIDIA in writing of any known or
      suspected distribution or use of the SDK not in compliance
      with the requirements of this Agreement, and to enforce
      the terms of your agreements with respect to distributed
      SDK.


  1.1.3. Authorized Users

  You may allow employees and contractors of your entity or of
  your subsidiary(ies) to access and use the SDK from your
  secure network to perform work on your behalf.

  If you are an academic institution you may allow users
  enrolled or employed by the academic institution to access and
  use the SDK from your secure network.

  You are responsible for the compliance with the terms of this
  Agreement by your authorized users. If you become aware that
  your authorized users didn’t follow the terms of this
  Agreement, you agree to take reasonable steps to resolve the
  non-compliance and prevent new occurrences.


  1.1.4. Pre-Release SDK

  The SDK versions identified as alpha, beta, preview or
  otherwise as pre-release, may not be fully functional, may
  contain errors or design flaws, and may have reduced or
  different security, privacy, accessibility, availability, and
  reliability standards relative to commercial versions of
  NVIDIA software and materials. Use of a pre-release SDK may
  result in unexpected results, loss of data, project delays or
  other unpredictable damage or loss.

  You may use a pre-release SDK at your own risk, understanding
  that pre-release SDKs are not intended for use in production
  or business-critical systems.

  NVIDIA may choose not to make available a commercial version
  of any pre-release SDK. NVIDIA may also choose to abandon
  development and terminate the availability of a pre-release
  SDK at any time without liability.


  1.1.5. Updates

  NVIDIA may, at its option, make available patches, workarounds
  or other updates to this SDK. Unless the updates are provided
  with their separate governing terms, they are deemed part of
  the SDK licensed to you as provided in this Agreement. You
  agree that the form and content of the SDK that NVIDIA
  provides may change without prior notice to you. While NVIDIA
  generally maintains compatibility between versions, NVIDIA may
  in some cases make changes that introduce incompatibilities in
  future versions of the SDK.


  1.1.6. Third Party Licenses

  The SDK may come bundled with, or otherwise include or be
  distributed with, third party software licensed by a NVIDIA
  supplier and/or open source software provided under an open
  source license. Use of third party software is subject to the
  third-party license terms, or in the absence of third party
  terms, the terms of this Agreement. Copyright to third party
  software is held by the copyright holders indicated in the
  third-party software or license.


  1.1.7. Reservation of Rights

  NVIDIA reserves all rights, title, and interest in and to the
  SDK, not expressly granted to you under this Agreement.


  1.2. Limitations

  The following license limitations apply to your use of the
  SDK:

    1. You may not reverse engineer, decompile or disassemble,
      or remove copyright or other proprietary notices from any
      portion of the SDK or copies of the SDK.

    2. Except as expressly provided in this Agreement, you may
      not copy, sell, rent, sublicense, transfer, distribute,
      modify, or create derivative works of any portion of the
      SDK. For clarity, you may not distribute or sublicense the
      SDK as a stand-alone product.

    3. Unless you have an agreement with NVIDIA for this
      purpose, you may not indicate that an application created
      with the SDK is sponsored or endorsed by NVIDIA.

    4. You may not bypass, disable, or circumvent any
      encryption, security, digital rights management or
      authentication mechanism in the SDK.

    5. You may not use the SDK in any manner that would cause it
      to become subject to an open source software license. As
      examples, licenses that require as a condition of use,
      modification, and/or distribution that the SDK be:

        a. Disclosed or distributed in source code form;

        b. Licensed for the purpose of making derivative works;
          or

        c. Redistributable at no charge.

    6. Unless you have an agreement with NVIDIA for this
      purpose, you may not use the SDK with any system or
      application where the use or failure of the system or
      application can reasonably be expected to threaten or
      result in personal injury, death, or catastrophic loss.
      Examples include use in avionics, navigation, military,
      medical, life support or other life critical applications.
      NVIDIA does not design, test or manufacture the SDK for
      these critical uses and NVIDIA shall not be liable to you
      or any third party, in whole or in part, for any claims or
      damages arising from such uses.

    7. You agree to defend, indemnify and hold harmless NVIDIA
      and its affiliates, and their respective employees,
      contractors, agents, officers and directors, from and
      against any and all claims, damages, obligations, losses,
      liabilities, costs or debt, fines, restitutions and
      expenses (including but not limited to attorney’s fees
      and costs incident to establishing the right of
      indemnification) arising out of or related to your use of
      the SDK outside of the scope of this Agreement, or not in
      compliance with its terms.


  1.3. Ownership

    1.  NVIDIA or its licensors hold all rights, title and
      interest in and to the SDK and its modifications and
      derivative works, including their respective intellectual
      property rights, subject to your rights described in this
      section. This SDK may include software and materials from
      NVIDIA’s licensors, and these licensors are intended
      third party beneficiaries that may enforce this Agreement
      with respect to their intellectual property rights.

    2.  You hold all rights, title and interest in and to your
      applications and your derivative works of the sample
      source code delivered in the SDK, including their
      respective intellectual property rights, subject to
      NVIDIA’s rights described in this section.

    3. You may, but don’t have to, provide to NVIDIA
      suggestions, feature requests or other feedback regarding
      the SDK, including possible enhancements or modifications
      to the SDK. For any feedback that you voluntarily provide,
      you hereby grant NVIDIA and its affiliates a perpetual,
      non-exclusive, worldwide, irrevocable license to use,
      reproduce, modify, license, sublicense (through multiple
      tiers of sublicensees), and distribute (through multiple
      tiers of distributors) it without the payment of any
      royalties or fees to you. NVIDIA will use feedback at its
      choice. NVIDIA is constantly looking for ways to improve
      its products, so you may send feedback to NVIDIA through
      the developer portal at https://developer.nvidia.com.


  1.4. No Warranties

  THE SDK IS PROVIDED BY NVIDIA “AS IS” AND “WITH ALL
  FAULTS.” TO THE MAXIMUM EXTENT PERMITTED BY LAW, NVIDIA AND
  ITS AFFILIATES EXPRESSLY DISCLAIM ALL WARRANTIES OF ANY KIND
  OR NATURE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING,
  BUT NOT LIMITED TO, ANY WARRANTIES OF MERCHANTABILITY, FITNESS
  FOR A PARTICULAR PURPOSE, TITLE, NON-INFRINGEMENT, OR THE
  ABSENCE OF ANY DEFECTS THEREIN, WHETHER LATENT OR PATENT. NO
  WARRANTY IS MADE ON THE BASIS OF TRADE USAGE, COURSE OF
  DEALING OR COURSE OF TRADE.


  1.5. Limitation of Liability

  TO THE MAXIMUM EXTENT PERMITTED BY LAW, NVIDIA AND ITS
  AFFILIATES SHALL NOT BE LIABLE FOR ANY SPECIAL, INCIDENTAL,
  PUNITIVE OR CONSEQUENTIAL DAMAGES, OR ANY LOST PROFITS, LOSS
  OF USE, LOSS OF DATA OR LOSS OF GOODWILL, OR THE COSTS OF
  PROCURING SUBSTITUTE PRODUCTS, ARISING OUT OF OR IN CONNECTION
  WITH THIS AGREEMENT OR THE USE OR PERFORMANCE OF THE SDK,
  WHETHER SUCH LIABILITY ARISES FROM ANY CLAIM BASED UPON BREACH
  OF CONTRACT, BREACH OF WARRANTY, TORT (INCLUDING NEGLIGENCE),
  PRODUCT LIABILITY OR ANY OTHER CAUSE OF ACTION OR THEORY OF
  LIABILITY. IN NO EVENT WILL NVIDIA’S AND ITS AFFILIATES
  TOTAL CUMULATIVE LIABILITY UNDER OR ARISING OUT OF THIS
  AGREEMENT EXCEED US$10.00. THE NATURE OF THE LIABILITY OR THE
  NUMBER OF CLAIMS OR SUITS SHALL NOT ENLARGE OR EXTEND THIS
  LIMIT.

  These exclusions and limitations of liability shall apply
  regardless if NVIDIA or its affiliates have been advised of
  the possibility of such damages, and regardless of whether a
  remedy fails its essential purpose. These exclusions and
  limitations of liability form an essential basis of the
  bargain between the parties, and, absent any of these
  exclusions or limitations of liability, the provisions of this
  Agreement, including, without limitation, the economic terms,
  would be substantially different.


  1.6. Termination

    1. This Agreement will continue to apply until terminated by
      either you or NVIDIA as described below.

    2. If you want to terminate this Agreement, you may do so by
      stopping to use the SDK.

    3. NVIDIA may, at any time, terminate this Agreement if:

        a. (i) you fail to comply with any term of this
          Agreement and the non-compliance is not fixed within
          thirty (30) days following notice from NVIDIA (or
          immediately if you violate NVIDIA’s intellectual
          property rights);

        b. (ii) you commence or participate in any legal
          proceeding against NVIDIA with respect to the SDK; or

        c. (iii) NVIDIA decides to no longer provide the SDK in
          a country or, in NVIDIA’s sole discretion, the
          continued use of it is no longer commercially viable.

    4. Upon any termination of this Agreement, you agree to
      promptly discontinue use of the SDK and destroy all copies
      in your possession or control. Your prior distributions in
      accordance with this Agreement are not affected by the
      termination of this Agreement. Upon written request, you
      will certify in writing that you have complied with your
      commitments under this section. Upon any termination of
      this Agreement all provisions survive except for the
      license grant provisions.


  1.7. General

  If you wish to assign this Agreement or your rights and
  obligations, including by merger, consolidation, dissolution
  or operation of law, contact NVIDIA to ask for permission. Any
  attempted assignment not approved by NVIDIA in writing shall
  be void and of no effect. NVIDIA may assign, delegate or
  transfer this Agreement and its rights and obligations, and if
  to a non-affiliate you will be notified.

  You agree to cooperate with NVIDIA and provide reasonably
  requested information to verify your compliance with this
  Agreement.

  This Agreement will be governed in all respects by the laws of
  the United States and of the State of Delaware as those laws
  are applied to contracts entered into and performed entirely
  within Delaware by Delaware residents, without regard to the
  conflicts of laws principles. The United Nations Convention on
  Contracts for the International Sale of Goods is specifically
  disclaimed. You agree to all terms of this Agreement in the
  English language.

  The state or federal courts residing in Santa Clara County,
  California shall have exclusive jurisdiction over any dispute
  or claim arising out of this Agreement. Notwithstanding this,
  you agree that NVIDIA shall still be allowed to apply for
  injunctive remedies or an equivalent type of urgent legal
  relief in any jurisdiction.

  If any court of competent jurisdiction determines that any
  provision of this Agreement is illegal, invalid or
  unenforceable, such provision will be construed as limited to
  the extent necessary to be consistent with and fully
  enforceable under the law and the remaining provisions will
  remain in full force and effect. Unless otherwise specified,
  remedies are cumulative.

  Each party acknowledges and agrees that the other is an
  independent contractor in the performance of this Agreement.

  The SDK has been developed entirely at private expense and is
  “commercial items” consisting of “commercial computer
  software” and “commercial computer software
  documentation” provided with RESTRICTED RIGHTS. Use,
  duplication or disclosure by the U.S. Government or a U.S.
  Government subcontractor is subject to the restrictions in
  this Agreement pursuant to DFARS 227.7202-3(a) or as set forth
  in subparagraphs (c)(1) and (2) of the Commercial Computer
  Software - Restricted Rights clause at FAR 52.227-19, as
  applicable. Contractor/manufacturer is NVIDIA, 2788 San Tomas
  Expressway, Santa Clara, CA 95051.

  The SDK is subject to United States export laws and
  regulations. You agree that you will not ship, transfer or
  export the SDK into any country, or use the SDK in any manner,
  prohibited by the United States Bureau of Industry and
  Security or economic sanctions regulations administered by the
  U.S. Department of Treasury’s Office of Foreign Assets
  Control (OFAC), or any applicable export laws, restrictions or
  regulations. These laws include restrictions on destinations,
  end users and end use. By accepting this Agreement, you
  confirm that you are not a resident or citizen of any country
  currently embargoed by the U.S. and that you are not otherwise
  prohibited from receiving the SDK.

  Any notice delivered by NVIDIA to you under this Agreement
  will be delivered via mail, email or fax. You agree that any
  notices that NVIDIA sends you electronically will satisfy any
  legal communication requirements. Please direct your legal
  notices or other correspondence to NVIDIA Corporation, 2788
  San Tomas Expressway, Santa Clara, California 95051, United
  States of America, Attention: Legal Department.

  This Agreement and any exhibits incorporated into this
  Agreement constitute the entire agreement of the parties with
  respect to the subject matter of this Agreement and supersede
  all prior negotiations or documentation exchanged between the
  parties relating to this SDK license. Any additional and/or
  conflicting terms on documents issued by you are null, void,
  and invalid. Any amendment or waiver under this Agreement
  shall be in writing and signed by representatives of both
  parties.


  2. CUDA Toolkit Supplement to Software License Agreement for
  NVIDIA Software Development Kits
  ------------------------------------------------------------


  Release date: August 16, 2018
  -----------------------------

  The terms in this supplement govern your use of the NVIDIA
  CUDA Toolkit SDK under the terms of your license agreement
  (“Agreement”) as modified by this supplement. Capitalized
  terms used but not defined below have the meaning assigned to
  them in the Agreement.

  This supplement is an exhibit to the Agreement and is
  incorporated as an integral part of the Agreement. In the
  event of conflict between the terms in this supplement and the
  terms in the Agreement, the terms in this supplement govern.


  2.1. License Scope

  The SDK is licensed for you to develop applications only for
  use in systems with NVIDIA GPUs.


  2.2. Distribution

  The portions of the SDK that are distributable under the
  Agreement are listed in Attachment A.


  2.3. Operating Systems

  Those portions of the SDK designed exclusively for use on the
  Linux or FreeBSD operating systems, or other operating systems
  derived from the source code to these operating systems, may
  be copied and redistributed for use in accordance with this
  Agreement, provided that the object code files are not
  modified in any way (except for unzipping of compressed
  files).


  2.4. Audio and Video Encoders and Decoders

  You acknowledge and agree that it is your sole responsibility
  to obtain any additional third-party licenses required to
  make, have made, use, have used, sell, import, and offer for
  sale your products or services that include or incorporate any
  third-party software and content relating to audio and/or
  video encoders and decoders from, including but not limited
  to, Microsoft, Thomson, Fraunhofer IIS, Sisvel S.p.A.,
  MPEG-LA, and Coding Technologies. NVIDIA does not grant to you
  under this Agreement any necessary patent or other rights with
  respect to any audio and/or video encoders and decoders.


  2.5. Licensing

  If the distribution terms in this Agreement are not suitable
  for your organization, or for any questions regarding this
  Agreement, please contact NVIDIA at
  <EMAIL>.

_______________________________________________________________________________

*Other names and brands may be claimed as the property of others.
