{"build": "1", "build_number": 1, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvjitlink-12.8.93-1", "files": ["Library/bin/nvJitLink_120_0.dll"], "fn": "libnvjitlink-12.8.93-1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvjitlink-12.8.93-1", "type": 1}, "md5": "f31efb1ecb6b72a776f8343bcc0cae38", "name": "libnvjitlink", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvjitlink-12.8.93-1.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvJitLink_120_0.dll", "path_type": "hardlink", "sha256": "959d3cb44527ec884db8dc20772520b584dbe7d622d11b0530e6326417078b3e", "sha256_in_prefix": "959d3cb44527ec884db8dc20772520b584dbe7d622d11b0530e6326417078b3e", "size_in_bytes": 77860352}], "paths_version": 1}, "requested_spec": "None", "sha256": "25ae6eb83d4462ca41ca8cc0ca53078707709cc6e2acac194433925b9a888b04", "size": 25645016, "subdir": "win-64", "timestamp": 1740204831000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/libnvjitlink-12.8.93-1.conda", "version": "12.8.93"}