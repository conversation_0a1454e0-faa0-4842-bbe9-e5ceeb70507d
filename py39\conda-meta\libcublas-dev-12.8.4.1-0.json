{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "libcublas 12.8.4.1 0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcublas-dev-12.8.4.1-0", "files": ["Library/include/cublas.h", "Library/include/cublasLt.h", "Library/include/cublasXt.h", "Library/include/cublas_api.h", "Library/include/cublas_v2.h", "Library/include/nvblas.h", "Library/lib/cublas.lib", "Library/lib/cublasLt.lib", "Library/lib/nvblas.lib"], "fn": "libcublas-dev-12.8.4.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcublas-dev-12.8.4.1-0", "type": 1}, "md5": "7398e938b61a9a1e0bd59b866f61f99b", "name": "libcublas-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcublas-dev-12.8.4.1-0.conda", "paths_data": {"paths": [{"_path": "Library/include/cublas.h", "path_type": "hardlink", "sha256": "917979843ed4b042674a0df27b86c06fe1e28b2643c8e24e6709c1e0d85c8b41", "sha256_in_prefix": "917979843ed4b042674a0df27b86c06fe1e28b2643c8e24e6709c1e0d85c8b41", "size_in_bytes": 42137}, {"_path": "Library/include/cublasLt.h", "path_type": "hardlink", "sha256": "58c6326b7f5e98aacb84e4c770291a7e2365b0708cf829d633576b94a930e127", "sha256_in_prefix": "58c6326b7f5e98aacb84e4c770291a7e2365b0708cf829d633576b94a930e127", "size_in_bytes": 104962}, {"_path": "Library/include/cublasXt.h", "path_type": "hardlink", "sha256": "a51299616004afcc0ed44d3394508d4878c66d6e5fd241ba027bc17e4dcc4fb5", "sha256_in_prefix": "a51299616004afcc0ed44d3394508d4878c66d6e5fd241ba027bc17e4dcc4fb5", "size_in_bytes": 38073}, {"_path": "Library/include/cublas_api.h", "path_type": "hardlink", "sha256": "8bf885b96dfc39d32b1741ab70891129fb94a38aea1475bf5b24233becbd38d3", "sha256_in_prefix": "8bf885b96dfc39d32b1741ab70891129fb94a38aea1475bf5b24233becbd38d3", "size_in_bytes": 380198}, {"_path": "Library/include/cublas_v2.h", "path_type": "hardlink", "sha256": "c00d426fbc7aa24c10702492c0df2530fcf45786fc3e78832a1dccb3fba2c4ee", "sha256_in_prefix": "c00d426fbc7aa24c10702492c0df2530fcf45786fc3e78832a1dccb3fba2c4ee", "size_in_bytes": 15938}, {"_path": "Library/include/nvblas.h", "path_type": "hardlink", "sha256": "0bebae64184148e78c709fd283616ec831f0802ca915f1951412a9185a627a0c", "sha256_in_prefix": "0bebae64184148e78c709fd283616ec831f0802ca915f1951412a9185a627a0c", "size_in_bytes": 24165}, {"_path": "Library/lib/cublas.lib", "path_type": "hardlink", "sha256": "eff27870d15e889ef5687a2f18a18c2b41c5f994e7f381f2ec3939b6c06e4e6d", "sha256_in_prefix": "eff27870d15e889ef5687a2f18a18c2b41c5f994e7f381f2ec3939b6c06e4e6d", "size_in_bytes": 154888}, {"_path": "Library/lib/cublasLt.lib", "path_type": "hardlink", "sha256": "16889e0442b10608060780c275201e09b00ca6075c5fc4b11b8826b757d3d098", "sha256_in_prefix": "16889e0442b10608060780c275201e09b00ca6075c5fc4b11b8826b757d3d098", "size_in_bytes": 1642432}, {"_path": "Library/lib/nvblas.lib", "path_type": "hardlink", "sha256": "c597c84e671bfe1c375bc7b072e3f233e048c8e0f076ab696a9e84856eabe42b", "sha256_in_prefix": "c597c84e671bfe1c375bc7b072e3f233e048c8e0f076ab696a9e84856eabe42b", "size_in_bytes": 11250}], "paths_version": 1}, "requested_spec": "None", "sha256": "754d6a0c02f5d432c1ddf54604786d9f839bf22dde201d2f95a6414ecad90c70", "size": 148838, "subdir": "win-64", "timestamp": 1740561834000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/libcublas-dev-12.8.4.1-0.conda", "version": "12.8.4.1"}