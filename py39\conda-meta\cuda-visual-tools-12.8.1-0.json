{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-libraries-dev 12.8.1.*", "cuda-nvml-dev 12.8.90.*", "cuda-nvvp 12.8.93.*", "nsight-compute 2025.1.1.2.*"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-visual-tools-12.8.1-0", "files": [], "fn": "cuda-visual-tools-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-visual-tools-12.8.1-0", "type": 1}, "md5": "c6c95eca027cfe88a79ed54865097399", "name": "cuda-visual-tools", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-visual-tools-12.8.1-0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "b342e59694dd35e6bff48be0de0bb2af66b9821ba86faf3baad982c9df2ef9a9", "size": 17045, "subdir": "win-64", "timestamp": 1741063773000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-visual-tools-12.8.1-0.conda", "version": "12.8.1"}