{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-sanitizer-api-12.8.93-0", "files": ["Library/compute-sanitizer/EnableDebuggerInterface.bat", "Library/compute-sanitizer/InterceptorInjectionTarget.dll", "Library/compute-sanitizer/TreeLauncherTargetInjection.dll", "Library/compute-sanitizer/compute-sanitizer.exe", "Library/compute-sanitizer/docs/ComputeSanitizer/index.html", "Library/compute-sanitizer/docs/CopyrightAndLicenses/index.html", "Library/compute-sanitizer/docs/ReleaseNotes/index.html", "Library/compute-sanitizer/docs/SanitizerApi/table-of-contents.html", "Library/compute-sanitizer/docs/SanitizerApiGuide/index.html", "Library/compute-sanitizer/docs/SanitizerNvtxGuide/index.html", "Library/compute-sanitizer/docs/VERSION", "Library/compute-sanitizer/docs/_images/no-padding.png", "Library/compute-sanitizer/docs/_images/padding.png", "Library/compute-sanitizer/docs/_images/use-after-free.png", "Library/compute-sanitizer/docs/_images/use-before-alloc.png", "Library/compute-sanitizer/docs/_sphinx_design_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "Library/compute-sanitizer/docs/_sphinx_design_static/design-tabs.js", "Library/compute-sanitizer/docs/_static/Logo_and_CUDA.png", "Library/compute-sanitizer/docs/_static/NVIDIA-LogoBlack.svg", "Library/compute-sanitizer/docs/_static/NVIDIA-LogoWhite.svg", "Library/compute-sanitizer/docs/_static/api-styles-dark.css", "Library/compute-sanitizer/docs/_static/api-styles.css", "Library/compute-sanitizer/docs/_static/basic.css", "Library/compute-sanitizer/docs/_static/css/badge_only.css", "Library/compute-sanitizer/docs/_static/css/fonts/Roboto-Slab-Bold.woff", "Library/compute-sanitizer/docs/_static/css/fonts/Roboto-Slab-Bold.woff2", "Library/compute-sanitizer/docs/_static/css/fonts/Roboto-Slab-Regular.woff", "Library/compute-sanitizer/docs/_static/css/fonts/Roboto-Slab-Regular.woff2", "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.eot", "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.svg", "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.ttf", "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.woff", "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.woff2", "Library/compute-sanitizer/docs/_static/css/fonts/lato-bold-italic.woff", "Library/compute-sanitizer/docs/_static/css/fonts/lato-bold-italic.woff2", "Library/compute-sanitizer/docs/_static/css/fonts/lato-bold.woff", "Library/compute-sanitizer/docs/_static/css/fonts/lato-bold.woff2", "Library/compute-sanitizer/docs/_static/css/fonts/lato-normal-italic.woff", "Library/compute-sanitizer/docs/_static/css/fonts/lato-normal-italic.woff2", "Library/compute-sanitizer/docs/_static/css/fonts/lato-normal.woff", "Library/compute-sanitizer/docs/_static/css/fonts/lato-normal.woff2", "Library/compute-sanitizer/docs/_static/css/theme.css", "Library/compute-sanitizer/docs/_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "Library/compute-sanitizer/docs/_static/design-tabs.js", "Library/compute-sanitizer/docs/_static/doctools.js", "Library/compute-sanitizer/docs/_static/documentation_options.js", "Library/compute-sanitizer/docs/_static/favicon.ico", "Library/compute-sanitizer/docs/_static/file.png", "Library/compute-sanitizer/docs/_static/jquery-3.5.1.js", "Library/compute-sanitizer/docs/_static/jquery.js", "Library/compute-sanitizer/docs/_static/js/badge_only.js", "Library/compute-sanitizer/docs/_static/js/html5shiv-printshiv.min.js", "Library/compute-sanitizer/docs/_static/js/html5shiv.min.js", "Library/compute-sanitizer/docs/_static/js/theme.js", "Library/compute-sanitizer/docs/_static/language_data.js", "Library/compute-sanitizer/docs/_static/lunr.min.js", "Library/compute-sanitizer/docs/_static/lunr_search.js", "Library/compute-sanitizer/docs/_static/main_ov_logo_rect.png", "Library/compute-sanitizer/docs/_static/main_ov_logo_square.png", "Library/compute-sanitizer/docs/_static/mermaid-init.js", "Library/compute-sanitizer/docs/_static/minus.png", "Library/compute-sanitizer/docs/_static/omni-style-dark.css", "Library/compute-sanitizer/docs/_static/omni-style.css", "Library/compute-sanitizer/docs/_static/plus.png", "Library/compute-sanitizer/docs/_static/pygments.css", "Library/compute-sanitizer/docs/_static/searchtools.js", "Library/compute-sanitizer/docs/_static/social-media.js", "Library/compute-sanitizer/docs/_static/theme-setter.js", "Library/compute-sanitizer/docs/_static/theme-switcher-general.css", "Library/compute-sanitizer/docs/_static/twemoji.css", "Library/compute-sanitizer/docs/_static/twemoji.js", "Library/compute-sanitizer/docs/_static/underscore-1.13.1.js", "Library/compute-sanitizer/docs/_static/underscore.js", "Library/compute-sanitizer/docs/_static/version.js", "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___b_a_r_r_i_e_r___a_p_i.html", "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___c_a_l_l_b_a_c_k___a_p_i.html", "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___m_e_m_o_r_y___a_p_i.html", "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___p_a_t_c_h_i_n_g___a_p_i.html", "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___r_e_s_u_l_t___a_p_i.html", "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___s_t_r_e_a_m___a_p_i.html", "Library/compute-sanitizer/docs/api/modules.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___batch_memop_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___callback_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___event_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___external_memory_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___graph_exec_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___graph_launch_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___graph_node_launch_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___launch_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___memcpy_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___memset_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_array_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_context_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_functions_lazy_loaded_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_memory_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_mempool_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_module_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_stream_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_virtual_range.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___synchronize_data.html", "Library/compute-sanitizer/docs/api/struct_sanitizer___uvm_data.html", "Library/compute-sanitizer/docs/api/structs.html", "Library/compute-sanitizer/docs/content/notices.html", "Library/compute-sanitizer/docs/genindex.html", "Library/compute-sanitizer/docs/index.html", "Library/compute-sanitizer/docs/objects.inv", "Library/compute-sanitizer/docs/project.json", "Library/compute-sanitizer/docs/search.html", "Library/compute-sanitizer/docs/searchindex.js", "Library/compute-sanitizer/include/generated_cudaD3D10_meta.h", "Library/compute-sanitizer/include/generated_cudaD3D11_meta.h", "Library/compute-sanitizer/include/generated_cudaD3D9_meta.h", "Library/compute-sanitizer/include/generated_cudaGL_meta.h", "Library/compute-sanitizer/include/generated_cuda_d3d10_interop_meta.h", "Library/compute-sanitizer/include/generated_cuda_d3d11_interop_meta.h", "Library/compute-sanitizer/include/generated_cuda_d3d9_interop_meta.h", "Library/compute-sanitizer/include/generated_cuda_gl_interop_meta.h", "Library/compute-sanitizer/include/generated_cuda_meta.h", "Library/compute-sanitizer/include/generated_cuda_profiler_api_meta.h", "Library/compute-sanitizer/include/generated_cuda_runtime_api_meta.h", "Library/compute-sanitizer/include/sanitizer.h", "Library/compute-sanitizer/include/sanitizer_barrier.h", "Library/compute-sanitizer/include/sanitizer_callbacks.h", "Library/compute-sanitizer/include/sanitizer_driver_cbid.h", "Library/compute-sanitizer/include/sanitizer_memory.h", "Library/compute-sanitizer/include/sanitizer_patching.h", "Library/compute-sanitizer/include/sanitizer_result.h", "Library/compute-sanitizer/include/sanitizer_runtime_cbid.h", "Library/compute-sanitizer/include/sanitizer_stream.h", "Library/compute-sanitizer/sanitizer-collection.dll", "Library/compute-sanitizer/sanitizer-public.dll", "Library/compute-sanitizer/sanitizer-public.lib", "Scripts/compute-sanitizer.bat"], "fn": "cuda-sanitizer-api-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-sanitizer-api-12.8.93-0", "type": 1}, "md5": "e02994bd84db696c67ad3fc2be4e74cc", "name": "cuda-sanitizer-api", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-sanitizer-api-12.8.93-0.conda", "paths_data": {"paths": [{"_path": "Library/compute-sanitizer/EnableDebuggerInterface.bat", "path_type": "hardlink", "sha256": "68c5f577c737a6784c2f25a79b37a7d08557a51c8cedc5cc89c20eaee4b5fe06", "sha256_in_prefix": "68c5f577c737a6784c2f25a79b37a7d08557a51c8cedc5cc89c20eaee4b5fe06", "size_in_bytes": 97}, {"_path": "Library/compute-sanitizer/InterceptorInjectionTarget.dll", "path_type": "hardlink", "sha256": "18292b0dc7314f3994d6d7788a451a1cfe39c14df8e0c75e04d2d7737b085a62", "sha256_in_prefix": "18292b0dc7314f3994d6d7788a451a1cfe39c14df8e0c75e04d2d7737b085a62", "size_in_bytes": 1119800}, {"_path": "Library/compute-sanitizer/TreeLauncherTargetInjection.dll", "path_type": "hardlink", "sha256": "194bc104c1c5f6b2775ff3de4c20cc00937852c015cb345421dd19ec43f874f7", "sha256_in_prefix": "194bc104c1c5f6b2775ff3de4c20cc00937852c015cb345421dd19ec43f874f7", "size_in_bytes": 1615936}, {"_path": "Library/compute-sanitizer/compute-sanitizer.exe", "path_type": "hardlink", "sha256": "a245be435b692bc9e9b50b6143d225790fefa6a3fbb3f258dc2347a98ea4bc57", "sha256_in_prefix": "a245be435b692bc9e9b50b6143d225790fefa6a3fbb3f258dc2347a98ea4bc57", "size_in_bytes": 4381768}, {"_path": "Library/compute-sanitizer/docs/ComputeSanitizer/index.html", "path_type": "hardlink", "sha256": "244b467c6ec83f5db82db8e9f4b5478edef90fc2194db99f19da15b9e2152e28", "sha256_in_prefix": "244b467c6ec83f5db82db8e9f4b5478edef90fc2194db99f19da15b9e2152e28", "size_in_bytes": 170753}, {"_path": "Library/compute-sanitizer/docs/CopyrightAndLicenses/index.html", "path_type": "hardlink", "sha256": "0b8415406445e140229173600a315387e5e53b82b72f205e99218176fa26e79f", "sha256_in_prefix": "0b8415406445e140229173600a315387e5e53b82b72f205e99218176fa26e79f", "size_in_bytes": 58778}, {"_path": "Library/compute-sanitizer/docs/ReleaseNotes/index.html", "path_type": "hardlink", "sha256": "9523d773a400cede01d99346f6ce09d678fb00c8228c09cc8021b0fbfc0deb94", "sha256_in_prefix": "9523d773a400cede01d99346f6ce09d678fb00c8228c09cc8021b0fbfc0deb94", "size_in_bytes": 49555}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/table-of-contents.html", "path_type": "hardlink", "sha256": "d2557191a9fc845a9fac40cb4d55b83bdae8c70aa29e604b6bbc30eee310c599", "sha256_in_prefix": "d2557191a9fc845a9fac40cb4d55b83bdae8c70aa29e604b6bbc30eee310c599", "size_in_bytes": 9254}, {"_path": "Library/compute-sanitizer/docs/SanitizerApiGuide/index.html", "path_type": "hardlink", "sha256": "bba888226852d76758021a9332125076d64af681eef65b28e1db9ab8807fe47f", "sha256_in_prefix": "bba888226852d76758021a9332125076d64af681eef65b28e1db9ab8807fe47f", "size_in_bytes": 46064}, {"_path": "Library/compute-sanitizer/docs/SanitizerNvtxGuide/index.html", "path_type": "hardlink", "sha256": "3770d0a9213446a8431db2105a118b33012c7eb01bd8d8a807d0fddcb28caa70", "sha256_in_prefix": "3770d0a9213446a8431db2105a118b33012c7eb01bd8d8a807d0fddcb28caa70", "size_in_bytes": 59042}, {"_path": "Library/compute-sanitizer/docs/VERSION", "path_type": "hardlink", "sha256": "b1fa3303a8f6b23f26c9f8bc2ac1f760a8ccada410c83b8e84c1533aac270037", "sha256_in_prefix": "b1fa3303a8f6b23f26c9f8bc2ac1f760a8ccada410c83b8e84c1533aac270037", "size_in_bytes": 6}, {"_path": "Library/compute-sanitizer/docs/_images/no-padding.png", "path_type": "hardlink", "sha256": "aeb8c47be35e661ac11ff85fa78bcf06a3bb3b234f76485c2c8d52c06a531fd9", "sha256_in_prefix": "aeb8c47be35e661ac11ff85fa78bcf06a3bb3b234f76485c2c8d52c06a531fd9", "size_in_bytes": 4055}, {"_path": "Library/compute-sanitizer/docs/_images/padding.png", "path_type": "hardlink", "sha256": "5160e3867cad691809961c68fa90b83022f3e20d6f2410176f910b85256956c2", "sha256_in_prefix": "5160e3867cad691809961c68fa90b83022f3e20d6f2410176f910b85256956c2", "size_in_bytes": 3753}, {"_path": "Library/compute-sanitizer/docs/_images/use-after-free.png", "path_type": "hardlink", "sha256": "03cbd67be9b6a84bfb132765dc1c622d7055de94aeb55efc804dc9a95f9eade4", "sha256_in_prefix": "03cbd67be9b6a84bfb132765dc1c622d7055de94aeb55efc804dc9a95f9eade4", "size_in_bytes": 73869}, {"_path": "Library/compute-sanitizer/docs/_images/use-before-alloc.png", "path_type": "hardlink", "sha256": "7bf5f067837b00c4963a61bca2c59b62c4af4996ba2dd288fd46d2fb8439e69d", "sha256_in_prefix": "7bf5f067837b00c4963a61bca2c59b62c4af4996ba2dd288fd46d2fb8439e69d", "size_in_bytes": 73157}, {"_path": "Library/compute-sanitizer/docs/_sphinx_design_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "path_type": "hardlink", "sha256": "4e50c43af8d63b9dd7641dc84184ce1a2b3f5ce33ee183e2f476d083d2273a70", "sha256_in_prefix": "4e50c43af8d63b9dd7641dc84184ce1a2b3f5ce33ee183e2f476d083d2273a70", "size_in_bytes": 48387}, {"_path": "Library/compute-sanitizer/docs/_sphinx_design_static/design-tabs.js", "path_type": "hardlink", "sha256": "b01cea2ce60dc68a45a610960b064b46a3410ef4bd0bb851e8cad1f67327850c", "sha256_in_prefix": "b01cea2ce60dc68a45a610960b064b46a3410ef4bd0bb851e8cad1f67327850c", "size_in_bytes": 797}, {"_path": "Library/compute-sanitizer/docs/_static/Logo_and_CUDA.png", "path_type": "hardlink", "sha256": "6e9f6a6532d666e24e3a6eb33611991618f4920e45e3a1f18cf5f02cbfb9a308", "sha256_in_prefix": "6e9f6a6532d666e24e3a6eb33611991618f4920e45e3a1f18cf5f02cbfb9a308", "size_in_bytes": 128602}, {"_path": "Library/compute-sanitizer/docs/_static/NVIDIA-LogoBlack.svg", "path_type": "hardlink", "sha256": "2b3902d0845adc134d656d464864abd3802a3e09bace5fc70a3c146894648c2a", "sha256_in_prefix": "2b3902d0845adc134d656d464864abd3802a3e09bace5fc70a3c146894648c2a", "size_in_bytes": 2494}, {"_path": "Library/compute-sanitizer/docs/_static/NVIDIA-LogoWhite.svg", "path_type": "hardlink", "sha256": "c4e96de70c9f79001312b0366965f0ca9384a542cdc8e095d16bc9e540a8e57d", "sha256_in_prefix": "c4e96de70c9f79001312b0366965f0ca9384a542cdc8e095d16bc9e540a8e57d", "size_in_bytes": 3780}, {"_path": "Library/compute-sanitizer/docs/_static/api-styles-dark.css", "path_type": "hardlink", "sha256": "4d2ed071f82a1455ab42bd3bc300f148b5fd22b53679872d456c5346f1b34dbf", "sha256_in_prefix": "4d2ed071f82a1455ab42bd3bc300f148b5fd22b53679872d456c5346f1b34dbf", "size_in_bytes": 1489}, {"_path": "Library/compute-sanitizer/docs/_static/api-styles.css", "path_type": "hardlink", "sha256": "b6c3df024d50e5d67a467c9ea67f46577f61ad8cb9be6d8acdc0a6fb62fc836f", "sha256_in_prefix": "b6c3df024d50e5d67a467c9ea67f46577f61ad8cb9be6d8acdc0a6fb62fc836f", "size_in_bytes": 2520}, {"_path": "Library/compute-sanitizer/docs/_static/basic.css", "path_type": "hardlink", "sha256": "b008f9f53f8600dd7c85e8d19645681c6596d54e286a6ff255632016de4fe317", "sha256_in_prefix": "b008f9f53f8600dd7c85e8d19645681c6596d54e286a6ff255632016de4fe317", "size_in_bytes": 15597}, {"_path": "Library/compute-sanitizer/docs/_static/css/badge_only.css", "path_type": "hardlink", "sha256": "c4050fa47d8bb6297c79811f663e6cfa32cb6b783b47eaeddd6ba50d5cf1a666", "sha256_in_prefix": "c4050fa47d8bb6297c79811f663e6cfa32cb6b783b47eaeddd6ba50d5cf1a666", "size_in_bytes": 3275}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/Roboto-Slab-Bold.woff", "path_type": "hardlink", "sha256": "9fec87cadbe2413b255f1ec577573a83f1ca2e1c37aa023dbebcd3a7b864636a", "sha256_in_prefix": "9fec87cadbe2413b255f1ec577573a83f1ca2e1c37aa023dbebcd3a7b864636a", "size_in_bytes": 87624}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/Roboto-Slab-Bold.woff2", "path_type": "hardlink", "sha256": "1a0c024dd1a267c52d5575469ffe8570d1e84164de7d393cf3414bafd17d7a0c", "sha256_in_prefix": "1a0c024dd1a267c52d5575469ffe8570d1e84164de7d393cf3414bafd17d7a0c", "size_in_bytes": 67312}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/Roboto-Slab-Regular.woff", "path_type": "hardlink", "sha256": "9f32630e2c0c5135bf1e86e36cb65b3932e4410644235bc2bd995e9c7f6ff117", "sha256_in_prefix": "9f32630e2c0c5135bf1e86e36cb65b3932e4410644235bc2bd995e9c7f6ff117", "size_in_bytes": 86288}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/Roboto-Slab-Regular.woff2", "path_type": "hardlink", "sha256": "874e42222856d7af03b3f438d21d923a4280d47fe67c48510e2174a1579795ef", "sha256_in_prefix": "874e42222856d7af03b3f438d21d923a4280d47fe67c48510e2174a1579795ef", "size_in_bytes": 66444}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.eot", "path_type": "hardlink", "sha256": "7bfcab6db99d5cfbf1705ca0536ddc78585432cc5fa41bbd7ad0f009033b2979", "sha256_in_prefix": "7bfcab6db99d5cfbf1705ca0536ddc78585432cc5fa41bbd7ad0f009033b2979", "size_in_bytes": 165742}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.svg", "path_type": "hardlink", "sha256": "ad6157926c1622ba4e1d03d478f1541368524bfc46f51e42fe0d945f7ef323e4", "sha256_in_prefix": "ad6157926c1622ba4e1d03d478f1541368524bfc46f51e42fe0d945f7ef323e4", "size_in_bytes": 444379}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.ttf", "path_type": "hardlink", "sha256": "aa58f33f239a0fb02f5c7a6c45c043d7a9ac9a093335806694ecd6d4edc0d6a8", "sha256_in_prefix": "aa58f33f239a0fb02f5c7a6c45c043d7a9ac9a093335806694ecd6d4edc0d6a8", "size_in_bytes": 165548}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.woff", "path_type": "hardlink", "sha256": "ba0c59deb5450f5cb41b3f93609ee2d0d995415877ddfa223e8a8a7533474f07", "sha256_in_prefix": "ba0c59deb5450f5cb41b3f93609ee2d0d995415877ddfa223e8a8a7533474f07", "size_in_bytes": 98024}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/fontawesome-webfont.woff2", "path_type": "hardlink", "sha256": "2adefcbc041e7d18fcf2d417879dc5a09997aa64d675b7a3c4b6ce33da13f3fe", "sha256_in_prefix": "2adefcbc041e7d18fcf2d417879dc5a09997aa64d675b7a3c4b6ce33da13f3fe", "size_in_bytes": 77160}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/lato-bold-italic.woff", "path_type": "hardlink", "sha256": "980c8592e5488df256192c999e92db8fd302db8cd8909b7fa266a684e37e45f8", "sha256_in_prefix": "980c8592e5488df256192c999e92db8fd302db8cd8909b7fa266a684e37e45f8", "size_in_bytes": 323344}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/lato-bold-italic.woff2", "path_type": "hardlink", "sha256": "c0916a33340d063f7b05679e08031e729d1888444706f04804705da5966d895d", "sha256_in_prefix": "c0916a33340d063f7b05679e08031e729d1888444706f04804705da5966d895d", "size_in_bytes": 193308}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/lato-bold.woff", "path_type": "hardlink", "sha256": "0e56b17d142eb366c8007031d14e34da48c70b4a9d9a0ca492e696a7bae45e1e", "sha256_in_prefix": "0e56b17d142eb366c8007031d14e34da48c70b4a9d9a0ca492e696a7bae45e1e", "size_in_bytes": 309728}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/lato-bold.woff2", "path_type": "hardlink", "sha256": "ae88fc0d7a961832f809527d30bd3983a6866d42f66a56ade23f543681594db6", "sha256_in_prefix": "ae88fc0d7a961832f809527d30bd3983a6866d42f66a56ade23f543681594db6", "size_in_bytes": 184912}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/lato-normal-italic.woff", "path_type": "hardlink", "sha256": "26318a1467a5e5caf10b04cfa942d079632560cd7a29cec565fd1dc9f7ec5081", "sha256_in_prefix": "26318a1467a5e5caf10b04cfa942d079632560cd7a29cec565fd1dc9f7ec5081", "size_in_bytes": 328412}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/lato-normal-italic.woff2", "path_type": "hardlink", "sha256": "4465765f2f6eddcdad34ffd7cab559e56bc0e75e45e192f85e9562b0771481dc", "sha256_in_prefix": "4465765f2f6eddcdad34ffd7cab559e56bc0e75e45e192f85e9562b0771481dc", "size_in_bytes": 195704}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/lato-normal.woff", "path_type": "hardlink", "sha256": "5b9025dda4d7688e3311b0c17eddc501133b807def33effaef6593843cf5416e", "sha256_in_prefix": "5b9025dda4d7688e3311b0c17eddc501133b807def33effaef6593843cf5416e", "size_in_bytes": 309192}, {"_path": "Library/compute-sanitizer/docs/_static/css/fonts/lato-normal.woff2", "path_type": "hardlink", "sha256": "983b0caf336e8542214fc17019a4fc5e0360864b92806ca14d55c1fc1c2c5a0f", "sha256_in_prefix": "983b0caf336e8542214fc17019a4fc5e0360864b92806ca14d55c1fc1c2c5a0f", "size_in_bytes": 182708}, {"_path": "Library/compute-sanitizer/docs/_static/css/theme.css", "path_type": "hardlink", "sha256": "af78284d3c7a8ab0917208ddc2a71854d4b552802104a7146f8705dca8dd88d1", "sha256_in_prefix": "af78284d3c7a8ab0917208ddc2a71854d4b552802104a7146f8705dca8dd88d1", "size_in_bytes": 129674}, {"_path": "Library/compute-sanitizer/docs/_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "path_type": "hardlink", "sha256": "4e50c43af8d63b9dd7641dc84184ce1a2b3f5ce33ee183e2f476d083d2273a70", "sha256_in_prefix": "4e50c43af8d63b9dd7641dc84184ce1a2b3f5ce33ee183e2f476d083d2273a70", "size_in_bytes": 48387}, {"_path": "Library/compute-sanitizer/docs/_static/design-tabs.js", "path_type": "hardlink", "sha256": "b01cea2ce60dc68a45a610960b064b46a3410ef4bd0bb851e8cad1f67327850c", "sha256_in_prefix": "b01cea2ce60dc68a45a610960b064b46a3410ef4bd0bb851e8cad1f67327850c", "size_in_bytes": 797}, {"_path": "Library/compute-sanitizer/docs/_static/doctools.js", "path_type": "hardlink", "sha256": "b5cad4208b5895e6182a3d6ba2a28c38ba4c3ed7ddff4635839aa430eee59614", "sha256_in_prefix": "b5cad4208b5895e6182a3d6ba2a28c38ba4c3ed7ddff4635839aa430eee59614", "size_in_bytes": 10766}, {"_path": "Library/compute-sanitizer/docs/_static/documentation_options.js", "path_type": "hardlink", "sha256": "89bc26d84bec4f95f0679f0af6edfaac400bcdcf23c6b085db386fc912084036", "sha256_in_prefix": "89bc26d84bec4f95f0679f0af6edfaac400bcdcf23c6b085db386fc912084036", "size_in_bytes": 435}, {"_path": "Library/compute-sanitizer/docs/_static/favicon.ico", "path_type": "hardlink", "sha256": "bedcb14e0470ff8abe26405a630e9604ac4fa581b385a7cfb00420c22f541b6e", "sha256_in_prefix": "bedcb14e0470ff8abe26405a630e9604ac4fa581b385a7cfb00420c22f541b6e", "size_in_bytes": 25214}, {"_path": "Library/compute-sanitizer/docs/_static/file.png", "path_type": "hardlink", "sha256": "5c4bc9a16aebf38c4b950f59b8e501ca36495328cb9eb622218bce9064a35e3e", "sha256_in_prefix": "5c4bc9a16aebf38c4b950f59b8e501ca36495328cb9eb622218bce9064a35e3e", "size_in_bytes": 286}, {"_path": "Library/compute-sanitizer/docs/_static/jquery-3.5.1.js", "path_type": "hardlink", "sha256": "416a3b2c3bf16d64f6b5b6d0f7b079df2267614dd6847fc2f3271b4409233c37", "sha256_in_prefix": "416a3b2c3bf16d64f6b5b6d0f7b079df2267614dd6847fc2f3271b4409233c37", "size_in_bytes": 287630}, {"_path": "Library/compute-sanitizer/docs/_static/jquery.js", "path_type": "hardlink", "sha256": "f7f6a5894f1d19ddad6fa392b2ece2c5e578cbf7da4ea805b6885eb6985b6e3d", "sha256_in_prefix": "f7f6a5894f1d19ddad6fa392b2ece2c5e578cbf7da4ea805b6885eb6985b6e3d", "size_in_bytes": 89476}, {"_path": "Library/compute-sanitizer/docs/_static/js/badge_only.js", "path_type": "hardlink", "sha256": "f0a4808d04c4d55378751ac096a8376b64b1a704c82584b0ee590212cf413013", "sha256_in_prefix": "f0a4808d04c4d55378751ac096a8376b64b1a704c82584b0ee590212cf413013", "size_in_bytes": 934}, {"_path": "Library/compute-sanitizer/docs/_static/js/html5shiv-printshiv.min.js", "path_type": "hardlink", "sha256": "b42a7e949a6e21d66b30fbbb4a22deafd9e0ccabc04f0fa2907fc6252fdf165f", "sha256_in_prefix": "b42a7e949a6e21d66b30fbbb4a22deafd9e0ccabc04f0fa2907fc6252fdf165f", "size_in_bytes": 4370}, {"_path": "Library/compute-sanitizer/docs/_static/js/html5shiv.min.js", "path_type": "hardlink", "sha256": "f6e0283561ddb33b140e14977ffad57163aa28f7e2e7ff15e51e1475b6657b60", "sha256_in_prefix": "f6e0283561ddb33b140e14977ffad57163aa28f7e2e7ff15e51e1475b6657b60", "size_in_bytes": 2734}, {"_path": "Library/compute-sanitizer/docs/_static/js/theme.js", "path_type": "hardlink", "sha256": "536ad2d746e944c5570cc15badaeccc3c0582a1b66e45511fe4edce32b6da510", "sha256_in_prefix": "536ad2d746e944c5570cc15badaeccc3c0582a1b66e45511fe4edce32b6da510", "size_in_bytes": 5023}, {"_path": "Library/compute-sanitizer/docs/_static/language_data.js", "path_type": "hardlink", "sha256": "7aa27b3410d57dcbc7b0e600fb4a497e536627c207f630b09801e172e29deaac", "sha256_in_prefix": "7aa27b3410d57dcbc7b0e600fb4a497e536627c207f630b09801e172e29deaac", "size_in_bytes": 339}, {"_path": "Library/compute-sanitizer/docs/_static/lunr.min.js", "path_type": "hardlink", "sha256": "43640071a5d5c092a876f893fa53833ae295bcb6616b6d9c3866bdb865178131", "sha256_in_prefix": "43640071a5d5c092a876f893fa53833ae295bcb6616b6d9c3866bdb865178131", "size_in_bytes": 29516}, {"_path": "Library/compute-sanitizer/docs/_static/lunr_search.js", "path_type": "hardlink", "sha256": "c2aea957172fc69212ceabfe3114b9f804d91e1179802d06fb944148c2d6cf30", "sha256_in_prefix": "c2aea957172fc69212ceabfe3114b9f804d91e1179802d06fb944148c2d6cf30", "size_in_bytes": 6606}, {"_path": "Library/compute-sanitizer/docs/_static/main_ov_logo_rect.png", "path_type": "hardlink", "sha256": "b15c15d30ac536c3fc4401c8d10abb699dc5b421d68ef6b394dfb4c617933dda", "sha256_in_prefix": "b15c15d30ac536c3fc4401c8d10abb699dc5b421d68ef6b394dfb4c617933dda", "size_in_bytes": 57423}, {"_path": "Library/compute-sanitizer/docs/_static/main_ov_logo_square.png", "path_type": "hardlink", "sha256": "06b7ba35792a640bf5725c179b84750d5ab670a88ccc94a8b4360997c08c19b8", "sha256_in_prefix": "06b7ba35792a640bf5725c179b84750d5ab670a88ccc94a8b4360997c08c19b8", "size_in_bytes": 195331}, {"_path": "Library/compute-sanitizer/docs/_static/mermaid-init.js", "path_type": "hardlink", "sha256": "278bd7f2126b6c73bf8f41cc5e78272934e558e9303089f5cff29d654e2554b6", "sha256_in_prefix": "278bd7f2126b6c73bf8f41cc5e78272934e558e9303089f5cff29d654e2554b6", "size_in_bytes": 1326}, {"_path": "Library/compute-sanitizer/docs/_static/minus.png", "path_type": "hardlink", "sha256": "47e7fc50db3699f1ca41ce9a2ffa202c00c5d1d5180c55f62ba859b1bd6cc008", "sha256_in_prefix": "47e7fc50db3699f1ca41ce9a2ffa202c00c5d1d5180c55f62ba859b1bd6cc008", "size_in_bytes": 90}, {"_path": "Library/compute-sanitizer/docs/_static/omni-style-dark.css", "path_type": "hardlink", "sha256": "e4b8da3e21e73fa5304e35bec66d6c6b126f8724d5d3611a036e9a157159eed1", "sha256_in_prefix": "e4b8da3e21e73fa5304e35bec66d6c6b126f8724d5d3611a036e9a157159eed1", "size_in_bytes": 14107}, {"_path": "Library/compute-sanitizer/docs/_static/omni-style.css", "path_type": "hardlink", "sha256": "978b64e740af59798392b4ae66efdf25d3fb2c751df62a168dcdf1679175fbf9", "sha256_in_prefix": "978b64e740af59798392b4ae66efdf25d3fb2c751df62a168dcdf1679175fbf9", "size_in_bytes": 53494}, {"_path": "Library/compute-sanitizer/docs/_static/plus.png", "path_type": "hardlink", "sha256": "54115199b96a130cba02147c47c0deb43dcc9b9f08b5162bba8642b34980ac63", "sha256_in_prefix": "54115199b96a130cba02147c47c0deb43dcc9b9f08b5162bba8642b34980ac63", "size_in_bytes": 90}, {"_path": "Library/compute-sanitizer/docs/_static/pygments.css", "path_type": "hardlink", "sha256": "b4ce0a3c690b00b06accc101a1afaa38c867bd444c7d3905979874dbb66d069f", "sha256_in_prefix": "b4ce0a3c690b00b06accc101a1afaa38c867bd444c7d3905979874dbb66d069f", "size_in_bytes": 4892}, {"_path": "Library/compute-sanitizer/docs/_static/searchtools.js", "path_type": "hardlink", "sha256": "d6b5ee21edd7b46c029c5111326719dcec5c5f52368704a93b2d6485cb22414c", "sha256_in_prefix": "d6b5ee21edd7b46c029c5111326719dcec5c5f52368704a93b2d6485cb22414c", "size_in_bytes": 16634}, {"_path": "Library/compute-sanitizer/docs/_static/social-media.js", "path_type": "hardlink", "sha256": "3ace3ec53ebde17036ee0879b1111264e0aba6e2315ecde76d2d4ffd9ba3ded5", "sha256_in_prefix": "3ace3ec53ebde17036ee0879b1111264e0aba6e2315ecde76d2d4ffd9ba3ded5", "size_in_bytes": 2681}, {"_path": "Library/compute-sanitizer/docs/_static/theme-setter.js", "path_type": "hardlink", "sha256": "b60c8d1a768192cb7088ee7f5f14a785ffabd4d1d6b2bab9ce8531a96bbed1b5", "sha256_in_prefix": "b60c8d1a768192cb7088ee7f5f14a785ffabd4d1d6b2bab9ce8531a96bbed1b5", "size_in_bytes": 1763}, {"_path": "Library/compute-sanitizer/docs/_static/theme-switcher-general.css", "path_type": "hardlink", "sha256": "5229f831e2d87761e735212bbe699ef9356477eb4b4012c4cbbf13f527071d9b", "sha256_in_prefix": "5229f831e2d87761e735212bbe699ef9356477eb4b4012c4cbbf13f527071d9b", "size_in_bytes": 927}, {"_path": "Library/compute-sanitizer/docs/_static/twemoji.css", "path_type": "hardlink", "sha256": "79f024cf4b763993639a7db8d2951b43e6a61619a50bc4fa92e3d3e20ccf8363", "sha256_in_prefix": "79f024cf4b763993639a7db8d2951b43e6a61619a50bc4fa92e3d3e20ccf8363", "size_in_bytes": 103}, {"_path": "Library/compute-sanitizer/docs/_static/twemoji.js", "path_type": "hardlink", "sha256": "4c98db8e2416f696e9805f90294c56fae45c71f1cae843c25d69c28087ad691b", "sha256_in_prefix": "4c98db8e2416f696e9805f90294c56fae45c71f1cae843c25d69c28087ad691b", "size_in_bytes": 332}, {"_path": "Library/compute-sanitizer/docs/_static/underscore-1.13.1.js", "path_type": "hardlink", "sha256": "cc10f799cd0f6b65f95c4012445497e5ba3cb9f51964a9468940b27bde98b487", "sha256_in_prefix": "cc10f799cd0f6b65f95c4012445497e5ba3cb9f51964a9468940b27bde98b487", "size_in_bytes": 68420}, {"_path": "Library/compute-sanitizer/docs/_static/underscore.js", "path_type": "hardlink", "sha256": "218fb1c1fc72e9af6b866f430be2a67fa376392b4db2f4dbf32772671b6ae55c", "sha256_in_prefix": "218fb1c1fc72e9af6b866f430be2a67fa376392b4db2f4dbf32772671b6ae55c", "size_in_bytes": 19530}, {"_path": "Library/compute-sanitizer/docs/_static/version.js", "path_type": "hardlink", "sha256": "6144e92b94ec60214a75aa94d7bbbb30e31223aa289f6bbc22c5e64975d8876a", "sha256_in_prefix": "6144e92b94ec60214a75aa94d7bbbb30e31223aa289f6bbc22c5e64975d8876a", "size_in_bytes": 7617}, {"_path": "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___b_a_r_r_i_e_r___a_p_i.html", "path_type": "hardlink", "sha256": "36986c0448f40c3f3f6b73fe1a877577123079b8d992212f75ec9d551467d72c", "sha256_in_prefix": "36986c0448f40c3f3f6b73fe1a877577123079b8d992212f75ec9d551467d72c", "size_in_bytes": 10906}, {"_path": "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___c_a_l_l_b_a_c_k___a_p_i.html", "path_type": "hardlink", "sha256": "56af8717307d4cedd01763f9b2467367cd09a02919ecd3f8e754507222bc0f7b", "sha256_in_prefix": "56af8717307d4cedd01763f9b2467367cd09a02919ecd3f8e754507222bc0f7b", "size_in_bytes": 184704}, {"_path": "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___m_e_m_o_r_y___a_p_i.html", "path_type": "hardlink", "sha256": "d026e94d6f798acdd290a555d674e0fa7711455f2a0900f26974c2bba334f19e", "sha256_in_prefix": "d026e94d6f798acdd290a555d674e0fa7711455f2a0900f26974c2bba334f19e", "size_in_bytes": 26605}, {"_path": "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___p_a_t_c_h_i_n_g___a_p_i.html", "path_type": "hardlink", "sha256": "f2e958322221c05c6cf3bf1510ea0b076c933b6e9ba0d8d3b402e84cbb281a24", "sha256_in_prefix": "f2e958322221c05c6cf3bf1510ea0b076c933b6e9ba0d8d3b402e84cbb281a24", "size_in_bytes": 210022}, {"_path": "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___r_e_s_u_l_t___a_p_i.html", "path_type": "hardlink", "sha256": "8f05453dab0d6c45f095c2b49f47294e73448b21cef5c8757e61925d19e78683", "sha256_in_prefix": "8f05453dab0d6c45f095c2b49f47294e73448b21cef5c8757e61925d19e78683", "size_in_bytes": 27730}, {"_path": "Library/compute-sanitizer/docs/api/group___s_a_n_i_t_i_z_e_r___s_t_r_e_a_m___a_p_i.html", "path_type": "hardlink", "sha256": "112a5ec0bc6d103b33f6aa9df4c82b2041ec1789e19b9259904ce8ba471af231", "sha256_in_prefix": "112a5ec0bc6d103b33f6aa9df4c82b2041ec1789e19b9259904ce8ba471af231", "size_in_bytes": 17797}, {"_path": "Library/compute-sanitizer/docs/api/modules.html", "path_type": "hardlink", "sha256": "b95cac142f6ce444aff37d5b77dd4afa756b41b1e1a0e76c9546ab0cdde4b9ec", "sha256_in_prefix": "b95cac142f6ce444aff37d5b77dd4afa756b41b1e1a0e76c9546ab0cdde4b9ec", "size_in_bytes": 9396}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___batch_memop_data.html", "path_type": "hardlink", "sha256": "03ca179272ef1eb5743d255cc4667913a8ddbb0ca6661d71d0bf97c6e725c7b1", "sha256_in_prefix": "03ca179272ef1eb5743d255cc4667913a8ddbb0ca6661d71d0bf97c6e725c7b1", "size_in_bytes": 16082}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___callback_data.html", "path_type": "hardlink", "sha256": "ebdf57d8a8790834a5c39a0b1b4bb54b76c7897e03f6b6104fed9830f8cb3cb9", "sha256_in_prefix": "ebdf57d8a8790834a5c39a0b1b4bb54b76c7897e03f6b6104fed9830f8cb3cb9", "size_in_bytes": 18512}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___event_data.html", "path_type": "hardlink", "sha256": "5c939254928458c7a3cdba8888871cf14f836bd4a925100d6e671db02b6d3732", "sha256_in_prefix": "5c939254928458c7a3cdba8888871cf14f836bd4a925100d6e671db02b6d3732", "size_in_bytes": 14614}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___external_memory_data.html", "path_type": "hardlink", "sha256": "8e35f6907143b2f826741932c9fdea5fc19c7bcb2f0fb573c0dc56febe2d1d57", "sha256_in_prefix": "8e35f6907143b2f826741932c9fdea5fc19c7bcb2f0fb573c0dc56febe2d1d57", "size_in_bytes": 15387}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___graph_exec_data.html", "path_type": "hardlink", "sha256": "f9757a2d2b518f86e0c6fdea48537782699d71e974eac5847d6757530610aa56", "sha256_in_prefix": "f9757a2d2b518f86e0c6fdea48537782699d71e974eac5847d6757530610aa56", "size_in_bytes": 15891}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___graph_launch_data.html", "path_type": "hardlink", "sha256": "04c2f075c99fcb5088096e8a64d4a8d2ab76f82cb969ec3c93532cf2a66c8bd8", "sha256_in_prefix": "04c2f075c99fcb5088096e8a64d4a8d2ab76f82cb969ec3c93532cf2a66c8bd8", "size_in_bytes": 15604}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___graph_node_launch_data.html", "path_type": "hardlink", "sha256": "173fa48d98797fcea2411946dc08df1f84800b9e3525a52fecf9c2d4e05afa6b", "sha256_in_prefix": "173fa48d98797fcea2411946dc08df1f84800b9e3525a52fecf9c2d4e05afa6b", "size_in_bytes": 21413}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___launch_data.html", "path_type": "hardlink", "sha256": "bc2c048303d282c137f4ac1c17fa48eefbc0bb8e61c00cf5ae94fdc3421048fc", "sha256_in_prefix": "bc2c048303d282c137f4ac1c17fa48eefbc0bb8e61c00cf5ae94fdc3421048fc", "size_in_bytes": 27816}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___memcpy_data.html", "path_type": "hardlink", "sha256": "2f9d4f8d55471af7f975923dcd854f1f15725af4cbfc5cca52eba91bc9d45032", "sha256_in_prefix": "2f9d4f8d55471af7f975923dcd854f1f15725af4cbfc5cca52eba91bc9d45032", "size_in_bytes": 26088}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___memset_data.html", "path_type": "hardlink", "sha256": "aa0482b52fcc6997514eaf5ee830f8074ec139e036a14947b716518b40fc44ec", "sha256_in_prefix": "aa0482b52fcc6997514eaf5ee830f8074ec139e036a14947b716518b40fc44ec", "size_in_bytes": 18551}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_array_data.html", "path_type": "hardlink", "sha256": "04f48447804c29619fcc010e08e66e47c6b7ad0acc562cf4fa25c2f974291360", "sha256_in_prefix": "04f48447804c29619fcc010e08e66e47c6b7ad0acc562cf4fa25c2f974291360", "size_in_bytes": 15223}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_context_data.html", "path_type": "hardlink", "sha256": "4b27ac21941d5db508b002bc53f1ffcb8dee51f58dde83fb3a70d3747164ef72", "sha256_in_prefix": "4b27ac21941d5db508b002bc53f1ffcb8dee51f58dde83fb3a70d3747164ef72", "size_in_bytes": 13343}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_functions_lazy_loaded_data.html", "path_type": "hardlink", "sha256": "fd69e651f90c0fda9b492d64993bee06f3144355b8f28190c1144f68a063e34d", "sha256_in_prefix": "fd69e651f90c0fda9b492d64993bee06f3144355b8f28190c1144f68a063e34d", "size_in_bytes": 15264}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_memory_data.html", "path_type": "hardlink", "sha256": "543583dd08e40ca61cb94e41ed3ad26f7790d9dd45b509fd145c79852107c6e6", "sha256_in_prefix": "543583dd08e40ca61cb94e41ed3ad26f7790d9dd45b509fd145c79852107c6e6", "size_in_bytes": 21409}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_mempool_data.html", "path_type": "hardlink", "sha256": "9f87beca8908d5a0b7448040877f4c6588580336459657a770f2d676be7ef081", "sha256_in_prefix": "9f87beca8908d5a0b7448040877f4c6588580336459657a770f2d676be7ef081", "size_in_bytes": 14163}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_module_data.html", "path_type": "hardlink", "sha256": "e4dec7dad8ecc75a3d7e088eafe0e2be2dd85e989ad32902a2ddbb08836c31be", "sha256_in_prefix": "e4dec7dad8ecc75a3d7e088eafe0e2be2dd85e989ad32902a2ddbb08836c31be", "size_in_bytes": 15529}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_stream_data.html", "path_type": "hardlink", "sha256": "620bb0390c52bb178e3c3352aae92945f7085c898f4f9e699db4078e780f9e6a", "sha256_in_prefix": "620bb0390c52bb178e3c3352aae92945f7085c898f4f9e699db4078e780f9e6a", "size_in_bytes": 14171}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___resource_virtual_range.html", "path_type": "hardlink", "sha256": "c4d718e6abd67adab2b44a587f2e2e6614ce05adb0d7aee3bc541e4e7131fd71", "sha256_in_prefix": "c4d718e6abd67adab2b44a587f2e2e6614ce05adb0d7aee3bc541e4e7131fd71", "size_in_bytes": 13152}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___synchronize_data.html", "path_type": "hardlink", "sha256": "721f967e482b7a34b6338bd1666a07cfda7d28157ea9cdaa568bba78b7fd371c", "sha256_in_prefix": "721f967e482b7a34b6338bd1666a07cfda7d28157ea9cdaa568bba78b7fd371c", "size_in_bytes": 13989}, {"_path": "Library/compute-sanitizer/docs/api/struct_sanitizer___uvm_data.html", "path_type": "hardlink", "sha256": "c4c1c56b6f84c1650eeca7e6e5d21878b7768c2d755d3fed014bf25041bac01e", "sha256_in_prefix": "c4c1c56b6f84c1650eeca7e6e5d21878b7768c2d755d3fed014bf25041bac01e", "size_in_bytes": 15252}, {"_path": "Library/compute-sanitizer/docs/api/structs.html", "path_type": "hardlink", "sha256": "5da66517efb1bbdffff0bb01003690fb695af4438d09b0cc7390fd0640e2a381", "sha256_in_prefix": "5da66517efb1bbdffff0bb01003690fb695af4438d09b0cc7390fd0640e2a381", "size_in_bytes": 14724}, {"_path": "Library/compute-sanitizer/docs/content/notices.html", "path_type": "hardlink", "sha256": "20580309230ddee6ec9ef278be799407ad98a86b945746adc86042720fb71fcf", "sha256_in_prefix": "20580309230ddee6ec9ef278be799407ad98a86b945746adc86042720fb71fcf", "size_in_bytes": 7449}, {"_path": "Library/compute-sanitizer/docs/genindex.html", "path_type": "hardlink", "sha256": "4ac8a0951e11a55261d1bc2ee912505d7a0247c518afae5b231954f3bb7cff02", "sha256_in_prefix": "4ac8a0951e11a55261d1bc2ee912505d7a0247c518afae5b231954f3bb7cff02", "size_in_bytes": 101775}, {"_path": "Library/compute-sanitizer/docs/index.html", "path_type": "hardlink", "sha256": "3f524bc9f89dfcef8077bf4a9f5d02600e15054ac79d42fb9e661126b38b8802", "sha256_in_prefix": "3f524bc9f89dfcef8077bf4a9f5d02600e15054ac79d42fb9e661126b38b8802", "size_in_bytes": 7945}, {"_path": "Library/compute-sanitizer/docs/objects.inv", "path_type": "hardlink", "sha256": "f8861d13204f1c9d94d22a52bb3863e066168c507db66cf8d2ce6ab224f6e33f", "sha256_in_prefix": "f8861d13204f1c9d94d22a52bb3863e066168c507db66cf8d2ce6ab224f6e33f", "size_in_bytes": 21920}, {"_path": "Library/compute-sanitizer/docs/project.json", "path_type": "hardlink", "sha256": "26143a4c6e7067171715e0d413a05edb734adb22812f46ba22426f0a29e42163", "sha256_in_prefix": "26143a4c6e7067171715e0d413a05edb734adb22812f46ba22426f0a29e42163", "size_in_bytes": 48}, {"_path": "Library/compute-sanitizer/docs/search.html", "path_type": "hardlink", "sha256": "7a25a9ff763fb05a73b650d1d60530daa7fb7ea41c5930acdb7ec598e79fbe35", "sha256_in_prefix": "7a25a9ff763fb05a73b650d1d60530daa7fb7ea41c5930acdb7ec598e79fbe35", "size_in_bytes": 6149}, {"_path": "Library/compute-sanitizer/docs/searchindex.js", "path_type": "hardlink", "sha256": "51a9b3b3d40582599db04d86b806c77d84bfe8f75356f18cf329ebaf6b863c35", "sha256_in_prefix": "51a9b3b3d40582599db04d86b806c77d84bfe8f75356f18cf329ebaf6b863c35", "size_in_bytes": 789546}, {"_path": "Library/compute-sanitizer/include/generated_cudaD3D10_meta.h", "path_type": "hardlink", "sha256": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "sha256_in_prefix": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "size_in_bytes": 4439}, {"_path": "Library/compute-sanitizer/include/generated_cudaD3D11_meta.h", "path_type": "hardlink", "sha256": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "sha256_in_prefix": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "size_in_bytes": 1823}, {"_path": "Library/compute-sanitizer/include/generated_cudaD3D9_meta.h", "path_type": "hardlink", "sha256": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "sha256_in_prefix": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "size_in_bytes": 5379}, {"_path": "Library/compute-sanitizer/include/generated_cudaGL_meta.h", "path_type": "hardlink", "sha256": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "sha256_in_prefix": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "size_in_bytes": 3344}, {"_path": "Library/compute-sanitizer/include/generated_cuda_d3d10_interop_meta.h", "path_type": "hardlink", "sha256": "f5f27e2aaff104e93316f7c11f158a65bd358b982952be13e25bde0cb7238ca0", "sha256_in_prefix": "f5f27e2aaff104e93316f7c11f158a65bd358b982952be13e25bde0cb7238ca0", "size_in_bytes": 3366}, {"_path": "Library/compute-sanitizer/include/generated_cuda_d3d11_interop_meta.h", "path_type": "hardlink", "sha256": "36cb61c510245590b0cb862997d405d69714b48ff8bdc85ddf7c9a7aedfad637", "sha256_in_prefix": "36cb61c510245590b0cb862997d405d69714b48ff8bdc85ddf7c9a7aedfad637", "size_in_bytes": 1502}, {"_path": "Library/compute-sanitizer/include/generated_cuda_d3d9_interop_meta.h", "path_type": "hardlink", "sha256": "b57f91a7968819fc04f271af446ee2fc58dcc1624d623cc69fa0262e4a9c117f", "sha256_in_prefix": "b57f91a7968819fc04f271af446ee2fc58dcc1624d623cc69fa0262e4a9c117f", "size_in_bytes": 4180}, {"_path": "Library/compute-sanitizer/include/generated_cuda_gl_interop_meta.h", "path_type": "hardlink", "sha256": "e78afc412e339da36d5e3fb66250f9a0c97eef86eee10fa3dffbbc7a1cd3ee4d", "sha256_in_prefix": "e78afc412e339da36d5e3fb66250f9a0c97eef86eee10fa3dffbbc7a1cd3ee4d", "size_in_bytes": 2494}, {"_path": "Library/compute-sanitizer/include/generated_cuda_meta.h", "path_type": "hardlink", "sha256": "d7134de07236f5fb6dbae219b26c8c1f8d37873dc3ba9115580c13460ca644d0", "sha256_in_prefix": "d7134de07236f5fb6dbae219b26c8c1f8d37873dc3ba9115580c13460ca644d0", "size_in_bytes": 92757}, {"_path": "Library/compute-sanitizer/include/generated_cuda_profiler_api_meta.h", "path_type": "hardlink", "sha256": "9fc1cd434c21fc94b3a710dfbcd5c2c67aa82982c83b86e017f95e4cccaa2645", "sha256_in_prefix": "9fc1cd434c21fc94b3a710dfbcd5c2c67aa82982c83b86e017f95e4cccaa2645", "size_in_bytes": 578}, {"_path": "Library/compute-sanitizer/include/generated_cuda_runtime_api_meta.h", "path_type": "hardlink", "sha256": "0bf7beece3b1183bd864c16612396ebdfb857994d3467f7a8fc2e0411ebf5325", "sha256_in_prefix": "0bf7beece3b1183bd864c16612396ebdfb857994d3467f7a8fc2e0411ebf5325", "size_in_bytes": 73336}, {"_path": "Library/compute-sanitizer/include/sanitizer.h", "path_type": "hardlink", "sha256": "c9578a7b0eb637bb2a4c0fa2fca9dd4270851186a3593f92b337644a066bfcf8", "sha256_in_prefix": "c9578a7b0eb637bb2a4c0fa2fca9dd4270851186a3593f92b337644a066bfcf8", "size_in_bytes": 3525}, {"_path": "Library/compute-sanitizer/include/sanitizer_barrier.h", "path_type": "hardlink", "sha256": "ee635dd136d052f68a2c60613de8b3983d030cd35a5209993dff8769073f6572", "sha256_in_prefix": "ee635dd136d052f68a2c60613de8b3983d030cd35a5209993dff8769073f6572", "size_in_bytes": 3807}, {"_path": "Library/compute-sanitizer/include/sanitizer_callbacks.h", "path_type": "hardlink", "sha256": "4888ed80f1d594a39e415a782e7833ad0005030edc4f2b63ecfdaa0a81b194b9", "sha256_in_prefix": "4888ed80f1d594a39e415a782e7833ad0005030edc4f2b63ecfdaa0a81b194b9", "size_in_bytes": 59687}, {"_path": "Library/compute-sanitizer/include/sanitizer_driver_cbid.h", "path_type": "hardlink", "sha256": "8e07b10d172ea80c5f41572dfcc0f4036d2de61f253f47fb3bd0babde2a6e312", "sha256_in_prefix": "8e07b10d172ea80c5f41572dfcc0f4036d2de61f253f47fb3bd0babde2a6e312", "size_in_bytes": 77304}, {"_path": "Library/compute-sanitizer/include/sanitizer_memory.h", "path_type": "hardlink", "sha256": "5708a97923b0b34a616a9d3360a37e8e2e5e20c3e0de34d9e899f6c777922202", "sha256_in_prefix": "5708a97923b0b34a616a9d3360a37e8e2e5e20c3e0de34d9e899f6c777922202", "size_in_bytes": 7509}, {"_path": "Library/compute-sanitizer/include/sanitizer_patching.h", "path_type": "hardlink", "sha256": "868b937a6de08fa5836eb5ba0618f9e49053604f51d4743bd7d4fa6a5629cd7c", "sha256_in_prefix": "868b937a6de08fa5836eb5ba0618f9e49053604f51d4743bd7d4fa6a5629cd7c", "size_in_bytes": 44094}, {"_path": "Library/compute-sanitizer/include/sanitizer_result.h", "path_type": "hardlink", "sha256": "c39cb510258fd9af27f13480e93624b14a31f4c3a843a37ab805ed216479dd44", "sha256_in_prefix": "c39cb510258fd9af27f13480e93624b14a31f4c3a843a37ab805ed216479dd44", "size_in_bytes": 6195}, {"_path": "Library/compute-sanitizer/include/sanitizer_runtime_cbid.h", "path_type": "hardlink", "sha256": "c39d04ce2f6b9a9d96b1b48f15d98b3f716f9b289a454ca392dc7ec98fb3ec64", "sha256_in_prefix": "c39d04ce2f6b9a9d96b1b48f15d98b3f716f9b289a454ca392dc7ec98fb3ec64", "size_in_bytes": 46705}, {"_path": "Library/compute-sanitizer/include/sanitizer_stream.h", "path_type": "hardlink", "sha256": "ad9560b19d5b3c8d368bb6fecd60f5a1549a1091656f1c736711f56c004f11c2", "sha256_in_prefix": "ad9560b19d5b3c8d368bb6fecd60f5a1549a1091656f1c736711f56c004f11c2", "size_in_bytes": 4909}, {"_path": "Library/compute-sanitizer/sanitizer-collection.dll", "path_type": "hardlink", "sha256": "9c37d5f6ef0ab12ba7ce22240bdf1b4a24ae45aed66ba662a84b83558044c66b", "sha256_in_prefix": "9c37d5f6ef0ab12ba7ce22240bdf1b4a24ae45aed66ba662a84b83558044c66b", "size_in_bytes": 7282240}, {"_path": "Library/compute-sanitizer/sanitizer-public.dll", "path_type": "hardlink", "sha256": "8858668fa03d9ac635d9796bb6c339fd8691b55587e0533724e86eb5dda55575", "sha256_in_prefix": "8858668fa03d9ac635d9796bb6c339fd8691b55587e0533724e86eb5dda55575", "size_in_bytes": 1126464}, {"_path": "Library/compute-sanitizer/sanitizer-public.lib", "path_type": "hardlink", "sha256": "f9ba966580f94fd91faff5f95b7275446cfd0c8330e5ae30b5eb3a30fa8d8d43", "sha256_in_prefix": "f9ba966580f94fd91faff5f95b7275446cfd0c8330e5ae30b5eb3a30fa8d8d43", "size_in_bytes": 9692}, {"_path": "Scripts/compute-sanitizer.bat", "path_type": "hardlink", "sha256": "380a8d162fbc6b20b5560b17c05518c3a27d65eda6a8efdf8dde13fcecef6660", "sha256_in_prefix": "380a8d162fbc6b20b5560b17c05518c3a27d65eda6a8efdf8dde13fcecef6660", "size_in_bytes": 73}], "paths_version": 1}, "requested_spec": "None", "sha256": "d11712e2fcb88d9bea9bf9d746c98a51b38990548d3968030e99693a9d84d344", "size": 6806302, "subdir": "win-64", "timestamp": 1740203060000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/cuda-sanitizer-api-12.8.93-0.conda", "version": "12.8.93"}