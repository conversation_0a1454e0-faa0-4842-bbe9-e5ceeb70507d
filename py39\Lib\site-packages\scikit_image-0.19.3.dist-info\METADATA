Metadata-Version: 2.1
Name: scikit-image
Version: 0.19.3
Summary: Image processing in Python
Home-page: https://scikit-image.org
Maintainer: <PERSON>
Maintainer-email: <EMAIL>
License: Modified BSD
Download-URL: https://scikit-image.org/docs/stable/install.html
Project-URL: Bug Tracker, https://github.com/scikit-image/scikit-image/issues
Project-URL: Documentation, https://scikit-image.org/docs/stable/
Project-URL: Source Code, https://github.com/scikit-image/scikit-image
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: C
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Scientific/Engineering
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: Unix
Classifier: Operating System :: MacOS
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE.txt
Requires-Dist: numpy (>=1.17.0)
Requires-Dist: scipy (>=1.4.1)
Requires-Dist: networkx (>=2.2)
Requires-Dist: pillow (!=7.1.0,!=7.1.1,!=8.3.0,>=6.1.0)
Requires-Dist: imageio (>=2.4.1)
Requires-Dist: tifffile (>=2019.7.26)
Requires-Dist: PyWavelets (>=1.1.1)
Requires-Dist: packaging (>=20.0)
Provides-Extra: data
Requires-Dist: pooch (>=1.3.0) ; extra == 'data'
Provides-Extra: docs
Requires-Dist: sphinx (>=1.8) ; extra == 'docs'
Requires-Dist: sphinx-gallery (>=0.10.1) ; extra == 'docs'
Requires-Dist: numpydoc (>=1.0) ; extra == 'docs'
Requires-Dist: sphinx-copybutton ; extra == 'docs'
Requires-Dist: pytest-runner ; extra == 'docs'
Requires-Dist: scikit-learn ; extra == 'docs'
Requires-Dist: matplotlib (>=3.3) ; extra == 'docs'
Requires-Dist: dask[array] (!=2.17.0,>=0.15.0) ; extra == 'docs'
Requires-Dist: cloudpickle (>=0.2.1) ; extra == 'docs'
Requires-Dist: pandas (>=0.23.0) ; extra == 'docs'
Requires-Dist: seaborn (>=0.7.1) ; extra == 'docs'
Requires-Dist: pooch (>=1.3.0) ; extra == 'docs'
Requires-Dist: tifffile (>=2020.5.30) ; extra == 'docs'
Requires-Dist: myst-parser ; extra == 'docs'
Requires-Dist: ipywidgets ; extra == 'docs'
Requires-Dist: plotly (>=4.14.0) ; extra == 'docs'
Requires-Dist: kaleido ; extra == 'docs'
Provides-Extra: optional
Requires-Dist: SimpleITK ; extra == 'optional'
Requires-Dist: astropy (>=3.1.2) ; extra == 'optional'
Requires-Dist: cloudpickle (>=0.2.1) ; extra == 'optional'
Requires-Dist: dask[array] (!=2.17.0,>=1.0.0) ; extra == 'optional'
Requires-Dist: matplotlib (>=3.0.3) ; extra == 'optional'
Requires-Dist: pooch (>=1.3.0) ; extra == 'optional'
Requires-Dist: pyamg ; extra == 'optional'
Requires-Dist: qtpy ; extra == 'optional'
Provides-Extra: test
Requires-Dist: asv ; extra == 'test'
Requires-Dist: codecov ; extra == 'test'
Requires-Dist: flake8 ; extra == 'test'
Requires-Dist: matplotlib (>=3.0.3) ; extra == 'test'
Requires-Dist: pooch (>=1.3.0) ; extra == 'test'
Requires-Dist: pytest (>=5.2.0) ; extra == 'test'
Requires-Dist: pytest-cov (>=2.7.0) ; extra == 'test'
Requires-Dist: pytest-localserver ; extra == 'test'
Requires-Dist: pytest-faulthandler ; extra == 'test'

# scikit-image: Image processing in Python
[![Image.sc forum](https://img.shields.io/badge/dynamic/json.svg?label=forum&url=https%3A%2F%2Fforum.image.sc%2Ftags%2Fscikit-image.json&query=%24.topic_list.tags.0.topic_count&colorB=brightgreen&suffix=%20topics&logo=data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABPklEQVR42m3SyyqFURTA8Y2BER0TDyExZ+aSPIKUlPIITFzKeQWXwhBlQrmFgUzMMFLKZeguBu5y+//17dP3nc5vuPdee6299gohUYYaDGOyyACq4JmQVoFujOMR77hNfOAGM+hBOQqB9TjHD36xhAa04RCuuXeKOvwHVWIKL9jCK2bRiV284QgL8MwEjAneeo9VNOEaBhzALGtoRy02cIcWhE34jj5YxgW+E5Z4iTPkMYpPLCNY3hdOYEfNbKYdmNngZ1jyEzw7h7AIb3fRTQ95OAZ6yQpGYHMMtOTgouktYwxuXsHgWLLl+4x++Kx1FJrjLTagA77bTPvYgw1rRqY56e+w7GNYsqX6JfPwi7aR+Y5SA+BXtKIRfkfJAYgj14tpOF6+I46c4/cAM3UhM3JxyKsxiOIhH0IO6SH/A1Kb1WBeUjbkAAAAAElFTkSuQmCC)](https://forum.image.sc/tags/scikit-image)
[![Stackoverflow](https://img.shields.io/badge/stackoverflow-Ask%20questions-blue.svg)](https://stackoverflow.com/questions/tagged/scikit-image)
[![project chat](https://img.shields.io/badge/zulip-join_chat-brightgreen.svg)](https://skimage.zulipchat.com)
[![codecov.io](https://codecov.io/github/scikit-image/scikit-image/coverage.svg?branch=main)](https://codecov.io/github/scikit-image/scikit-image?branch=main)

- **Website (including documentation):** [https://scikit-image.org/](https://scikit-image.org)
- **User forum:** [https://forum.image.sc/tag/scikit-image](https://forum.image.sc/tag/scikit-image)
- **Developer forum:** [https://discuss.scientific-python.org/c/contributor/skimage](https://discuss.scientific-python.org/c/contributor/skimage)
- **Source:** [https://github.com/scikit-image/scikit-image](https://github.com/scikit-image/scikit-image)
- **Benchmarks:** [https://pandas.pydata.org/speed/scikit-image/](https://pandas.pydata.org/speed/scikit-image/)

## Installation from binaries

- **Debian/Ubuntu:** ``sudo apt-get install python-skimage``
- **OSX:** ``pip install scikit-image``
- **Anaconda:** ``conda install -c conda-forge scikit-image``
- **Windows:** Download [Windows binaries](http://www.lfd.uci.edu/~gohlke/pythonlibs/#scikit-image)

Also see [installing ``scikit-image``](INSTALL.rst).

## Installation from source

Install dependencies using:

```
pip install -r requirements.txt
```

Then, install scikit-image using:

```
$ pip install .
```

If you plan to develop the package, you may run it directly from source:

```
$ pip install -e .  # Do this once to add package to Python path
```

Every time you modify Cython files, also run:

```
$ python setup.py build_ext -i  # Build binary extensions
```

## License (Modified BSD)

Copyright (C) 2011, the scikit-image team
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

 1. Redistributions of source code must retain the above copyright
    notice, this list of conditions and the following disclaimer.
 2. Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in
    the documentation and/or other materials provided with the
    distribution.
 3. Neither the name of skimage nor the names of its contributors may be
    used to endorse or promote products derived from this software without
    specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

## Citation

If you find this project useful, please cite:

> Stéfan van der Walt, Johannes L. Schönberger, Juan Nunez-Iglesias,
> François Boulogne, Joshua D. Warner, Neil Yager, Emmanuelle
> Gouillart, Tony Yu, and the scikit-image contributors.
> *scikit-image: Image processing in Python*. PeerJ 2:e453 (2014)
> https://doi.org/10.7717/peerj.453


