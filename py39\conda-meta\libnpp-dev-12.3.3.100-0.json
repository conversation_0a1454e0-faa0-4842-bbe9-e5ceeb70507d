{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "libnpp 12.3.3.100 0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnpp-dev-12.3.3.100-0", "files": ["Library/include/npp.h", "Library/include/nppcore.h", "Library/include/nppdefs.h", "Library/include/nppi.h", "Library/include/nppi_arithmetic_and_logical_operations.h", "Library/include/nppi_color_conversion.h", "Library/include/nppi_data_exchange_and_initialization.h", "Library/include/nppi_filtering_functions.h", "Library/include/nppi_geometry_transforms.h", "Library/include/nppi_linear_transforms.h", "Library/include/nppi_morphological_operations.h", "Library/include/nppi_statistics_functions.h", "Library/include/nppi_support_functions.h", "Library/include/nppi_threshold_and_compare_operations.h", "Library/include/npps.h", "Library/include/npps_arithmetic_and_logical_operations.h", "Library/include/npps_conversion_functions.h", "Library/include/npps_filtering_functions.h", "Library/include/npps_initialization.h", "Library/include/npps_statistics_functions.h", "Library/include/npps_support_functions.h", "Library/lib/nppc.lib", "Library/lib/nppial.lib", "Library/lib/nppicc.lib", "Library/lib/nppidei.lib", "Library/lib/nppif.lib", "Library/lib/nppig.lib", "Library/lib/nppim.lib", "Library/lib/nppist.lib", "Library/lib/nppisu.lib", "Library/lib/nppitc.lib", "Library/lib/npps.lib"], "fn": "libnpp-dev-12.3.3.100-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnpp-dev-12.3.3.100-0", "type": 1}, "md5": "78756f3800c2f9a11c17474c667aea57", "name": "libnpp-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnpp-dev-12.3.3.100-0.conda", "paths_data": {"paths": [{"_path": "Library/include/npp.h", "path_type": "hardlink", "sha256": "18f449eba050b2c82c9d4962dbe6886f028cde41328a954f024eaff0c5ff857f", "sha256_in_prefix": "18f449eba050b2c82c9d4962dbe6886f028cde41328a954f024eaff0c5ff857f", "size_in_bytes": 3532}, {"_path": "Library/include/nppcore.h", "path_type": "hardlink", "sha256": "b33853bed2298f3ae6bd3e0052c3f9397131eec5e572dcc678ade72537fba595", "sha256_in_prefix": "b33853bed2298f3ae6bd3e0052c3f9397131eec5e572dcc678ade72537fba595", "size_in_bytes": 8579}, {"_path": "Library/include/nppdefs.h", "path_type": "hardlink", "sha256": "04ebc5a23aca635b313557f70685b016ba7f2e701413fe0a349af607d79a43de", "sha256_in_prefix": "04ebc5a23aca635b313557f70685b016ba7f2e701413fe0a349af607d79a43de", "size_in_bytes": 42841}, {"_path": "Library/include/nppi.h", "path_type": "hardlink", "sha256": "c2ee3d052f2ceca1396426c39b8f66ff2f0a92c7f6364feada9878c148b7a8ac", "sha256_in_prefix": "c2ee3d052f2ceca1396426c39b8f66ff2f0a92c7f6364feada9878c148b7a8ac", "size_in_bytes": 4181}, {"_path": "Library/include/nppi_arithmetic_and_logical_operations.h", "path_type": "hardlink", "sha256": "d640873fac8a805b666050d24f8e7ca0da173b0bd2580cea14ce8e165e2b6a1e", "sha256_in_prefix": "d640873fac8a805b666050d24f8e7ca0da173b0bd2580cea14ce8e165e2b6a1e", "size_in_bytes": 1493821}, {"_path": "Library/include/nppi_color_conversion.h", "path_type": "hardlink", "sha256": "3e6ac1fbc24fdd4700f5ae540d6ccf2c8327a90af10d91993f0b8a25cea0527a", "sha256_in_prefix": "3e6ac1fbc24fdd4700f5ae540d6ccf2c8327a90af10d91993f0b8a25cea0527a", "size_in_bytes": 988351}, {"_path": "Library/include/nppi_data_exchange_and_initialization.h", "path_type": "hardlink", "sha256": "f728330f1fa94cd915434d295fa0afc5e1031b6e82558ea0e8eb007300f20829", "sha256_in_prefix": "f728330f1fa94cd915434d295fa0afc5e1031b6e82558ea0e8eb007300f20829", "size_in_bytes": 416065}, {"_path": "Library/include/nppi_filtering_functions.h", "path_type": "hardlink", "sha256": "59496ddfece5e0072c9cd7d709903133ccefedb7e3d8e040d988abccb0c640e6", "sha256_in_prefix": "59496ddfece5e0072c9cd7d709903133ccefedb7e3d8e040d988abccb0c640e6", "size_in_bytes": 1215518}, {"_path": "Library/include/nppi_geometry_transforms.h", "path_type": "hardlink", "sha256": "f0b85eee334f2e66af744cf40fcb723066a40eea6b53921504db10b269ac84bb", "sha256_in_prefix": "f0b85eee334f2e66af744cf40fcb723066a40eea6b53921504db10b269ac84bb", "size_in_bytes": 371721}, {"_path": "Library/include/nppi_linear_transforms.h", "path_type": "hardlink", "sha256": "f6e19ce6c71ee90bdfc00a54843e9480f4ead4b89d88fc1bcbeba8500f34dbc0", "sha256_in_prefix": "f6e19ce6c71ee90bdfc00a54843e9480f4ead4b89d88fc1bcbeba8500f34dbc0", "size_in_bytes": 7562}, {"_path": "Library/include/nppi_morphological_operations.h", "path_type": "hardlink", "sha256": "7a46511328ddbf4c768d21b853c8fdebf9b926b313a6889f9ae97a934debbabc", "sha256_in_prefix": "7a46511328ddbf4c768d21b853c8fdebf9b926b313a6889f9ae97a934debbabc", "size_in_bytes": 148930}, {"_path": "Library/include/nppi_statistics_functions.h", "path_type": "hardlink", "sha256": "413d7afa84893422aab7a97ad465d129b22a981b8a81f0a5c27d8ae535bc9624", "sha256_in_prefix": "413d7afa84893422aab7a97ad465d129b22a981b8a81f0a5c27d8ae535bc9624", "size_in_bytes": 1296601}, {"_path": "Library/include/nppi_support_functions.h", "path_type": "hardlink", "sha256": "91d029657a147eb293063b641a08cb9c7b84c8b889fbaaf34b3f73346f2be813", "sha256_in_prefix": "91d029657a147eb293063b641a08cb9c7b84c8b889fbaaf34b3f73346f2be813", "size_in_bytes": 14047}, {"_path": "Library/include/nppi_threshold_and_compare_operations.h", "path_type": "hardlink", "sha256": "347a22ea8cfc5e081ea530c3673781fe5cd637c517ab4bbe89c5a9a2a2527228", "sha256_in_prefix": "347a22ea8cfc5e081ea530c3673781fe5cd637c517ab4bbe89c5a9a2a2527228", "size_in_bytes": 276505}, {"_path": "Library/include/npps.h", "path_type": "hardlink", "sha256": "4c1a5bc08059fd8e76c29c8890c40d17158c8226fe054fcf0fdfcbb49251ae6e", "sha256_in_prefix": "4c1a5bc08059fd8e76c29c8890c40d17158c8226fe054fcf0fdfcbb49251ae6e", "size_in_bytes": 3772}, {"_path": "Library/include/npps_arithmetic_and_logical_operations.h", "path_type": "hardlink", "sha256": "20b7461e327aae2d554f4aee7a89deb2675c344d128b1a98a051465a025d8d84", "sha256_in_prefix": "20b7461e327aae2d554f4aee7a89deb2675c344d128b1a98a051465a025d8d84", "size_in_bytes": 396008}, {"_path": "Library/include/npps_conversion_functions.h", "path_type": "hardlink", "sha256": "3795d3c4ef61a9b8b22e39c8ef192c02beeba6e2743ece1e67df48f32b0e1fd1", "sha256_in_prefix": "3795d3c4ef61a9b8b22e39c8ef192c02beeba6e2743ece1e67df48f32b0e1fd1", "size_in_bytes": 102614}, {"_path": "Library/include/npps_filtering_functions.h", "path_type": "hardlink", "sha256": "df77897ee1b7547c59af6b77ab78782eb25432ca416687f42b2cdf420a21afd9", "sha256_in_prefix": "df77897ee1b7547c59af6b77ab78782eb25432ca416687f42b2cdf420a21afd9", "size_in_bytes": 5040}, {"_path": "Library/include/npps_initialization.h", "path_type": "hardlink", "sha256": "1a048e227dad308ef3d8ad207950a9d3bf463263f7239586df14026cecdfeb73", "sha256_in_prefix": "1a048e227dad308ef3d8ad207950a9d3bf463263f7239586df14026cecdfeb73", "size_in_bytes": 30969}, {"_path": "Library/include/npps_statistics_functions.h", "path_type": "hardlink", "sha256": "c4a5dcafe7c5e222fd906e48e8c97dee20d396efa9ef80b141cfb0d8b786db08", "sha256_in_prefix": "c4a5dcafe7c5e222fd906e48e8c97dee20d396efa9ef80b141cfb0d8b786db08", "size_in_bytes": 441809}, {"_path": "Library/include/npps_support_functions.h", "path_type": "hardlink", "sha256": "acd2a0b5c558de13289803048b63ab99c15805f592069e8dad77047ddb85aaa3", "sha256_in_prefix": "acd2a0b5c558de13289803048b63ab99c15805f592069e8dad77047ddb85aaa3", "size_in_bytes": 8265}, {"_path": "Library/lib/nppc.lib", "path_type": "hardlink", "sha256": "8fba44427a0f964acb1f80aec3e65bc90200706430e2c2eed067c1069daa2e7a", "sha256_in_prefix": "8fba44427a0f964acb1f80aec3e65bc90200706430e2c2eed067c1069daa2e7a", "size_in_bytes": 4710}, {"_path": "Library/lib/nppial.lib", "path_type": "hardlink", "sha256": "bfd2d510ff373cee7459decb83ac109ed73b89b09e29f640a1fbc3bb0ccf1f78", "sha256_in_prefix": "bfd2d510ff373cee7459decb83ac109ed73b89b09e29f640a1fbc3bb0ccf1f78", "size_in_bytes": 579496}, {"_path": "Library/lib/nppicc.lib", "path_type": "hardlink", "sha256": "12ff60a0c1add8124fde9758da4354d5f749bd8bc5e7a6fa222a0a9ad8a9d152", "sha256_in_prefix": "12ff60a0c1add8124fde9758da4354d5f749bd8bc5e7a6fa222a0a9ad8a9d152", "size_in_bytes": 297082}, {"_path": "Library/lib/nppidei.lib", "path_type": "hardlink", "sha256": "dc13d734b749ecc2d181e5f066648c4ed858601822082673ccae8c557bc51f9e", "sha256_in_prefix": "dc13d734b749ecc2d181e5f066648c4ed858601822082673ccae8c557bc51f9e", "size_in_bytes": 392410}, {"_path": "Library/lib/nppif.lib", "path_type": "hardlink", "sha256": "aae8e7872a8fbf9ad2813cf13cbee8502e6359bb54d4ae7a47693c2708cd2c1a", "sha256_in_prefix": "aae8e7872a8fbf9ad2813cf13cbee8502e6359bb54d4ae7a47693c2708cd2c1a", "size_in_bytes": 645738}, {"_path": "Library/lib/nppig.lib", "path_type": "hardlink", "sha256": "c8faf5183ec85a25593eecfc10bd6f0746c9008b30353097a78fcb9b77a14719", "sha256_in_prefix": "c8faf5183ec85a25593eecfc10bd6f0746c9008b30353097a78fcb9b77a14719", "size_in_bytes": 207506}, {"_path": "Library/lib/nppim.lib", "path_type": "hardlink", "sha256": "60848a7e4dd4e556032dc8fbd9204c72f7a20797341884d7d05dfeb187d14e00", "sha256_in_prefix": "60848a7e4dd4e556032dc8fbd9204c72f7a20797341884d7d05dfeb187d14e00", "size_in_bytes": 88892}, {"_path": "Library/lib/nppist.lib", "path_type": "hardlink", "sha256": "c7b1e5f68f645400d72117cdd686111e38a7823fa74549183b2c05ad5fdbf793", "sha256_in_prefix": "c7b1e5f68f645400d72117cdd686111e38a7823fa74549183b2c05ad5fdbf793", "size_in_bytes": 1058274}, {"_path": "Library/lib/nppisu.lib", "path_type": "hardlink", "sha256": "9fb2b7a8f8ce9801d156bd0e0e57a301a0dd634e0c85b650e8171936bbae2784", "sha256_in_prefix": "9fb2b7a8f8ce9801d156bd0e0e57a301a0dd634e0c85b650e8171936bbae2784", "size_in_bytes": 8416}, {"_path": "Library/lib/nppitc.lib", "path_type": "hardlink", "sha256": "616cf33f8a1e35144fa0b5f7d08d0efd8e3104adb9d6674dae18d2cc8cf15a7e", "sha256_in_prefix": "616cf33f8a1e35144fa0b5f7d08d0efd8e3104adb9d6674dae18d2cc8cf15a7e", "size_in_bytes": 126246}, {"_path": "Library/lib/npps.lib", "path_type": "hardlink", "sha256": "6877f967c5f9829cff94bba6a34d7997b5ea4befeeb97907d788c6bd6c15e320", "sha256_in_prefix": "6877f967c5f9829cff94bba6a34d7997b5ea4befeeb97907d788c6bd6c15e320", "size_in_bytes": 500704}], "paths_version": 1}, "requested_spec": "None", "sha256": "9251a06d96c7ea1ea91f0d1b6635fe3e91cb7e87e5f8237f074efd3fa6dba8ff", "size": 481001, "subdir": "win-64", "timestamp": 1739451863000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.8.1/win-64/libnpp-dev-12.3.3.100-0.conda", "version": "12.3.3.100"}